using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Crudible.Services;
using Crudible.Models;

namespace Crudible.Pages;

public class IndexModel : PageModel
{
    private readonly ILogger<IndexModel> _logger;
    private readonly ConfigurationService _configurationService;
    private readonly DatabaseService _databaseService;

    public IndexModel(
        ILogger<IndexModel> logger,
        ConfigurationService configurationService,
        DatabaseService databaseService)
    {
        _logger = logger;
        _configurationService = configurationService;
        _databaseService = databaseService;
    }

    public int TableCount { get; set; }
    public int RecordCount { get; set; }
    public int AuditCount { get; set; }
    public int ExportCount { get; set; }
    public List<AuditEntry> RecentActivity { get; set; } = new();

    public async Task OnGetAsync()
    {
        try
        {
            await LoadDashboardDataAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading dashboard data");
            // Set default values
            TableCount = 0;
            RecordCount = 0;
            AuditCount = 0;
            ExportCount = 0;
        }
    }

    private async Task LoadDashboardDataAsync()
    {
        var config = await _configurationService.LoadConfigurationAsync();

        // For now, use sample data
        // In a real implementation, these would query the actual database
        TableCount = 5; // Categories, Products, Customers, Orders, OrderItems
        RecordCount = 1250; // Total records across all tables
        AuditCount = 342; // Total audit entries
        ExportCount = 28; // Total exports generated

        // Load recent activity (sample data)
        RecentActivity = new List<AuditEntry>
        {
            new AuditEntry
            {
                TableName = "Products",
                Operation = AuditOperation.Create,
                UserName = "System",
                Timestamp = DateTime.UtcNow.AddMinutes(-5)
            },
            new AuditEntry
            {
                TableName = "Orders",
                Operation = AuditOperation.Update,
                UserName = "Admin",
                Timestamp = DateTime.UtcNow.AddMinutes(-15)
            },
            new AuditEntry
            {
                TableName = "Customers",
                Operation = AuditOperation.Create,
                UserName = "System",
                Timestamp = DateTime.UtcNow.AddMinutes(-30)
            },
            new AuditEntry
            {
                TableName = "Categories",
                Operation = AuditOperation.Update,
                UserName = "Admin",
                Timestamp = DateTime.UtcNow.AddHours(-1)
            },
            new AuditEntry
            {
                TableName = "Products",
                Operation = AuditOperation.Delete,
                UserName = "Admin",
                Timestamp = DateTime.UtcNow.AddHours(-2)
            }
        };
    }
}
