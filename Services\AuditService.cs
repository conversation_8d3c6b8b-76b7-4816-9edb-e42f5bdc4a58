using Microsoft.EntityFrameworkCore;
using Crudible.Data;
using Crudible.Models;
using Crudible.Services.Interfaces;
using Newtonsoft.Json;

namespace Crudible.Services;

/// <summary>
/// Service for managing audit trail functionality
/// </summary>
public class AuditService : IAuditService
{
    private readonly CrudibleDbContext _context;
    private readonly ILogger<AuditService> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public AuditService(
        CrudibleDbContext context,
        ILogger<AuditService> logger,
        IHttpContextAccessor httpContextAccessor)
    {
        _context = context;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
    }

    /// <summary>
    /// Log an audit entry
    /// </summary>
    /// <param name="entry">Audit entry to log</param>
    public async Task LogAuditEntryAsync(AuditEntry entry)
    {
        try
        {
            // Enrich audit entry with context information
            EnrichAuditEntry(entry);

            _context.AuditEntries.Add(entry);
            await _context.SaveChangesAsync();

            _logger.LogDebug("Audit entry logged: {Operation} on {TableName} by {UserName}", 
                entry.Operation, entry.TableName, entry.UserName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to log audit entry for {Operation} on {TableName}", 
                entry.Operation, entry.TableName);
            // Don't throw - audit logging should not break the main operation
        }
    }

    /// <summary>
    /// Get audit entries for a specific table and record
    /// </summary>
    /// <param name="tableName">Table name</param>
    /// <param name="recordId">Record ID</param>
    /// <returns>List of audit entries</returns>
    public async Task<List<AuditEntry>> GetAuditEntriesAsync(string tableName, string recordId)
    {
        try
        {
            return await _context.AuditEntries
                .Where(a => a.TableName == tableName && a.RecordId == recordId)
                .OrderByDescending(a => a.Timestamp)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get audit entries for {TableName} record {RecordId}", 
                tableName, recordId);
            return new List<AuditEntry>();
        }
    }

    /// <summary>
    /// Get audit entries for a date range
    /// </summary>
    /// <param name="startDate">Start date</param>
    /// <param name="endDate">End date</param>
    /// <returns>List of audit entries</returns>
    public async Task<List<AuditEntry>> GetAuditEntriesAsync(DateTime startDate, DateTime endDate)
    {
        try
        {
            return await _context.AuditEntries
                .Where(a => a.Timestamp >= startDate && a.Timestamp <= endDate)
                .OrderByDescending(a => a.Timestamp)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get audit entries for date range {StartDate} to {EndDate}", 
                startDate, endDate);
            return new List<AuditEntry>();
        }
    }

    /// <summary>
    /// Get paged audit entries with filtering
    /// </summary>
    /// <param name="filter">Audit filter criteria</param>
    /// <returns>Paged result of audit entries</returns>
    public async Task<PagedResult<AuditEntry>> GetAuditEntriesAsync(AuditFilter filter)
    {
        try
        {
            var query = _context.AuditEntries.AsQueryable();

            // Apply filters
            if (!string.IsNullOrEmpty(filter.TableName))
            {
                query = query.Where(a => a.TableName.Contains(filter.TableName));
            }

            if (!string.IsNullOrEmpty(filter.RecordId))
            {
                query = query.Where(a => a.RecordId == filter.RecordId);
            }

            if (!string.IsNullOrEmpty(filter.UserId))
            {
                query = query.Where(a => a.UserId == filter.UserId);
            }

            if (filter.Operation.HasValue)
            {
                query = query.Where(a => a.Operation == filter.Operation.Value);
            }

            if (filter.StartDate.HasValue)
            {
                query = query.Where(a => a.Timestamp >= filter.StartDate.Value);
            }

            if (filter.EndDate.HasValue)
            {
                query = query.Where(a => a.Timestamp <= filter.EndDate.Value);
            }

            if (filter.Success.HasValue)
            {
                query = query.Where(a => a.Success == filter.Success.Value);
            }

            // Get total count
            var totalItems = await query.CountAsync();

            // Apply sorting
            query = filter.SortDirection == SortDirection.Ascending
                ? query.OrderBy(a => EF.Property<object>(a, filter.SortField))
                : query.OrderByDescending(a => EF.Property<object>(a, filter.SortField));

            // Apply pagination
            var items = await query
                .Skip((filter.Page - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            return new PagedResult<AuditEntry>
            {
                Items = items,
                TotalItems = totalItems,
                CurrentPage = filter.Page,
                PageSize = filter.PageSize
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get paged audit entries");
            return new PagedResult<AuditEntry>();
        }
    }

    /// <summary>
    /// Get audit summary for reporting
    /// </summary>
    /// <param name="startDate">Start date</param>
    /// <param name="endDate">End date</param>
    /// <returns>Audit summary</returns>
    public async Task<AuditSummary> GetAuditSummaryAsync(DateTime startDate, DateTime endDate)
    {
        try
        {
            var entries = await _context.AuditEntries
                .Where(a => a.Timestamp >= startDate && a.Timestamp <= endDate)
                .ToListAsync();

            var summary = new AuditSummary
            {
                StartDate = startDate,
                EndDate = endDate,
                TotalOperations = entries.Count,
                CreateOperations = entries.Count(e => e.Operation == AuditOperation.Create),
                ReadOperations = entries.Count(e => e.Operation == AuditOperation.Read),
                UpdateOperations = entries.Count(e => e.Operation == AuditOperation.Update),
                DeleteOperations = entries.Count(e => e.Operation == AuditOperation.Delete),
                FailedOperations = entries.Count(e => !e.Success)
            };

            // Get top users
            summary.TopUsers = entries
                .Where(e => !string.IsNullOrEmpty(e.UserName))
                .GroupBy(e => new { e.UserId, e.UserName })
                .Select(g => new UserActivity
                {
                    UserId = g.Key.UserId,
                    UserName = g.Key.UserName,
                    OperationCount = g.Count(),
                    LastActivity = g.Max(e => e.Timestamp)
                })
                .OrderByDescending(u => u.OperationCount)
                .Take(10)
                .ToList();

            return summary;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get audit summary for date range {StartDate} to {EndDate}", 
                startDate, endDate);
            return new AuditSummary { StartDate = startDate, EndDate = endDate };
        }
    }

    /// <summary>
    /// Clean up old audit entries based on retention policy
    /// </summary>
    public async Task CleanupOldEntriesAsync()
    {
        try
        {
            var configService = _httpContextAccessor.HttpContext?.RequestServices
                .GetService<ConfigurationService>();
            
            if (configService == null)
            {
                _logger.LogWarning("Configuration service not available for audit cleanup");
                return;
            }

            var config = await configService.LoadConfigurationAsync();
            
            if (config.Audit.RetentionDays <= 0)
            {
                _logger.LogDebug("Audit retention is unlimited, skipping cleanup");
                return;
            }

            var cutoffDate = DateTime.UtcNow.AddDays(-config.Audit.RetentionDays);
            
            var oldEntries = await _context.AuditEntries
                .Where(a => a.Timestamp < cutoffDate)
                .ToListAsync();

            if (oldEntries.Any())
            {
                _context.AuditEntries.RemoveRange(oldEntries);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Cleaned up {Count} old audit entries older than {CutoffDate}", 
                    oldEntries.Count, cutoffDate);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to cleanup old audit entries");
        }
    }

    /// <summary>
    /// Log CRUD operation
    /// </summary>
    /// <param name="tableName">Table name</param>
    /// <param name="recordId">Record ID</param>
    /// <param name="operation">CRUD operation</param>
    /// <param name="oldValues">Old values (for updates/deletes)</param>
    /// <param name="newValues">New values (for creates/updates)</param>
    /// <param name="success">Whether operation was successful</param>
    /// <param name="errorMessage">Error message if operation failed</param>
    public async Task LogCrudOperationAsync(
        string tableName,
        string recordId,
        AuditOperation operation,
        object? oldValues = null,
        object? newValues = null,
        bool success = true,
        string? errorMessage = null)
    {
        var entry = new AuditEntry
        {
            TableName = tableName,
            RecordId = recordId,
            Operation = operation,
            Success = success,
            ErrorMessage = errorMessage
        };

        if (oldValues != null)
        {
            entry.OldValues = JsonConvert.SerializeObject(oldValues);
        }

        if (newValues != null)
        {
            entry.NewValues = JsonConvert.SerializeObject(newValues);
        }

        // Determine changed fields for updates
        if (operation == AuditOperation.Update && oldValues != null && newValues != null)
        {
            entry.ChangedFields = GetChangedFields(oldValues, newValues);
        }

        await LogAuditEntryAsync(entry);
    }

    private void EnrichAuditEntry(AuditEntry entry)
    {
        var httpContext = _httpContextAccessor.HttpContext;
        
        if (httpContext != null)
        {
            // Get user information
            var user = httpContext.User;
            if (user.Identity?.IsAuthenticated == true)
            {
                entry.UserId = user.FindFirst("sub")?.Value ?? user.FindFirst("id")?.Value;
                entry.UserName = user.Identity.Name ?? user.FindFirst("name")?.Value;
            }
            else
            {
                entry.UserName = "Anonymous";
            }

            // Get request information
            entry.IpAddress = GetClientIpAddress(httpContext);
            entry.UserAgent = httpContext.Request.Headers["User-Agent"].FirstOrDefault();
        }

        // Set timestamp if not already set
        if (entry.Timestamp == default)
        {
            entry.Timestamp = DateTime.UtcNow;
        }
    }

    private string? GetClientIpAddress(HttpContext httpContext)
    {
        // Check for forwarded IP first (for load balancers/proxies)
        var forwardedFor = httpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(forwardedFor))
        {
            return forwardedFor.Split(',')[0].Trim();
        }

        var realIp = httpContext.Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(realIp))
        {
            return realIp;
        }

        return httpContext.Connection.RemoteIpAddress?.ToString();
    }

    private string? GetChangedFields(object oldValues, object newValues)
    {
        try
        {
            var oldJson = JsonConvert.SerializeObject(oldValues);
            var newJson = JsonConvert.SerializeObject(newValues);
            
            var oldDict = JsonConvert.DeserializeObject<Dictionary<string, object>>(oldJson);
            var newDict = JsonConvert.DeserializeObject<Dictionary<string, object>>(newJson);
            
            if (oldDict == null || newDict == null)
                return null;

            var changedFields = new List<string>();
            
            foreach (var key in newDict.Keys)
            {
                if (!oldDict.ContainsKey(key) || 
                    !Equals(oldDict[key]?.ToString(), newDict[key]?.ToString()))
                {
                    changedFields.Add(key);
                }
            }

            return changedFields.Any() ? string.Join(",", changedFields) : null;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to determine changed fields");
            return null;
        }
    }
}
