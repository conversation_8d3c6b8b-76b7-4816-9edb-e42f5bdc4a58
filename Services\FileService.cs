using System.Text;

namespace Crudible.Services;

/// <summary>
/// Service for file operations
/// </summary>
public class FileService
{
    private readonly ILogger<FileService> _logger;
    private readonly IWebHostEnvironment _environment;

    public FileService(ILogger<FileService> logger, IWebHostEnvironment environment)
    {
        _logger = logger;
        _environment = environment;
    }

    /// <summary>
    /// Save file to disk
    /// </summary>
    /// <param name="fileName">File name</param>
    /// <param name="content">File content</param>
    /// <param name="directory">Directory path (relative to wwwroot)</param>
    /// <returns>Full file path</returns>
    public async Task<string> SaveFileAsync(string fileName, string content, string directory = "uploads")
    {
        try
        {
            var uploadsPath = Path.Combine(_environment.WebRootPath, directory);
            
            // Create directory if it doesn't exist
            if (!Directory.Exists(uploadsPath))
            {
                Directory.CreateDirectory(uploadsPath);
            }

            var filePath = Path.Combine(uploadsPath, fileName);
            await File.WriteAllTextAsync(filePath, content, Encoding.UTF8);

            _logger.LogInformation("File saved: {FilePath}", filePath);
            return filePath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save file: {FileName}", fileName);
            throw;
        }
    }

    /// <summary>
    /// Save binary file to disk
    /// </summary>
    /// <param name="fileName">File name</param>
    /// <param name="content">File content</param>
    /// <param name="directory">Directory path (relative to wwwroot)</param>
    /// <returns>Full file path</returns>
    public async Task<string> SaveFileAsync(string fileName, byte[] content, string directory = "uploads")
    {
        try
        {
            var uploadsPath = Path.Combine(_environment.WebRootPath, directory);
            
            // Create directory if it doesn't exist
            if (!Directory.Exists(uploadsPath))
            {
                Directory.CreateDirectory(uploadsPath);
            }

            var filePath = Path.Combine(uploadsPath, fileName);
            await File.WriteAllBytesAsync(filePath, content);

            _logger.LogInformation("Binary file saved: {FilePath}", filePath);
            return filePath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save binary file: {FileName}", fileName);
            throw;
        }
    }

    /// <summary>
    /// Read file from disk
    /// </summary>
    /// <param name="filePath">File path</param>
    /// <returns>File content</returns>
    public async Task<string> ReadFileAsync(string filePath)
    {
        try
        {
            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"File not found: {filePath}");
            }

            return await File.ReadAllTextAsync(filePath, Encoding.UTF8);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to read file: {FilePath}", filePath);
            throw;
        }
    }

    /// <summary>
    /// Read binary file from disk
    /// </summary>
    /// <param name="filePath">File path</param>
    /// <returns>File content</returns>
    public async Task<byte[]> ReadBinaryFileAsync(string filePath)
    {
        try
        {
            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"File not found: {filePath}");
            }

            return await File.ReadAllBytesAsync(filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to read binary file: {FilePath}", filePath);
            throw;
        }
    }

    /// <summary>
    /// Delete file from disk
    /// </summary>
    /// <param name="filePath">File path</param>
    public void DeleteFile(string filePath)
    {
        try
        {
            if (File.Exists(filePath))
            {
                File.Delete(filePath);
                _logger.LogInformation("File deleted: {FilePath}", filePath);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete file: {FilePath}", filePath);
            throw;
        }
    }

    /// <summary>
    /// Check if file exists
    /// </summary>
    /// <param name="filePath">File path</param>
    /// <returns>True if file exists</returns>
    public bool FileExists(string filePath)
    {
        return File.Exists(filePath);
    }

    /// <summary>
    /// Get file size
    /// </summary>
    /// <param name="filePath">File path</param>
    /// <returns>File size in bytes</returns>
    public long GetFileSize(string filePath)
    {
        if (!File.Exists(filePath))
        {
            return 0;
        }

        var fileInfo = new FileInfo(filePath);
        return fileInfo.Length;
    }

    /// <summary>
    /// Get file extension
    /// </summary>
    /// <param name="fileName">File name</param>
    /// <returns>File extension</returns>
    public string GetFileExtension(string fileName)
    {
        return Path.GetExtension(fileName);
    }

    /// <summary>
    /// Generate unique file name
    /// </summary>
    /// <param name="originalFileName">Original file name</param>
    /// <returns>Unique file name</returns>
    public string GenerateUniqueFileName(string originalFileName)
    {
        var extension = Path.GetExtension(originalFileName);
        var nameWithoutExtension = Path.GetFileNameWithoutExtension(originalFileName);
        var uniqueId = Guid.NewGuid().ToString("N")[..8];
        
        return $"{nameWithoutExtension}_{uniqueId}{extension}";
    }
}
