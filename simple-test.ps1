Write-Host "=== Crudible Quick Test ===" -ForegroundColor Green

# Test 1: Application running
Write-Host "Testing application..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5000" -TimeoutSec 5
    Write-Host "✓ Application running" -ForegroundColor Green
} catch {
    Write-Host "✗ Application not running" -ForegroundColor Red
    exit 1
}

# Test 2: Status page
Write-Host "Testing status page..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5000/status" -TimeoutSec 5
    Write-Host "✓ Status page works" -ForegroundColor Green
} catch {
    Write-Host "✗ Status page failed" -ForegroundColor Red
}

# Test 3: Config page
Write-Host "Testing config page..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5000/config" -TimeoutSec 5
    Write-Host "✓ Config page works" -ForegroundColor Green
} catch {
    Write-Host "✗ Config page failed" -ForegroundColor Red
}

# Test 4: Tables page
Write-Host "Testing tables page..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5000/tables" -TimeoutSec 5
    Write-Host "✓ Tables page works" -ForegroundColor Green
} catch {
    Write-Host "✗ Tables page failed" -ForegroundColor Red
}

# Test 5: Sample schema exists
Write-Host "Checking sample schema..." -ForegroundColor Yellow
if (Test-Path "wwwroot/sample-schema.xml") {
    Write-Host "✓ Sample schema found" -ForegroundColor Green
} else {
    Write-Host "✗ Sample schema missing" -ForegroundColor Red
}

Write-Host "`n=== Results ===" -ForegroundColor Green
Write-Host "Basic tests completed." -ForegroundColor White
Write-Host "Next: Upload schema at /config, then generate CRUD at /tables" -ForegroundColor Yellow
