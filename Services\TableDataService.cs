using System.Data;
using Microsoft.Data.SqlClient;
using System.Text;
using Crudible.Models;

namespace Crudible.Services;

/// <summary>
/// Service for performing CRUD operations on database tables
/// </summary>
public class TableDataService
{
    private readonly ILogger<TableDataService> _logger;
    private readonly ConfigurationService _configService;

    public TableDataService(ILogger<TableDataService> logger, ConfigurationService configService)
    {
        _logger = logger;
        _configService = configService;
    }

    /// <summary>
    /// Get paginated data from a table
    /// </summary>
    public async Task<(List<dynamic> Data, int TotalCount)> GetTableDataAsync(string tableName, int page = 1, int pageSize = 25, string searchTerm = "")
    {
        try
        {
            var config = await _configService.LoadConfigurationAsync();
            var connectionString = config.Database.ConnectionString;

            if (string.IsNullOrEmpty(connectionString))
            {
                _logger.LogInformation("No database connection configured, returning sample data for table: {TableName}", tableName);
                return GenerateSampleData(tableName, page, pageSize, searchTerm);
            }

            _logger.LogInformation("Getting data for table {TableName}, page {Page}, pageSize {PageSize}, search: {SearchTerm}", 
                tableName, page, pageSize, searchTerm);

            using var connection = new SqlConnection(connectionString);
            await connection.OpenAsync();

            // Build the query with search and pagination
            var whereClause = "";
            var parameters = new List<SqlParameter>();

            if (!string.IsNullOrEmpty(searchTerm))
            {
                // Get table schema to build search across text columns
                var searchColumns = await GetSearchableColumnsAsync(connection, tableName);
                if (searchColumns.Any())
                {
                    var searchConditions = searchColumns.Select((col, index) => 
                        $"CAST([{col}] AS NVARCHAR(MAX)) LIKE @search{index}").ToList();
                    whereClause = $"WHERE {string.Join(" OR ", searchConditions)}";
                    
                    for (int i = 0; i < searchColumns.Count; i++)
                    {
                        parameters.Add(new SqlParameter($"@search{i}", $"%{searchTerm}%"));
                    }
                }
            }

            // Get total count
            var countQuery = $"SELECT COUNT(*) FROM [{tableName}] {whereClause}";
            using var countCommand = new SqlCommand(countQuery, connection);
            countCommand.Parameters.AddRange(parameters.ToArray());
            var totalCount = (int)await countCommand.ExecuteScalarAsync();

            // Get paginated data
            var offset = (page - 1) * pageSize;
            var dataQuery = $@"
                SELECT * FROM [{tableName}] 
                {whereClause}
                ORDER BY (SELECT NULL)
                OFFSET {offset} ROWS 
                FETCH NEXT {pageSize} ROWS ONLY";

            using var dataCommand = new SqlCommand(dataQuery, connection);
            dataCommand.Parameters.AddRange(parameters.ToArray());
            
            var data = new List<dynamic>();
            using var reader = await dataCommand.ExecuteReaderAsync();
            
            while (await reader.ReadAsync())
            {
                var row = new Dictionary<string, object>();
                for (int i = 0; i < reader.FieldCount; i++)
                {
                    var value = reader.IsDBNull(i) ? null : reader.GetValue(i);
                    row[reader.GetName(i)] = value;
                }
                data.Add(row);
            }

            _logger.LogInformation("Retrieved {Count} records from {TableName} (total: {TotalCount})", 
                data.Count, tableName, totalCount);

            return (data.Cast<dynamic>().ToList(), totalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting table data for {TableName}", tableName);
            return (new List<dynamic>(), 0);
        }
    }

    /// <summary>
    /// Get a single record by ID
    /// </summary>
    public async Task<dynamic?> GetRecordByIdAsync(string tableName, string idColumn, object id)
    {
        try
        {
            var config = await _configService.LoadConfigurationAsync();
            var connectionString = config.Database.ConnectionString;

            if (string.IsNullOrEmpty(connectionString))
            {
                _logger.LogInformation("No database connection configured, returning sample record for table: {TableName}, ID: {Id}", tableName, id);
                return GetSampleRecordById(tableName, idColumn, id);
            }

            using var connection = new SqlConnection(connectionString);
            await connection.OpenAsync();

            var query = $"SELECT * FROM [{tableName}] WHERE [{idColumn}] = @id";
            using var command = new SqlCommand(query, connection);
            command.Parameters.AddWithValue("@id", id);

            using var reader = await command.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                var row = new Dictionary<string, object>();
                for (int i = 0; i < reader.FieldCount; i++)
                {
                    var value = reader.IsDBNull(i) ? null : reader.GetValue(i);
                    row[reader.GetName(i)] = value;
                }
                return row;
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting record from {TableName} with ID {Id}", tableName, id);
            return null;
        }
    }

    /// <summary>
    /// Insert a new record
    /// </summary>
    public async Task<bool> InsertRecordAsync(string tableName, Dictionary<string, object> data)
    {
        try
        {
            var config = await _configService.LoadConfigurationAsync();
            var connectionString = config.Database.ConnectionString;

            if (string.IsNullOrEmpty(connectionString))
            {
                _logger.LogInformation("No database connection configured, simulating insert for table: {TableName}", tableName);
                // In demo mode, always return success
                await Task.Delay(100); // Simulate database operation
                return true;
            }

            using var connection = new SqlConnection(connectionString);
            await connection.OpenAsync();

            var columns = string.Join(", ", data.Keys.Select(k => $"[{k}]"));
            var values = string.Join(", ", data.Keys.Select(k => $"@{k}"));
            var query = $"INSERT INTO [{tableName}] ({columns}) VALUES ({values})";

            using var command = new SqlCommand(query, connection);
            foreach (var kvp in data)
            {
                command.Parameters.AddWithValue($"@{kvp.Key}", kvp.Value ?? DBNull.Value);
            }

            var rowsAffected = await command.ExecuteNonQueryAsync();
            return rowsAffected > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error inserting record into {TableName}", tableName);
            return false;
        }
    }

    /// <summary>
    /// Update an existing record
    /// </summary>
    public async Task<bool> UpdateRecordAsync(string tableName, string idColumn, object id, Dictionary<string, object> data)
    {
        try
        {
            var config = await _configService.LoadConfigurationAsync();
            var connectionString = config.Database.ConnectionString;

            if (string.IsNullOrEmpty(connectionString))
            {
                _logger.LogInformation("No database connection configured, simulating update for table: {TableName}, ID: {Id}", tableName, id);
                // In demo mode, always return success
                await Task.Delay(100); // Simulate database operation
                return true;
            }

            using var connection = new SqlConnection(connectionString);
            await connection.OpenAsync();

            var setClause = string.Join(", ", data.Keys.Select(k => $"[{k}] = @{k}"));
            var query = $"UPDATE [{tableName}] SET {setClause} WHERE [{idColumn}] = @id";

            using var command = new SqlCommand(query, connection);
            command.Parameters.AddWithValue("@id", id);
            foreach (var kvp in data)
            {
                command.Parameters.AddWithValue($"@{kvp.Key}", kvp.Value ?? DBNull.Value);
            }

            var rowsAffected = await command.ExecuteNonQueryAsync();
            return rowsAffected > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating record in {TableName} with ID {Id}", tableName, id);
            return false;
        }
    }

    /// <summary>
    /// Delete a record
    /// </summary>
    public async Task<bool> DeleteRecordAsync(string tableName, string idColumn, object id)
    {
        try
        {
            var config = await _configService.LoadConfigurationAsync();
            var connectionString = config.Database.ConnectionString;

            if (string.IsNullOrEmpty(connectionString))
            {
                _logger.LogInformation("No database connection configured, simulating delete for table: {TableName}, ID: {Id}", tableName, id);
                // In demo mode, always return success
                await Task.Delay(100); // Simulate database operation
                return true;
            }

            using var connection = new SqlConnection(connectionString);
            await connection.OpenAsync();

            var query = $"DELETE FROM [{tableName}] WHERE [{idColumn}] = @id";
            using var command = new SqlCommand(query, connection);
            command.Parameters.AddWithValue("@id", id);

            var rowsAffected = await command.ExecuteNonQueryAsync();
            return rowsAffected > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting record from {TableName} with ID {Id}", tableName, id);
            return false;
        }
    }

    private async Task<List<string>> GetSearchableColumnsAsync(SqlConnection connection, string tableName)
    {
        var query = @"
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = @tableName 
            AND DATA_TYPE IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext')";

        using var command = new SqlCommand(query, connection);
        command.Parameters.AddWithValue("@tableName", tableName);

        var columns = new List<string>();
        using var reader = await command.ExecuteReaderAsync();
        while (await reader.ReadAsync())
        {
            columns.Add(reader.GetString("COLUMN_NAME"));
        }

        return columns;
    }

    /// <summary>
    /// Get table schema information
    /// </summary>
    public async Task<List<ColumnInfo>> GetTableSchemaAsync(string tableName)
    {
        try
        {
            var config = await _configService.LoadConfigurationAsync();
            var connectionString = config.Database.ConnectionString;

            if (string.IsNullOrEmpty(connectionString))
            {
                return new List<ColumnInfo>();
            }

            using var connection = new SqlConnection(connectionString);
            await connection.OpenAsync();

            var query = @"
                SELECT 
                    COLUMN_NAME,
                    DATA_TYPE,
                    IS_NULLABLE,
                    CHARACTER_MAXIMUM_LENGTH,
                    NUMERIC_PRECISION,
                    NUMERIC_SCALE,
                    COLUMN_DEFAULT
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = @tableName
                ORDER BY ORDINAL_POSITION";

            using var command = new SqlCommand(query, connection);
            command.Parameters.AddWithValue("@tableName", tableName);

            var columns = new List<ColumnInfo>();
            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                columns.Add(new ColumnInfo
                {
                    Name = reader.GetString("COLUMN_NAME"),
                    DataType = reader.GetString("DATA_TYPE"),
                    IsNullable = reader.GetString("IS_NULLABLE") == "YES",
                    MaxLength = reader.IsDBNull("CHARACTER_MAXIMUM_LENGTH") ? null : reader.GetInt32("CHARACTER_MAXIMUM_LENGTH"),
                    Precision = reader.IsDBNull("NUMERIC_PRECISION") ? null : reader.GetByte("NUMERIC_PRECISION"),
                    Scale = reader.IsDBNull("NUMERIC_SCALE") ? null : reader.GetInt32("NUMERIC_SCALE"),
                    DefaultValue = reader.IsDBNull("COLUMN_DEFAULT") ? null : reader.GetString("COLUMN_DEFAULT")
                });
            }

            return columns;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting schema for table {TableName}", tableName);
            return new List<ColumnInfo>();
        }

    /// <summary>
    /// Generate sample data when no database connection is available
    /// </summary>
    private (List<dynamic> Data, int TotalCount) GenerateSampleData(string tableName, int page, int pageSize, string searchTerm)
{
    public string Name { get; set; } = string.Empty;
    public string DataType { get; set; } = string.Empty;
    public bool IsNullable { get; set; }
    public int? MaxLength { get; set; }
    public byte? Precision { get; set; }
    public int? Scale { get; set; }
    public string? DefaultValue { get; set; }
}


{
    var sampleData = new List<dynamic>();
    var totalCount = 0;

    switch (tableName.ToLower())
    {
        case "categories":
            var categories = new List<dynamic>
            {
                new { CategoryId = 1, CategoryName = "Electronics", Description = "Electronic devices and gadgets", IsActive = true, CreatedDate = DateTime.Now.AddDays(-30) },
                new { CategoryId = 2, CategoryName = "Books", Description = "Books and literature", IsActive = true, CreatedDate = DateTime.Now.AddDays(-25) },
                new { CategoryId = 3, CategoryName = "Clothing", Description = "Apparel and accessories", IsActive = true, CreatedDate = DateTime.Now.AddDays(-20) },
                new { CategoryId = 4, CategoryName = "Home & Garden", Description = "Home improvement and gardening", IsActive = true, CreatedDate = DateTime.Now.AddDays(-15) },
                new { CategoryId = 5, CategoryName = "Sports", Description = "Sports and outdoor equipment", IsActive = false, CreatedDate = DateTime.Now.AddDays(-10) }
            };
            sampleData = FilterAndPaginate(categories, searchTerm, page, pageSize, out totalCount);
            break;

        case "products":
            var products = new List<dynamic>
            {
                new { ProductId = 1, ProductName = "Laptop Computer", CategoryId = 1, Price = 999.99m, SKU = "LAP001", InStock = 25, IsActive = true, CreatedDate = DateTime.Now.AddDays(-20) },
                new { ProductId = 2, ProductName = "Smartphone", CategoryId = 1, Price = 699.99m, SKU = "PHN001", InStock = 50, IsActive = true, CreatedDate = DateTime.Now.AddDays(-18) },
                new { ProductId = 3, ProductName = "Programming Book", CategoryId = 2, Price = 49.99m, SKU = "BK001", InStock = 100, IsActive = true, CreatedDate = DateTime.Now.AddDays(-15) },
                new { ProductId = 4, ProductName = "T-Shirt", CategoryId = 3, Price = 19.99m, SKU = "TSH001", InStock = 200, IsActive = true, CreatedDate = DateTime.Now.AddDays(-12) },
                new { ProductId = 5, ProductName = "Running Shoes", CategoryId = 5, Price = 129.99m, SKU = "SH001", InStock = 75, IsActive = true, CreatedDate = DateTime.Now.AddDays(-8) },
                new { ProductId = 6, ProductName = "Tablet", CategoryId = 1, Price = 399.99m, SKU = "TAB001", InStock = 30, IsActive = true, CreatedDate = DateTime.Now.AddDays(-5) }
            };
            sampleData = FilterAndPaginate(products, searchTerm, page, pageSize, out totalCount);
            break;

        case "customers":
            var customers = new List<dynamic>
            {
                new { CustomerId = 1, FirstName = "John", LastName = "Doe", Email = "<EMAIL>", Phone = "555-0101", City = "New York", State = "NY", IsActive = true, CreatedDate = DateTime.Now.AddDays(-25) },
                new { CustomerId = 2, FirstName = "Jane", LastName = "Smith", Email = "<EMAIL>", Phone = "555-0102", City = "Los Angeles", State = "CA", IsActive = true, CreatedDate = DateTime.Now.AddDays(-22) },
                new { CustomerId = 3, FirstName = "Bob", LastName = "Johnson", Email = "<EMAIL>", Phone = "555-0103", City = "Chicago", State = "IL", IsActive = true, CreatedDate = DateTime.Now.AddDays(-18) },
                new { CustomerId = 4, FirstName = "Alice", LastName = "Williams", Email = "<EMAIL>", Phone = "555-0104", City = "Houston", State = "TX", IsActive = false, CreatedDate = DateTime.Now.AddDays(-15) },
                new { CustomerId = 5, FirstName = "Charlie", LastName = "Brown", Email = "<EMAIL>", Phone = "555-0105", City = "Phoenix", State = "AZ", IsActive = true, CreatedDate = DateTime.Now.AddDays(-10) }
            };
            sampleData = FilterAndPaginate(customers, searchTerm, page, pageSize, out totalCount);
            break;

        case "orders":
            var orders = new List<dynamic>
            {
                new { OrderId = 1, CustomerId = 1, OrderDate = DateTime.Now.AddDays(-10), TotalAmount = 1049.98m, Status = "Delivered", CreatedDate = DateTime.Now.AddDays(-10) },
                new { OrderId = 2, CustomerId = 2, OrderDate = DateTime.Now.AddDays(-8), TotalAmount = 699.99m, Status = "Shipped", CreatedDate = DateTime.Now.AddDays(-8) },
                new { OrderId = 3, CustomerId = 3, OrderDate = DateTime.Now.AddDays(-5), TotalAmount = 169.98m, Status = "Processing", CreatedDate = DateTime.Now.AddDays(-5) },
                new { OrderId = 4, CustomerId = 1, OrderDate = DateTime.Now.AddDays(-3), TotalAmount = 399.99m, Status = "Pending", CreatedDate = DateTime.Now.AddDays(-3) },
                new { OrderId = 5, CustomerId = 5, OrderDate = DateTime.Now.AddDays(-1), TotalAmount = 129.99m, Status = "Pending", CreatedDate = DateTime.Now.AddDays(-1) }
            };
            sampleData = FilterAndPaginate(orders, searchTerm, page, pageSize, out totalCount);
            break;

        case "orderitems":
            var orderItems = new List<dynamic>
            {
                new { OrderItemId = 1, OrderId = 1, ProductId = 1, Quantity = 1, UnitPrice = 999.99m, TotalPrice = 999.99m, CreatedDate = DateTime.Now.AddDays(-10) },
                new { OrderItemId = 2, OrderId = 1, ProductId = 3, Quantity = 1, UnitPrice = 49.99m, TotalPrice = 49.99m, CreatedDate = DateTime.Now.AddDays(-10) },
                new { OrderItemId = 3, OrderId = 2, ProductId = 2, Quantity = 1, UnitPrice = 699.99m, TotalPrice = 699.99m, CreatedDate = DateTime.Now.AddDays(-8) },
                new { OrderItemId = 4, OrderId = 3, ProductId = 4, Quantity = 2, UnitPrice = 19.99m, TotalPrice = 39.98m, CreatedDate = DateTime.Now.AddDays(-5) },
                new { OrderItemId = 5, OrderId = 3, ProductId = 5, Quantity = 1, UnitPrice = 129.99m, TotalPrice = 129.99m, CreatedDate = DateTime.Now.AddDays(-5) }
            };
            sampleData = FilterAndPaginate(orderItems, searchTerm, page, pageSize, out totalCount);
            break;

        default:
            // Generate generic sample data for unknown tables
            sampleData = new List<dynamic>
            {
                new { Id = 1, Name = $"Sample {tableName} 1", Description = "Sample data for demonstration", IsActive = true, CreatedDate = DateTime.Now.AddDays(-5) },
                new { Id = 2, Name = $"Sample {tableName} 2", Description = "Sample data for demonstration", IsActive = true, CreatedDate = DateTime.Now.AddDays(-3) },
                new { Id = 3, Name = $"Sample {tableName} 3", Description = "Sample data for demonstration", IsActive = false, CreatedDate = DateTime.Now.AddDays(-1) }
            };
            totalCount = sampleData.Count;
            break;
    }

    _logger.LogInformation("Generated {Count} sample records for table {TableName} (total: {TotalCount})",
        sampleData.Count, tableName, totalCount);

    return (sampleData, totalCount);
}

private List<dynamic> FilterAndPaginate(List<dynamic> data, string searchTerm, int page, int pageSize, out int totalCount)
{
    var filteredData = data.AsEnumerable();

    // Apply search filter if provided
    if (!string.IsNullOrEmpty(searchTerm))
    {
        filteredData = filteredData.Where(item =>
        {
            var dict = (IDictionary<string, object>)item;
            return dict.Values.Any(value =>
                value?.ToString()?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true);
        });
    }

    totalCount = filteredData.Count();

    // Apply pagination
    var pagedData = filteredData
        .Skip((page - 1) * pageSize)
        .Take(pageSize)
        .ToList();

    return pagedData;
}

private dynamic? GetSampleRecordById(string tableName, string idColumn, object id)
{
    var (sampleData, _) = GenerateSampleData(tableName, 1, 1000, "");

    return sampleData.FirstOrDefault(item =>
    {
        var dict = (IDictionary<string, object>)item;
        return dict.ContainsKey(idColumn) && dict[idColumn]?.ToString() == id.ToString();
    });
}
}

public class ColumnInfo
{
    public string Name { get; set; } = string.Empty;
    public string DataType { get; set; } = string.Empty;
    public bool IsNullable { get; set; }
    public int? MaxLength { get; set; }
    public byte? Precision { get; set; }
    public int? Scale { get; set; }
    public string? DefaultValue { get; set; }
}
