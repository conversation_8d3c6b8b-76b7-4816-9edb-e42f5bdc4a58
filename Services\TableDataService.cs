using Microsoft.Data.SqlClient;

namespace Crudible.Services;

public class TableDataService
{
    private readonly ILogger<TableDataService> _logger;
    private readonly ConfigurationService _configService;

    public TableDataService(ILogger<TableDataService> logger, ConfigurationService configService)
    {
        _logger = logger;
        _configService = configService;
    }

    public async Task<(List<dynamic> Data, int TotalCount)> GetTableDataAsync(string tableName, int page = 1, int pageSize = 25, string searchTerm = "")
    {
        try
        {
            var config = await _configService.LoadConfigurationAsync();
            var connectionString = config.Database.ConnectionString;

            if (string.IsNullOrEmpty(connectionString))
            {
                return GenerateSampleData(tableName, page, pageSize);
            }

            using var connection = new SqlConnection(connectionString);
            await connection.OpenAsync();

            var countQuery = $"SELECT COUNT(*) FROM [{tableName}]";
            using var countCommand = new SqlCommand(countQuery, connection);
            var totalCount = (int)await countCommand.ExecuteScalarAsync();

            var dataQuery = $"SELECT * FROM [{tableName}] ORDER BY (SELECT NULL) OFFSET {(page - 1) * pageSize} ROWS FETCH NEXT {pageSize} ROWS ONLY";
            using var dataCommand = new SqlCommand(dataQuery, connection);
            
            var data = new List<dynamic>();
            using var reader = await dataCommand.ExecuteReaderAsync();
            
            while (await reader.ReadAsync())
            {
                var row = new Dictionary<string, object>();
                for (int i = 0; i < reader.FieldCount; i++)
                {
                    var value = reader.IsDBNull(i) ? null : reader.GetValue(i);
                    row[reader.GetName(i)] = value;
                }
                data.Add(row);
            }

            return (data.Cast<dynamic>().ToList(), totalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting table data for {TableName}", tableName);
            return (new List<dynamic>(), 0);
        }
    }

    public async Task<dynamic?> GetRecordByIdAsync(string tableName, string idColumn, object id)
    {
        try
        {
            var config = await _configService.LoadConfigurationAsync();
            var connectionString = config.Database.ConnectionString;

            if (string.IsNullOrEmpty(connectionString))
            {
                return GetSampleRecordById(tableName, idColumn, id);
            }

            using var connection = new SqlConnection(connectionString);
            await connection.OpenAsync();

            var query = $"SELECT * FROM [{tableName}] WHERE [{idColumn}] = @id";
            using var command = new SqlCommand(query, connection);
            command.Parameters.AddWithValue("@id", id);

            using var reader = await command.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                var row = new Dictionary<string, object>();
                for (int i = 0; i < reader.FieldCount; i++)
                {
                    var value = reader.IsDBNull(i) ? null : reader.GetValue(i);
                    row[reader.GetName(i)] = value;
                }
                return row;
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting record by ID for {TableName}", tableName);
            return null;
        }
    }

    public async Task<bool> InsertRecordAsync(string tableName, Dictionary<string, object> data)
    {
        try
        {
            var config = await _configService.LoadConfigurationAsync();
            var connectionString = config.Database.ConnectionString;

            if (string.IsNullOrEmpty(connectionString))
            {
                _logger.LogInformation("Demo mode: Simulating insert for table: {TableName}", tableName);
                await Task.Delay(100);
                return true;
            }

            using var connection = new SqlConnection(connectionString);
            await connection.OpenAsync();

            var columns = string.Join(", ", data.Keys.Select(k => $"[{k}]"));
            var values = string.Join(", ", data.Keys.Select(k => $"@{k}"));
            var query = $"INSERT INTO [{tableName}] ({columns}) VALUES ({values})";

            using var command = new SqlCommand(query, connection);
            foreach (var kvp in data)
            {
                command.Parameters.AddWithValue($"@{kvp.Key}", kvp.Value ?? DBNull.Value);
            }

            var rowsAffected = await command.ExecuteNonQueryAsync();
            return rowsAffected > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error inserting record into {TableName}", tableName);
            return false;
        }
    }

    public async Task<bool> UpdateRecordAsync(string tableName, string idColumn, object id, Dictionary<string, object> data)
    {
        try
        {
            var config = await _configService.LoadConfigurationAsync();
            var connectionString = config.Database.ConnectionString;

            if (string.IsNullOrEmpty(connectionString))
            {
                _logger.LogInformation("Demo mode: Simulating update for table: {TableName}, ID: {Id}", tableName, id);
                await Task.Delay(100);
                return true;
            }

            using var connection = new SqlConnection(connectionString);
            await connection.OpenAsync();

            var setClause = string.Join(", ", data.Keys.Select(k => $"[{k}] = @{k}"));
            var query = $"UPDATE [{tableName}] SET {setClause} WHERE [{idColumn}] = @id";

            using var command = new SqlCommand(query, connection);
            command.Parameters.AddWithValue("@id", id);
            foreach (var kvp in data)
            {
                command.Parameters.AddWithValue($"@{kvp.Key}", kvp.Value ?? DBNull.Value);
            }

            var rowsAffected = await command.ExecuteNonQueryAsync();
            return rowsAffected > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating record in {TableName} with ID {Id}", tableName, id);
            return false;
        }
    }

    public async Task<bool> DeleteRecordAsync(string tableName, string idColumn, object id)
    {
        try
        {
            var config = await _configService.LoadConfigurationAsync();
            var connectionString = config.Database.ConnectionString;

            if (string.IsNullOrEmpty(connectionString))
            {
                _logger.LogInformation("Demo mode: Simulating delete for table: {TableName}, ID: {Id}", tableName, id);
                await Task.Delay(100);
                return true;
            }

            using var connection = new SqlConnection(connectionString);
            await connection.OpenAsync();

            var query = $"DELETE FROM [{tableName}] WHERE [{idColumn}] = @id";
            using var command = new SqlCommand(query, connection);
            command.Parameters.AddWithValue("@id", id);

            var rowsAffected = await command.ExecuteNonQueryAsync();
            return rowsAffected > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting record from {TableName} with ID {Id}", tableName, id);
            return false;
        }
    }

    private (List<dynamic> Data, int TotalCount) GenerateSampleData(string tableName, int page, int pageSize)
    {
        var sampleData = new List<dynamic>();
        
        switch (tableName.ToLower())
        {
            case "categories":
                sampleData = new List<dynamic>
                {
                    new { CategoryId = 1, CategoryName = "Electronics", Description = "Electronic devices", IsActive = true },
                    new { CategoryId = 2, CategoryName = "Books", Description = "Books and literature", IsActive = true },
                    new { CategoryId = 3, CategoryName = "Clothing", Description = "Apparel and accessories", IsActive = true }
                };
                break;
            case "products":
                sampleData = new List<dynamic>
                {
                    new { ProductId = 1, ProductName = "Laptop", CategoryId = 1, Price = 999.99m, SKU = "LAP001" },
                    new { ProductId = 2, ProductName = "Book", CategoryId = 2, Price = 29.99m, SKU = "BK001" },
                    new { ProductId = 3, ProductName = "Shirt", CategoryId = 3, Price = 19.99m, SKU = "SH001" }
                };
                break;
            default:
                sampleData = new List<dynamic>
                {
                    new { Id = 1, Name = $"Sample {tableName} 1", Description = "Sample data" },
                    new { Id = 2, Name = $"Sample {tableName} 2", Description = "Sample data" }
                };
                break;
        }

        var totalCount = sampleData.Count;
        var pagedData = sampleData.Skip((page - 1) * pageSize).Take(pageSize).ToList();
        
        return (pagedData, totalCount);
    }

    private dynamic? GetSampleRecordById(string tableName, string idColumn, object id)
    {
        var (sampleData, _) = GenerateSampleData(tableName, 1, 1000);
        
        return sampleData.FirstOrDefault(item =>
        {
            var dict = (IDictionary<string, object>)item;
            return dict.ContainsKey(idColumn) && dict[idColumn]?.ToString() == id.ToString();
        });
    }
}
