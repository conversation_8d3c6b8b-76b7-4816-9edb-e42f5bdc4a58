@page "/demo"
@model DemoIndexModel
@using Crudible.Models
@{
    ViewData["Title"] = "Crudible Demo";
    ViewData["Breadcrumbs"] = new List<BreadcrumbItem>
    {
        new BreadcrumbItem("Demo", "/demo", true, "fas fa-play")
    };
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="jumbotron bg-primary text-white p-5 rounded mb-4">
                <h1 class="display-4"><i class="fas fa-magic me-3"></i>Crudible Enterprise CRUD Generator</h1>
                <p class="lead">Transform your database into a complete web application in seconds!</p>
                <hr class="my-4 bg-white">
                <p>Upload a schema, connect to a database, and generate professional CRUD interfaces instantly.</p>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-4 mb-4">
            <div class="card h-100 border-primary">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-upload me-2"></i>1. Upload Schema</h5>
                </div>
                <div class="card-body">
                    <p>Start by uploading an XML schema file or connecting to your database.</p>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>XML Schema Support</li>
                        <li><i class="fas fa-check text-success me-2"></i>Database Extraction</li>
                        <li><i class="fas fa-check text-success me-2"></i>Multiple Formats</li>
                        <li><i class="fas fa-check text-success me-2"></i>Validation & Error Handling</li>
                    </ul>
                    <div class="mt-3">
                        <a href="/config" class="btn btn-primary">
                            <i class="fas fa-cog me-1"></i>Configure
                        </a>
                        <button class="btn btn-outline-primary" onclick="downloadSample()">
                            <i class="fas fa-download me-1"></i>Sample Schema
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-4">
            <div class="card h-100 border-success">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-magic me-2"></i>2. Generate CRUD</h5>
                </div>
                <div class="card-body">
                    <p>Automatically generate complete CRUD interfaces for your tables.</p>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>Professional UI</li>
                        <li><i class="fas fa-check text-success me-2"></i>Responsive Design</li>
                        <li><i class="fas fa-check text-success me-2"></i>Validation & Security</li>
                        <li><i class="fas fa-check text-success me-2"></i>Search & Pagination</li>
                    </ul>
                    <div class="mt-3">
                        <a href="/tables" class="btn btn-success">
                            <i class="fas fa-table me-1"></i>Manage Tables
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-4">
            <div class="card h-100 border-info">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-rocket me-2"></i>3. Use & Deploy</h5>
                </div>
                <div class="card-body">
                    <p>Your complete application is ready to use and deploy!</p>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>Production Ready</li>
                        <li><i class="fas fa-check text-success me-2"></i>Export Capabilities</li>
                        <li><i class="fas fa-check text-success me-2"></i>Audit Trail</li>
                        <li><i class="fas fa-check text-success me-2"></i>Customizable</li>
                    </ul>
                    <div class="mt-3">
                        <button class="btn btn-info" onclick="showFeatures()">
                            <i class="fas fa-list me-1"></i>View Features
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-database me-2"></i>Quick Test Database Setup</h5>
                </div>
                <div class="card-body">
                    <p>Want to test immediately? Use our sample database setup:</p>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-server me-2"></i>SQL Server LocalDB</h6>
                            <div class="bg-light p-3 rounded">
                                <code>Server=(localdb)\MSSQLLocalDB;Database=CrudibleTest;Integrated Security=true;TrustServerCertificate=true;</code>
                            </div>
                            <small class="text-muted">For local development with SQL Server LocalDB</small>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-cloud me-2"></i>SQL Server Express</h6>
                            <div class="bg-light p-3 rounded">
                                <code>Server=localhost\SQLEXPRESS;Database=CrudibleTest;Integrated Security=true;TrustServerCertificate=true;</code>
                            </div>
                            <small class="text-muted">For SQL Server Express installations</small>
                        </div>
                    </div>

                    <div class="mt-3">
                        <button class="btn btn-outline-primary" onclick="copyConnectionString('localdb')">
                            <i class="fas fa-copy me-1"></i>Copy LocalDB
                        </button>
                        <button class="btn btn-outline-primary" onclick="copyConnectionString('express')">
                            <i class="fas fa-copy me-1"></i>Copy Express
                        </button>
                        <button class="btn btn-outline-success" onclick="downloadSqlScript()">
                            <i class="fas fa-download me-1"></i>Download Setup Script
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-star me-2"></i>Enterprise Features</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <h6><i class="fas fa-shield-alt text-primary me-2"></i>Security</h6>
                            <ul class="list-unstyled small">
                                <li>SQL Injection Protection</li>
                                <li>Input Validation</li>
                                <li>Error Handling</li>
                                <li>Audit Logging</li>
                            </ul>
                        </div>
                        <div class="col-md-3">
                            <h6><i class="fas fa-tachometer-alt text-success me-2"></i>Performance</h6>
                            <ul class="list-unstyled small">
                                <li>Optimized Queries</li>
                                <li>Pagination</li>
                                <li>Connection Pooling</li>
                                <li>Caching Support</li>
                            </ul>
                        </div>
                        <div class="col-md-3">
                            <h6><i class="fas fa-mobile-alt text-info me-2"></i>UI/UX</h6>
                            <ul class="list-unstyled small">
                                <li>Responsive Design</li>
                                <li>Bootstrap 5</li>
                                <li>Professional Styling</li>
                                <li>Accessibility</li>
                            </ul>
                        </div>
                        <div class="col-md-3">
                            <h6><i class="fas fa-download text-warning me-2"></i>Export</h6>
                            <ul class="list-unstyled small">
                                <li>Excel Export</li>
                                <li>CSV Export</li>
                                <li>JSON Export</li>
                                <li>PDF Reports</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function downloadSample() {
    window.location.href = '/sample-schema.xml';
}

function downloadSqlScript() {
    window.location.href = '/test-connection.sql';
}

function copyConnectionString(type) {
    let connectionString;
    if (type === 'localdb') {
        connectionString = 'Server=(localdb)\\MSSQLLocalDB;Database=CrudibleTest;Integrated Security=true;TrustServerCertificate=true;';
    } else {
        connectionString = 'Server=localhost\\SQLEXPRESS;Database=CrudibleTest;Integrated Security=true;TrustServerCertificate=true;';
    }
    
    navigator.clipboard.writeText(connectionString).then(function() {
        showToast('Connection string copied to clipboard!', 'success');
    });
}

function showFeatures() {
    alert('Enterprise Features:\n\n' +
          '✓ Real-time CRUD Generation\n' +
          '✓ Professional UI Components\n' +
          '✓ Database Schema Extraction\n' +
          '✓ Multi-format Export\n' +
          '✓ Search & Pagination\n' +
          '✓ Validation & Security\n' +
          '✓ Audit Trail\n' +
          '✓ Responsive Design\n' +
          '✓ Production Ready');
}

function showToast(message, type) {
    // Simple toast notification
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
    document.body.appendChild(toast);
    
    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 3000);
}
</script>
