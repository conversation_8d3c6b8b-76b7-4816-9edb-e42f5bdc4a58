@page "/status"
@using Crudible.Services
@inject ConfigurationService ConfigService
@{
    ViewData["Title"] = "System Status";

    var config = await ConfigService.LoadConfigurationAsync();
    var hasConnection = !string.IsNullOrEmpty(config.Database.ConnectionString);
    var hasSchema = !string.IsNullOrEmpty(config.Schema.XmlSchema);
}

<div class="container-fluid">
    <h2><i class="fas fa-info-circle me-2"></i>System Status</h2>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">Configuration</div>
                <div class="card-body">
                    <p><strong>Database:</strong> 
                        @if (hasConnection)
                        {
                            <span class="badge bg-success">Connected</span>
                        }
                        else
                        {
                            <span class="badge bg-warning">Demo Mode</span>
                        }
                    </p>
                    <p><strong>Schema:</strong> 
                        @if (hasSchema)
                        {
                            <span class="badge bg-success">Loaded</span>
                        }
                        else
                        {
                            <span class="badge bg-danger">None</span>
                        }
                    </p>
                    <p><strong>Tables:</strong> @(hasSchema ? "Available" : "None")</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">Quick Actions</div>
                <div class="card-body">
                    <a href="/config" class="btn btn-primary mb-2 w-100">Upload Schema</a>
                    <a href="/tables" class="btn btn-success mb-2 w-100">Manage Tables</a>
                    <a href="/demo" class="btn btn-info w-100">View Demo</a>
                </div>
            </div>
        </div>
    </div>
    

</div>
