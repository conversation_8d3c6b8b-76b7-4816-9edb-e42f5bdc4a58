-- Test SQL Server Connection Script
-- This script creates a simple test database for Crudible demonstration

-- Create test database (if it doesn't exist)
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'CrudibleTest')
BEGIN
    CREATE DATABASE CrudibleTest;
END
GO

USE CrudibleTest;
GO

-- Create Categories table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Categories' AND xtype='U')
BEGIN
    CREATE TABLE Categories (
        CategoryId INT IDENTITY(1,1) PRIMARY KEY,
        CategoryName NVARCHAR(100) NOT NULL UNIQUE,
        Description NVARCHAR(500) NULL,
        ParentCategoryId INT NULL,
        IsActive BIT NOT NULL DEFAULT 1,
        CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        ModifiedDate DATETIME2 NULL,
        FOREIGN KEY (ParentCategoryId) REFERENCES Categories(CategoryId)
    );
END
GO

-- Create Products table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Products' AND xtype='U')
BEGIN
    CREATE TABLE Products (
        ProductId INT IDENTITY(1,1) PRIMARY KEY,
        ProductName NVARCHAR(200) NOT NULL,
        CategoryId INT NOT NULL,
        Price DECIMAL(10,2) NOT NULL,
        Description NVARCHAR(1000) NULL,
        SKU NVARCHAR(50) NOT NULL UNIQUE,
        InStock INT NOT NULL DEFAULT 0,
        IsActive BIT NOT NULL DEFAULT 1,
        CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        ModifiedDate DATETIME2 NULL,
        FOREIGN KEY (CategoryId) REFERENCES Categories(CategoryId)
    );
END
GO

-- Create Customers table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Customers' AND xtype='U')
BEGIN
    CREATE TABLE Customers (
        CustomerId INT IDENTITY(1,1) PRIMARY KEY,
        FirstName NVARCHAR(50) NOT NULL,
        LastName NVARCHAR(50) NOT NULL,
        Email NVARCHAR(100) NOT NULL UNIQUE,
        Phone NVARCHAR(20) NULL,
        Address NVARCHAR(200) NULL,
        City NVARCHAR(50) NULL,
        State NVARCHAR(50) NULL,
        ZipCode NVARCHAR(10) NULL,
        IsActive BIT NOT NULL DEFAULT 1,
        CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        ModifiedDate DATETIME2 NULL
    );
END
GO

-- Insert sample data
IF NOT EXISTS (SELECT * FROM Categories)
BEGIN
    INSERT INTO Categories (CategoryName, Description) VALUES
    ('Electronics', 'Electronic devices and gadgets'),
    ('Books', 'Books and literature'),
    ('Clothing', 'Apparel and accessories'),
    ('Home & Garden', 'Home improvement and gardening supplies'),
    ('Sports', 'Sports and outdoor equipment');
END
GO

IF NOT EXISTS (SELECT * FROM Products)
BEGIN
    INSERT INTO Products (ProductName, CategoryId, Price, Description, SKU, InStock) VALUES
    ('Laptop Computer', 1, 999.99, 'High-performance laptop for work and gaming', 'LAP001', 25),
    ('Smartphone', 1, 699.99, 'Latest smartphone with advanced features', 'PHN001', 50),
    ('Programming Book', 2, 49.99, 'Learn programming with this comprehensive guide', 'BK001', 100),
    ('T-Shirt', 3, 19.99, 'Comfortable cotton t-shirt', 'TSH001', 200),
    ('Running Shoes', 5, 129.99, 'Professional running shoes for athletes', 'SH001', 75);
END
GO

IF NOT EXISTS (SELECT * FROM Customers)
BEGIN
    INSERT INTO Customers (FirstName, LastName, Email, Phone, Address, City, State, ZipCode) VALUES
    ('John', 'Doe', '<EMAIL>', '555-0101', '123 Main St', 'Anytown', 'CA', '12345'),
    ('Jane', 'Smith', '<EMAIL>', '555-0102', '456 Oak Ave', 'Somewhere', 'NY', '67890'),
    ('Bob', 'Johnson', '<EMAIL>', '555-0103', '789 Pine Rd', 'Elsewhere', 'TX', '54321'),
    ('Alice', 'Williams', '<EMAIL>', '555-0104', '321 Elm St', 'Nowhere', 'FL', '98765'),
    ('Charlie', 'Brown', '<EMAIL>', '555-0105', '654 Maple Dr', 'Anywhere', 'WA', '13579');
END
GO

-- Create a view for testing
IF NOT EXISTS (SELECT * FROM sys.views WHERE name = 'ProductCategoryView')
BEGIN
    EXEC('CREATE VIEW ProductCategoryView AS
    SELECT 
        p.ProductId,
        p.ProductName,
        p.Price,
        p.SKU,
        p.InStock,
        c.CategoryName,
        c.Description as CategoryDescription,
        p.IsActive,
        p.CreatedDate
    FROM Products p
    INNER JOIN Categories c ON p.CategoryId = c.CategoryId
    WHERE p.IsActive = 1 AND c.IsActive = 1');
END
GO

-- Display connection info
SELECT 
    'CrudibleTest' as DatabaseName,
    COUNT(*) as TableCount
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_TYPE = 'BASE TABLE';

SELECT 
    TABLE_NAME as TableName,
    COUNT(*) as ColumnCount
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_CATALOG = 'CrudibleTest'
GROUP BY TABLE_NAME
ORDER BY TABLE_NAME;

PRINT 'Test database setup complete!';
PRINT 'Connection string example:';
PRINT 'Server=localhost;Database=CrudibleTest;Integrated Security=true;TrustServerCertificate=true;';
