# Crudible Test Automation Script
Write-Host "=== Crudible Test Automation ===" -ForegroundColor Green

# Test 1: Check if application is running
Write-Host "1. Testing application availability..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5000" -Method GET -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ Application is running" -ForegroundColor Green
    }
} catch {
    Write-Host "✗ Application not running. Start with 'dotnet run'" -ForegroundColor Red
    exit 1
}

# Test 2: Check status page
Write-Host "2. Testing status page..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5000/status" -Method GET -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ Status page accessible" -ForegroundColor Green
    }
} catch {
    Write-Host "✗ Status page failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Check config page
Write-Host "3. Testing config page..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5000/config" -Method GET -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ Config page accessible" -ForegroundColor Green
    }
} catch {
    Write-Host "✗ Config page failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Check tables page
Write-Host "4. Testing tables page..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5000/tables" -Method GET -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ Tables page accessible" -ForegroundColor Green
    }
} catch {
    Write-Host "✗ Tables page failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: Upload sample schema
Write-Host "5. Testing schema upload..." -ForegroundColor Yellow
try {
    # Read sample schema
    $schemaPath = "wwwroot/sample-schema.xml"
    if (Test-Path $schemaPath) {
        Write-Host "✓ Sample schema file found" -ForegroundColor Green
    } else {
        Write-Host "✗ Sample schema file not found" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Schema upload failed: $($_.Exception.Message)" -ForegroundColor Red
}
}

# Test 6: Check if tables are loaded
Write-Host "6. Testing table loading..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5000/tables" -Method GET -TimeoutSec 10
    $content = $response.Content
    
    if ($content -match "Categories|Products|Customers") {
        Write-Host "✓ Tables loaded from schema" -ForegroundColor Green
    } else {
        Write-Host "? Tables may not be loaded yet" -ForegroundColor Yellow
    }
} catch {
    Write-Host "✗ Table loading test failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 7: Test CRUD generation
Write-Host "7. Testing CRUD generation..." -ForegroundColor Yellow
try {
    Write-Host "? CRUD generation requires manual testing via web interface" -ForegroundColor Yellow
} catch {
    Write-Host "✗ CRUD generation test failed: $($_.Exception.Message)" -ForegroundColor Red
}
}

# Test 8: Check if CRUD pages were created
Write-Host "8. Testing generated CRUD pages..." -ForegroundColor Yellow
$crudPaths = @(
    "Pages/Tables/Categories/Index.cshtml",
    "Pages/Tables/Categories/Create.cshtml",
    "Pages/Tables/Categories/Edit.cshtml"
)

$crudFilesExist = $true
foreach ($path in $crudPaths) {
    if (-not (Test-Path $path)) {
        $crudFilesExist = $false
        break
    }
}

if ($crudFilesExist) {
    Write-Host "✓ CRUD files generated successfully" -ForegroundColor Green
} else {
    Write-Host "✗ CRUD files not found" -ForegroundColor Red
}

# Test 9: Test sample data API
Write-Host "9. Testing sample data API..." -ForegroundColor Yellow
try {
    Write-Host "? API testing requires CRUD generation first" -ForegroundColor Yellow
} catch {
    Write-Host "✗ Sample data API failed: $($_.Exception.Message)" -ForegroundColor Red
}
}

Write-Host "`n=== Test Summary ===" -ForegroundColor Green
Write-Host "Basic workflow test completed." -ForegroundColor White
Write-Host "If all tests passed, the system is working correctly." -ForegroundColor White
Write-Host "`nNext steps:" -ForegroundColor Yellow
Write-Host "1. Go to http://localhost:5000/config to upload schema" -ForegroundColor White
Write-Host "2. Go to http://localhost:5000/tables to generate CRUD" -ForegroundColor White
Write-Host "3. Test the generated CRUD pages" -ForegroundColor White
