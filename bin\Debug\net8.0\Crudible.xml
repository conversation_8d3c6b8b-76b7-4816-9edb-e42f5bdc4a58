<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Crudible</name>
    </assembly>
    <members>
        <member name="T:Crudible.Configuration.CrudibleConfiguration">
            <summary>
            Main configuration class for the Crudible application
            </summary>
        </member>
        <member name="P:Crudible.Configuration.CrudibleConfiguration.Database">
            <summary>
            Database connection configuration
            </summary>
        </member>
        <member name="P:Crudible.Configuration.CrudibleConfiguration.Schema">
            <summary>
            Schema configuration settings
            </summary>
        </member>
        <member name="P:Crudible.Configuration.CrudibleConfiguration.UI">
            <summary>
            UI and display configuration
            </summary>
        </member>
        <member name="P:Crudible.Configuration.CrudibleConfiguration.Export">
            <summary>
            Export functionality configuration
            </summary>
        </member>
        <member name="P:Crudible.Configuration.CrudibleConfiguration.Audit">
            <summary>
            Audit trail configuration
            </summary>
        </member>
        <member name="T:Crudible.Configuration.DatabaseConfiguration">
            <summary>
            Database connection configuration
            </summary>
        </member>
        <member name="P:Crudible.Configuration.DatabaseConfiguration.ConnectionString">
            <summary>
            SQL Server connection string
            </summary>
        </member>
        <member name="P:Crudible.Configuration.DatabaseConfiguration.Provider">
            <summary>
            Database provider type
            </summary>
        </member>
        <member name="P:Crudible.Configuration.DatabaseConfiguration.CommandTimeout">
            <summary>
            Connection timeout in seconds
            </summary>
        </member>
        <member name="P:Crudible.Configuration.DatabaseConfiguration.EnablePooling">
            <summary>
            Enable connection pooling
            </summary>
        </member>
        <member name="T:Crudible.Configuration.SchemaConfiguration">
            <summary>
            Schema configuration settings
            </summary>
        </member>
        <member name="P:Crudible.Configuration.SchemaConfiguration.XmlSchema">
            <summary>
            XML schema file path or content
            </summary>
        </member>
        <member name="P:Crudible.Configuration.SchemaConfiguration.IncludedTables">
            <summary>
            List of tables to include in CRUD generation
            </summary>
        </member>
        <member name="P:Crudible.Configuration.SchemaConfiguration.ExcludedTables">
            <summary>
            List of tables to exclude from CRUD generation
            </summary>
        </member>
        <member name="P:Crudible.Configuration.SchemaConfiguration.ValidationRules">
            <summary>
            Custom validation rules per table/field
            </summary>
        </member>
        <member name="P:Crudible.Configuration.SchemaConfiguration.AutoDetectRelationships">
            <summary>
            Auto-detect relationships
            </summary>
        </member>
        <member name="T:Crudible.Configuration.UIConfiguration">
            <summary>
            UI configuration settings
            </summary>
        </member>
        <member name="P:Crudible.Configuration.UIConfiguration.ApplicationTitle">
            <summary>
            Application title
            </summary>
        </member>
        <member name="P:Crudible.Configuration.UIConfiguration.ItemsPerPage">
            <summary>
            Items per page for list views
            </summary>
        </member>
        <member name="P:Crudible.Configuration.UIConfiguration.EnableResponsiveDesign">
            <summary>
            Enable responsive design
            </summary>
        </member>
        <member name="P:Crudible.Configuration.UIConfiguration.BootstrapTheme">
            <summary>
            Bootstrap theme
            </summary>
        </member>
        <member name="P:Crudible.Configuration.UIConfiguration.ShowAuditInfo">
            <summary>
            Show audit information in UI
            </summary>
        </member>
        <member name="T:Crudible.Configuration.ExportConfiguration">
            <summary>
            Export configuration settings
            </summary>
        </member>
        <member name="P:Crudible.Configuration.ExportConfiguration.EnablePdfExport">
            <summary>
            Enable PDF export
            </summary>
        </member>
        <member name="P:Crudible.Configuration.ExportConfiguration.EnableExcelExport">
            <summary>
            Enable Excel export
            </summary>
        </member>
        <member name="P:Crudible.Configuration.ExportConfiguration.MaxExportRecords">
            <summary>
            Maximum records per export
            </summary>
        </member>
        <member name="P:Crudible.Configuration.ExportConfiguration.FileNameTemplate">
            <summary>
            Export file name template
            </summary>
        </member>
        <member name="T:Crudible.Configuration.AuditConfiguration">
            <summary>
            Audit configuration settings
            </summary>
        </member>
        <member name="P:Crudible.Configuration.AuditConfiguration.EnableAuditTrail">
            <summary>
            Enable audit trail
            </summary>
        </member>
        <member name="P:Crudible.Configuration.AuditConfiguration.AuditTableName">
            <summary>
            Audit table name
            </summary>
        </member>
        <member name="P:Crudible.Configuration.AuditConfiguration.RetentionDays">
            <summary>
            Retain audit records for days (0 = forever)
            </summary>
        </member>
        <member name="P:Crudible.Configuration.AuditConfiguration.LogReadOperations">
            <summary>
            Log read operations
            </summary>
        </member>
        <member name="P:Crudible.Configuration.AuditConfiguration.LogCreateOperations">
            <summary>
            Log create operations
            </summary>
        </member>
        <member name="P:Crudible.Configuration.AuditConfiguration.LogUpdateOperations">
            <summary>
            Log update operations
            </summary>
        </member>
        <member name="P:Crudible.Configuration.AuditConfiguration.LogDeleteOperations">
            <summary>
            Log delete operations
            </summary>
        </member>
        <member name="T:Crudible.Configuration.TableValidationRules">
            <summary>
            Table-specific validation rules
            </summary>
        </member>
        <member name="P:Crudible.Configuration.TableValidationRules.FieldRules">
            <summary>
            Field-specific validation rules
            </summary>
        </member>
        <member name="P:Crudible.Configuration.TableValidationRules.BusinessRules">
            <summary>
            Custom business rules
            </summary>
        </member>
        <member name="T:Crudible.Configuration.FieldValidationRule">
            <summary>
            Field-specific validation rule
            </summary>
        </member>
        <member name="P:Crudible.Configuration.FieldValidationRule.RegexPattern">
            <summary>
            Regular expression pattern
            </summary>
        </member>
        <member name="P:Crudible.Configuration.FieldValidationRule.MinLength">
            <summary>
            Minimum length
            </summary>
        </member>
        <member name="P:Crudible.Configuration.FieldValidationRule.MaxLength">
            <summary>
            Maximum length
            </summary>
        </member>
        <member name="P:Crudible.Configuration.FieldValidationRule.MinValue">
            <summary>
            Minimum value (for numeric fields)
            </summary>
        </member>
        <member name="P:Crudible.Configuration.FieldValidationRule.MaxValue">
            <summary>
            Maximum value (for numeric fields)
            </summary>
        </member>
        <member name="P:Crudible.Configuration.FieldValidationRule.ValidationMessage">
            <summary>
            Custom validation message
            </summary>
        </member>
        <member name="P:Crudible.Configuration.FieldValidationRule.IsRequired">
            <summary>
            Field is required
            </summary>
        </member>
        <member name="P:Crudible.Configuration.FieldValidationRule.IsUnique">
            <summary>
            Field must be unique
            </summary>
        </member>
        <member name="T:Crudible.Configuration.DatabaseProvider">
            <summary>
            Database provider types
            </summary>
        </member>
        <member name="T:Crudible.Controllers.ExportController">
            <summary>
            API controller for data export functionality
            </summary>
        </member>
        <member name="M:Crudible.Controllers.ExportController.ExportToExcel(System.String,System.String,System.String,Crudible.Models.SortDirection)">
            <summary>
            Export table data to Excel format
            </summary>
            <param name="tableName">Table name</param>
            <param name="searchTerm">Search term</param>
            <param name="sortField">Sort field</param>
            <param name="sortDirection">Sort direction</param>
            <returns>Excel file</returns>
        </member>
        <member name="M:Crudible.Controllers.ExportController.ExportToCsv(System.String,System.String,System.String,Crudible.Models.SortDirection)">
            <summary>
            Export table data to CSV format
            </summary>
            <param name="tableName">Table name</param>
            <param name="searchTerm">Search term</param>
            <param name="sortField">Sort field</param>
            <param name="sortDirection">Sort direction</param>
            <returns>CSV file</returns>
        </member>
        <member name="M:Crudible.Controllers.ExportController.ExportToJson(System.String,System.String,System.String,Crudible.Models.SortDirection)">
            <summary>
            Export table data to JSON format
            </summary>
            <param name="tableName">Table name</param>
            <param name="searchTerm">Search term</param>
            <param name="sortField">Sort field</param>
            <param name="sortDirection">Sort direction</param>
            <returns>JSON file</returns>
        </member>
        <member name="M:Crudible.Controllers.ExportController.GetExportStatus(System.String)">
            <summary>
            Get export status
            </summary>
            <param name="exportId">Export ID</param>
            <returns>Export status</returns>
        </member>
        <member name="T:Crudible.Controllers.DatabaseController">
            <summary>
            API controller for database operations
            </summary>
        </member>
        <member name="M:Crudible.Controllers.DatabaseController.TestConnection(Crudible.Controllers.DatabaseController.TestConnectionRequest)">
            <summary>
            Test database connection
            </summary>
            <param name="request">Connection test request</param>
            <returns>Connection test result</returns>
        </member>
        <member name="M:Crudible.Controllers.DatabaseController.GetTables(Crudible.Controllers.DatabaseController.GetTablesRequest)">
            <summary>
            Get table list from database
            </summary>
            <param name="connectionString">Database connection string</param>
            <returns>List of tables</returns>
        </member>
        <member name="T:Crudible.Controllers.AuditController">
            <summary>
            API controller for audit operations
            </summary>
        </member>
        <member name="M:Crudible.Controllers.AuditController.GetAuditEntries(System.String,System.String,System.String,System.Nullable{Crudible.Models.AuditOperation},System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Int32,System.Int32)">
            <summary>
            Get audit entries with filtering and pagination
            </summary>
            <param name="tableName">Table name filter</param>
            <param name="recordId">Record ID filter</param>
            <param name="userId">User ID filter</param>
            <param name="operation">Operation filter</param>
            <param name="startDate">Start date filter</param>
            <param name="endDate">End date filter</param>
            <param name="page">Page number</param>
            <param name="pageSize">Page size</param>
            <returns>Paged audit entries</returns>
        </member>
        <member name="M:Crudible.Controllers.AuditController.GetAuditSummary(System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            Get audit summary for reporting
            </summary>
            <param name="startDate">Start date</param>
            <param name="endDate">End date</param>
            <returns>Audit summary</returns>
        </member>
        <member name="T:Crudible.Data.CrudibleDbContext">
            <summary>
            Main database context for Crudible application
            </summary>
        </member>
        <member name="P:Crudible.Data.CrudibleDbContext.AuditEntries">
            <summary>
            Audit log entries
            </summary>
        </member>
        <member name="P:Crudible.Data.CrudibleDbContext.TableDefinitions">
            <summary>
            Table definitions
            </summary>
        </member>
        <member name="P:Crudible.Data.CrudibleDbContext.FieldDefinitions">
            <summary>
            Field definitions
            </summary>
        </member>
        <member name="P:Crudible.Data.CrudibleDbContext.TableRelationships">
            <summary>
            Table relationships
            </summary>
        </member>
        <member name="M:Crudible.Data.CrudibleDbContext.SaveChangesAsync(System.Threading.CancellationToken)">
            <summary>
            Save changes with audit logging
            </summary>
            <returns>Number of affected records</returns>
        </member>
        <member name="T:Crudible.Extensions.ServiceCollectionExtensions">
            <summary>
            Extension methods for IServiceCollection
            </summary>
        </member>
        <member name="M:Crudible.Extensions.ServiceCollectionExtensions.AddCrudibleServices(Microsoft.Extensions.DependencyInjection.IServiceCollection,Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            Add Crudible services to the service collection
            </summary>
            <param name="services">Service collection</param>
            <param name="configuration">Configuration</param>
            <returns>Service collection</returns>
        </member>
        <member name="M:Crudible.Extensions.ServiceCollectionExtensions.AddCrudibleDbContext(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.String)">
            <summary>
            Add Crudible database context
            </summary>
            <param name="services">Service collection</param>
            <param name="connectionString">Database connection string</param>
            <returns>Service collection</returns>
        </member>
        <member name="M:Crudible.Extensions.ServiceCollectionExtensions.AddCrudibleConfiguration(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{Crudible.Configuration.CrudibleConfiguration})">
            <summary>
            Add Crudible configuration
            </summary>
            <param name="services">Service collection</param>
            <param name="configureOptions">Configuration action</param>
            <returns>Service collection</returns>
        </member>
        <member name="M:Crudible.Extensions.ServiceCollectionExtensions.AddCrudibleAudit(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Add Crudible audit services
            </summary>
            <param name="services">Service collection</param>
            <returns>Service collection</returns>
        </member>
        <member name="M:Crudible.Extensions.ServiceCollectionExtensions.AddCrudibleExport(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Add Crudible export services
            </summary>
            <param name="services">Service collection</param>
            <returns>Service collection</returns>
        </member>
        <member name="M:Crudible.Extensions.ServiceCollectionExtensions.AddCrudibleValidation(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Add Crudible validation services
            </summary>
            <param name="services">Service collection</param>
            <returns>Service collection</returns>
        </member>
        <member name="M:Crudible.Extensions.ServiceCollectionExtensions.AddCrudibleSchemaProcessing(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Add Crudible schema processing services
            </summary>
            <param name="services">Service collection</param>
            <returns>Service collection</returns>
        </member>
        <member name="M:Crudible.Extensions.ServiceCollectionExtensions.AddCrudiblePageGeneration(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Add Crudible page generation services
            </summary>
            <param name="services">Service collection</param>
            <returns>Service collection</returns>
        </member>
        <member name="T:Crudible.Extensions.ApplicationBuilderExtensions">
            <summary>
            Extension methods for IApplicationBuilder
            </summary>
        </member>
        <member name="M:Crudible.Extensions.ApplicationBuilderExtensions.UseCrudible(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            Use Crudible middleware
            </summary>
            <param name="app">Application builder</param>
            <returns>Application builder</returns>
        </member>
        <member name="M:Crudible.Extensions.ApplicationBuilderExtensions.UseCrudibleAudit(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            Use Crudible audit middleware
            </summary>
            <param name="app">Application builder</param>
            <returns>Application builder</returns>
        </member>
        <member name="M:Crudible.Extensions.ApplicationBuilderExtensions.UseCrudibleErrorHandling(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            Use Crudible error handling
            </summary>
            <param name="app">Application builder</param>
            <returns>Application builder</returns>
        </member>
        <member name="M:Crudible.Extensions.ApplicationBuilderExtensions.InitializeCrudibleDatabase(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            Initialize Crudible database
            </summary>
            <param name="app">Application builder</param>
            <returns>Application builder</returns>
        </member>
        <member name="T:Crudible.Middleware.AuditMiddleware">
            <summary>
            Middleware for automatic audit logging of HTTP requests
            </summary>
        </member>
        <member name="T:Crudible.Middleware.ErrorHandlingMiddleware">
            <summary>
            Middleware for handling errors and logging them
            </summary>
        </member>
        <member name="T:Crudible.Models.AuditEntry">
            <summary>
            Represents an audit log entry
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditEntry.Id">
            <summary>
            Unique identifier for the audit entry
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditEntry.TableName">
            <summary>
            Table name that was affected
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditEntry.RecordId">
            <summary>
            Record ID that was affected
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditEntry.Operation">
            <summary>
            Type of operation performed
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditEntry.UserId">
            <summary>
            User who performed the operation
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditEntry.UserName">
            <summary>
            Username who performed the operation
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditEntry.Timestamp">
            <summary>
            Timestamp when the operation occurred
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditEntry.IpAddress">
            <summary>
            IP address of the user
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditEntry.UserAgent">
            <summary>
            User agent string
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditEntry.OldValues">
            <summary>
            Old values (JSON format)
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditEntry.NewValues">
            <summary>
            New values (JSON format)
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditEntry.ChangedFields">
            <summary>
            Changed fields
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditEntry.Metadata">
            <summary>
            Additional metadata
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditEntry.Success">
            <summary>
            Success status of the operation
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditEntry.ErrorMessage">
            <summary>
            Error message if operation failed
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditEntry.DurationMs">
            <summary>
            Duration of the operation in milliseconds
            </summary>
        </member>
        <member name="T:Crudible.Models.AuditOperation">
            <summary>
            Audit operation types
            </summary>
        </member>
        <member name="T:Crudible.Models.AuditSummary">
            <summary>
            Audit summary for reporting
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditSummary.TableName">
            <summary>
            Table name
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditSummary.TotalOperations">
            <summary>
            Total number of operations
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditSummary.CreateOperations">
            <summary>
            Number of create operations
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditSummary.ReadOperations">
            <summary>
            Number of read operations
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditSummary.UpdateOperations">
            <summary>
            Number of update operations
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditSummary.DeleteOperations">
            <summary>
            Number of delete operations
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditSummary.FailedOperations">
            <summary>
            Number of failed operations
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditSummary.StartDate">
            <summary>
            Date range start
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditSummary.EndDate">
            <summary>
            Date range end
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditSummary.TopUsers">
            <summary>
            Most active users
            </summary>
        </member>
        <member name="T:Crudible.Models.UserActivity">
            <summary>
            User activity summary
            </summary>
        </member>
        <member name="P:Crudible.Models.UserActivity.UserId">
            <summary>
            User ID
            </summary>
        </member>
        <member name="P:Crudible.Models.UserActivity.UserName">
            <summary>
            Username
            </summary>
        </member>
        <member name="P:Crudible.Models.UserActivity.OperationCount">
            <summary>
            Number of operations
            </summary>
        </member>
        <member name="P:Crudible.Models.UserActivity.LastActivity">
            <summary>
            Last activity timestamp
            </summary>
        </member>
        <member name="T:Crudible.Models.AuditFilter">
            <summary>
            Audit filter for querying audit entries
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditFilter.TableName">
            <summary>
            Table name filter
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditFilter.RecordId">
            <summary>
            Record ID filter
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditFilter.UserId">
            <summary>
            User ID filter
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditFilter.Operation">
            <summary>
            Operation type filter
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditFilter.StartDate">
            <summary>
            Start date filter
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditFilter.EndDate">
            <summary>
            End date filter
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditFilter.Success">
            <summary>
            Success status filter
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditFilter.Page">
            <summary>
            Page number for pagination
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditFilter.PageSize">
            <summary>
            Page size for pagination
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditFilter.SortField">
            <summary>
            Sort field
            </summary>
        </member>
        <member name="P:Crudible.Models.AuditFilter.SortDirection">
            <summary>
            Sort direction
            </summary>
        </member>
        <member name="T:Crudible.Models.BreadcrumbItem">
            <summary>
            Represents a breadcrumb navigation item
            </summary>
        </member>
        <member name="P:Crudible.Models.BreadcrumbItem.Title">
            <summary>
            Display title of the breadcrumb
            </summary>
        </member>
        <member name="P:Crudible.Models.BreadcrumbItem.Url">
            <summary>
            URL for the breadcrumb link
            </summary>
        </member>
        <member name="P:Crudible.Models.BreadcrumbItem.IsActive">
            <summary>
            Whether this is the active (current) breadcrumb
            </summary>
        </member>
        <member name="P:Crudible.Models.BreadcrumbItem.Icon">
            <summary>
            Icon class for the breadcrumb (optional)
            </summary>
        </member>
        <member name="M:Crudible.Models.BreadcrumbItem.#ctor(System.String,System.String,System.Boolean,System.String)">
            <summary>
            Create a new breadcrumb item
            </summary>
            <param name="title">Display title</param>
            <param name="url">URL (null for active item)</param>
            <param name="isActive">Whether this is the active item</param>
            <param name="icon">Optional icon class</param>
        </member>
        <member name="M:Crudible.Models.BreadcrumbItem.#ctor">
            <summary>
            Create a new breadcrumb item (parameterless constructor for model binding)
            </summary>
        </member>
        <member name="T:Crudible.Models.CrudPageSet">
            <summary>
            Represents a complete set of CRUD pages for a table
            </summary>
        </member>
        <member name="P:Crudible.Models.CrudPageSet.TableDefinition">
            <summary>
            Table definition
            </summary>
        </member>
        <member name="P:Crudible.Models.CrudPageSet.ListPageContent">
            <summary>
            List page content
            </summary>
        </member>
        <member name="P:Crudible.Models.CrudPageSet.CreatePageContent">
            <summary>
            Create page content
            </summary>
        </member>
        <member name="P:Crudible.Models.CrudPageSet.EditPageContent">
            <summary>
            Edit page content
            </summary>
        </member>
        <member name="P:Crudible.Models.CrudPageSet.DetailsPageContent">
            <summary>
            Details page content
            </summary>
        </member>
        <member name="P:Crudible.Models.CrudPageSet.DeletePageContent">
            <summary>
            Delete page content
            </summary>
        </member>
        <member name="P:Crudible.Models.CrudPageSet.PageModelContent">
            <summary>
            Page model class content
            </summary>
        </member>
        <member name="P:Crudible.Models.CrudPageSet.EntityModelContent">
            <summary>
            Entity model class content
            </summary>
        </member>
        <member name="P:Crudible.Models.CrudPageSet.GeneratedAt">
            <summary>
            Generation timestamp
            </summary>
        </member>
        <member name="T:Crudible.Models.SchemaValidationResult">
            <summary>
            Schema validation result
            </summary>
        </member>
        <member name="P:Crudible.Models.SchemaValidationResult.IsValid">
            <summary>
            Whether the schema is valid
            </summary>
        </member>
        <member name="P:Crudible.Models.SchemaValidationResult.Errors">
            <summary>
            Validation errors
            </summary>
        </member>
        <member name="P:Crudible.Models.SchemaValidationResult.Warnings">
            <summary>
            Validation warnings
            </summary>
        </member>
        <member name="P:Crudible.Models.SchemaValidationResult.TableCount">
            <summary>
            Number of tables found
            </summary>
        </member>
        <member name="P:Crudible.Models.SchemaValidationResult.RelationshipCount">
            <summary>
            Number of relationships found
            </summary>
        </member>
        <member name="T:Crudible.Models.TableRelationship">
            <summary>
            Table relationship definition
            </summary>
        </member>
        <member name="P:Crudible.Models.TableRelationship.Name">
            <summary>
            Relationship name
            </summary>
        </member>
        <member name="P:Crudible.Models.TableRelationship.ParentTable">
            <summary>
            Parent table name
            </summary>
        </member>
        <member name="P:Crudible.Models.TableRelationship.ChildTable">
            <summary>
            Child table name
            </summary>
        </member>
        <member name="P:Crudible.Models.TableRelationship.ParentFields">
            <summary>
            Parent field names
            </summary>
        </member>
        <member name="P:Crudible.Models.TableRelationship.ChildFields">
            <summary>
            Child field names
            </summary>
        </member>
        <member name="P:Crudible.Models.TableRelationship.RelationshipType">
            <summary>
            Relationship type
            </summary>
        </member>
        <member name="P:Crudible.Models.TableRelationship.ShowInParentDetails">
            <summary>
            Whether to show child records in parent details
            </summary>
        </member>
        <member name="P:Crudible.Models.TableRelationship.DisplayName">
            <summary>
            Display name for the relationship
            </summary>
        </member>
        <member name="T:Crudible.Models.ValidationResult">
            <summary>
            Validation result for entities
            </summary>
        </member>
        <member name="P:Crudible.Models.ValidationResult.IsValid">
            <summary>
            Whether validation passed
            </summary>
        </member>
        <member name="P:Crudible.Models.ValidationResult.Errors">
            <summary>
            Validation errors
            </summary>
        </member>
        <member name="M:Crudible.Models.ValidationResult.AddError(System.String,System.String)">
            <summary>
            Add an error
            </summary>
            <param name="fieldName">Field name</param>
            <param name="errorMessage">Error message</param>
        </member>
        <member name="T:Crudible.Models.ValidationError">
            <summary>
            Validation error
            </summary>
        </member>
        <member name="P:Crudible.Models.ValidationError.FieldName">
            <summary>
            Field name that has the error
            </summary>
        </member>
        <member name="P:Crudible.Models.ValidationError.ErrorMessage">
            <summary>
            Error message
            </summary>
        </member>
        <member name="P:Crudible.Models.ValidationError.ErrorCode">
            <summary>
            Error code
            </summary>
        </member>
        <member name="T:Crudible.Models.PagedResult`1">
            <summary>
            Paged result for list views
            </summary>
            <typeparam name="T">Type of items</typeparam>
        </member>
        <member name="P:Crudible.Models.PagedResult`1.Items">
            <summary>
            Items in the current page
            </summary>
        </member>
        <member name="P:Crudible.Models.PagedResult`1.TotalItems">
            <summary>
            Total number of items
            </summary>
        </member>
        <member name="P:Crudible.Models.PagedResult`1.CurrentPage">
            <summary>
            Current page number
            </summary>
        </member>
        <member name="P:Crudible.Models.PagedResult`1.PageSize">
            <summary>
            Page size
            </summary>
        </member>
        <member name="P:Crudible.Models.PagedResult`1.TotalPages">
            <summary>
            Total number of pages
            </summary>
        </member>
        <member name="P:Crudible.Models.PagedResult`1.HasPreviousPage">
            <summary>
            Whether there is a previous page
            </summary>
        </member>
        <member name="P:Crudible.Models.PagedResult`1.HasNextPage">
            <summary>
            Whether there is a next page
            </summary>
        </member>
        <member name="T:Crudible.Models.ListFilter">
            <summary>
            Filter criteria for list views
            </summary>
        </member>
        <member name="P:Crudible.Models.ListFilter.SearchTerm">
            <summary>
            Search term
            </summary>
        </member>
        <member name="P:Crudible.Models.ListFilter.FieldFilters">
            <summary>
            Field-specific filters
            </summary>
        </member>
        <member name="P:Crudible.Models.ListFilter.SortField">
            <summary>
            Sort field
            </summary>
        </member>
        <member name="P:Crudible.Models.ListFilter.SortDirection">
            <summary>
            Sort direction
            </summary>
        </member>
        <member name="P:Crudible.Models.ListFilter.Page">
            <summary>
            Page number
            </summary>
        </member>
        <member name="P:Crudible.Models.ListFilter.PageSize">
            <summary>
            Page size
            </summary>
        </member>
        <member name="T:Crudible.Models.ExportFormat">
            <summary>
            Export format enumeration
            </summary>
        </member>
        <member name="T:Crudible.Models.ExportRequest">
            <summary>
            Export request
            </summary>
        </member>
        <member name="P:Crudible.Models.ExportRequest.TableName">
            <summary>
            Table name to export
            </summary>
        </member>
        <member name="P:Crudible.Models.ExportRequest.Format">
            <summary>
            Export format
            </summary>
        </member>
        <member name="P:Crudible.Models.ExportRequest.Filter">
            <summary>
            Filter criteria
            </summary>
        </member>
        <member name="P:Crudible.Models.ExportRequest.IncludeFields">
            <summary>
            Fields to include in export
            </summary>
        </member>
        <member name="P:Crudible.Models.ExportRequest.FileName">
            <summary>
            File name
            </summary>
        </member>
        <member name="T:Crudible.Models.ConfigurationUpdateRequest">
            <summary>
            Configuration update request
            </summary>
        </member>
        <member name="P:Crudible.Models.ConfigurationUpdateRequest.Section">
            <summary>
            Configuration section to update
            </summary>
        </member>
        <member name="P:Crudible.Models.ConfigurationUpdateRequest.Values">
            <summary>
            Configuration values
            </summary>
        </member>
        <member name="P:Crudible.Models.ConfigurationUpdateRequest.UserId">
            <summary>
            User making the change
            </summary>
        </member>
        <member name="T:Crudible.Models.RelationshipType">
            <summary>
            Relationship types
            </summary>
        </member>
        <member name="T:Crudible.Models.TableDefinition">
            <summary>
            Represents a database table definition
            </summary>
        </member>
        <member name="P:Crudible.Models.TableDefinition.Name">
            <summary>
            Table name
            </summary>
        </member>
        <member name="P:Crudible.Models.TableDefinition.Schema">
            <summary>
            Schema name (if applicable)
            </summary>
        </member>
        <member name="P:Crudible.Models.TableDefinition.DisplayName">
            <summary>
            Display name for UI
            </summary>
        </member>
        <member name="P:Crudible.Models.TableDefinition.Description">
            <summary>
            Table description
            </summary>
        </member>
        <member name="P:Crudible.Models.TableDefinition.Fields">
            <summary>
            List of field definitions
            </summary>
        </member>
        <member name="P:Crudible.Models.TableDefinition.PrimaryKeys">
            <summary>
            Primary key field names
            </summary>
        </member>
        <member name="P:Crudible.Models.TableDefinition.ForeignKeys">
            <summary>
            Foreign key relationships
            </summary>
        </member>
        <member name="P:Crudible.Models.TableDefinition.Indexes">
            <summary>
            Indexes defined on the table
            </summary>
        </member>
        <member name="P:Crudible.Models.TableDefinition.IncludeInCrud">
            <summary>
            Whether this table should be included in CRUD generation
            </summary>
        </member>
        <member name="P:Crudible.Models.TableDefinition.CustomValidationRules">
            <summary>
            Custom validation rules for this table
            </summary>
        </member>
        <member name="P:Crudible.Models.TableDefinition.UIConfiguration">
            <summary>
            UI configuration for this table
            </summary>
        </member>
        <member name="T:Crudible.Models.FieldDefinition">
            <summary>
            Represents a database field/column definition
            </summary>
        </member>
        <member name="P:Crudible.Models.FieldDefinition.Name">
            <summary>
            Field name
            </summary>
        </member>
        <member name="P:Crudible.Models.FieldDefinition.DisplayName">
            <summary>
            Display name for UI
            </summary>
        </member>
        <member name="P:Crudible.Models.FieldDefinition.Description">
            <summary>
            Field description
            </summary>
        </member>
        <member name="P:Crudible.Models.FieldDefinition.DataType">
            <summary>
            Data type
            </summary>
        </member>
        <member name="P:Crudible.Models.FieldDefinition.DotNetType">
            <summary>
            .NET type name
            </summary>
        </member>
        <member name="P:Crudible.Models.FieldDefinition.SqlDataType">
            <summary>
            SQL data type
            </summary>
        </member>
        <member name="P:Crudible.Models.FieldDefinition.MaxLength">
            <summary>
            Maximum length (for string types)
            </summary>
        </member>
        <member name="P:Crudible.Models.FieldDefinition.Precision">
            <summary>
            Precision (for decimal types)
            </summary>
        </member>
        <member name="P:Crudible.Models.FieldDefinition.Scale">
            <summary>
            Scale (for decimal types)
            </summary>
        </member>
        <member name="P:Crudible.Models.FieldDefinition.IsNullable">
            <summary>
            Whether the field allows null values
            </summary>
        </member>
        <member name="P:Crudible.Models.FieldDefinition.IsPrimaryKey">
            <summary>
            Whether this is a primary key field
            </summary>
        </member>
        <member name="P:Crudible.Models.FieldDefinition.IsIdentity">
            <summary>
            Whether this is an identity/auto-increment field
            </summary>
        </member>
        <member name="P:Crudible.Models.FieldDefinition.IsUnique">
            <summary>
            Whether this field is unique
            </summary>
        </member>
        <member name="P:Crudible.Models.FieldDefinition.DefaultValue">
            <summary>
            Default value
            </summary>
        </member>
        <member name="P:Crudible.Models.FieldDefinition.ForeignKeyReference">
            <summary>
            Foreign key reference (if applicable)
            </summary>
        </member>
        <member name="P:Crudible.Models.FieldDefinition.UIConfiguration">
            <summary>
            UI configuration for this field
            </summary>
        </member>
        <member name="P:Crudible.Models.FieldDefinition.ValidationRules">
            <summary>
            Validation rules for this field
            </summary>
        </member>
        <member name="T:Crudible.Models.FieldValidationRule">
            <summary>
            Validation rule for a field
            </summary>
        </member>
        <member name="T:Crudible.Models.ForeignKeyDefinition">
            <summary>
            Foreign key definition
            </summary>
        </member>
        <member name="P:Crudible.Models.ForeignKeyDefinition.Name">
            <summary>
            Foreign key name
            </summary>
        </member>
        <member name="P:Crudible.Models.ForeignKeyDefinition.LocalFields">
            <summary>
            Local field names
            </summary>
        </member>
        <member name="P:Crudible.Models.ForeignKeyDefinition.ReferencedTable">
            <summary>
            Referenced table name
            </summary>
        </member>
        <member name="P:Crudible.Models.ForeignKeyDefinition.ReferencedFields">
            <summary>
            Referenced field names
            </summary>
        </member>
        <member name="P:Crudible.Models.ForeignKeyDefinition.OnDelete">
            <summary>
            Delete action
            </summary>
        </member>
        <member name="P:Crudible.Models.ForeignKeyDefinition.OnUpdate">
            <summary>
            Update action
            </summary>
        </member>
        <member name="T:Crudible.Models.ForeignKeyReference">
            <summary>
            Foreign key reference for a field
            </summary>
        </member>
        <member name="P:Crudible.Models.ForeignKeyReference.ReferencedTable">
            <summary>
            Referenced table name
            </summary>
        </member>
        <member name="P:Crudible.Models.ForeignKeyReference.ReferencedField">
            <summary>
            Referenced field name
            </summary>
        </member>
        <member name="P:Crudible.Models.ForeignKeyReference.DisplayField">
            <summary>
            Display field name (for dropdowns)
            </summary>
        </member>
        <member name="T:Crudible.Models.IndexDefinition">
            <summary>
            Index definition
            </summary>
        </member>
        <member name="P:Crudible.Models.IndexDefinition.Name">
            <summary>
            Index name
            </summary>
        </member>
        <member name="P:Crudible.Models.IndexDefinition.Fields">
            <summary>
            Field names in the index
            </summary>
        </member>
        <member name="P:Crudible.Models.IndexDefinition.IsUnique">
            <summary>
            Whether this is a unique index
            </summary>
        </member>
        <member name="P:Crudible.Models.IndexDefinition.IsClustered">
            <summary>
            Whether this is a clustered index
            </summary>
        </member>
        <member name="T:Crudible.Models.TableUIConfiguration">
            <summary>
            Table UI configuration
            </summary>
        </member>
        <member name="P:Crudible.Models.TableUIConfiguration.Icon">
            <summary>
            Icon for the table
            </summary>
        </member>
        <member name="P:Crudible.Models.TableUIConfiguration.ColorTheme">
            <summary>
            Color theme
            </summary>
        </member>
        <member name="P:Crudible.Models.TableUIConfiguration.ListViewFields">
            <summary>
            Fields to display in list view
            </summary>
        </member>
        <member name="P:Crudible.Models.TableUIConfiguration.DefaultSortField">
            <summary>
            Default sort field
            </summary>
        </member>
        <member name="P:Crudible.Models.TableUIConfiguration.DefaultSortDirection">
            <summary>
            Default sort direction
            </summary>
        </member>
        <member name="P:Crudible.Models.TableUIConfiguration.ItemsPerPage">
            <summary>
            Items per page
            </summary>
        </member>
        <member name="T:Crudible.Models.FieldUIConfiguration">
            <summary>
            Field UI configuration
            </summary>
        </member>
        <member name="P:Crudible.Models.FieldUIConfiguration.InputType">
            <summary>
            Input type for forms
            </summary>
        </member>
        <member name="P:Crudible.Models.FieldUIConfiguration.ShowInList">
            <summary>
            Whether to show in list view
            </summary>
        </member>
        <member name="P:Crudible.Models.FieldUIConfiguration.ShowInCreate">
            <summary>
            Whether to show in create form
            </summary>
        </member>
        <member name="P:Crudible.Models.FieldUIConfiguration.ShowInEdit">
            <summary>
            Whether to show in edit form
            </summary>
        </member>
        <member name="P:Crudible.Models.FieldUIConfiguration.ShowInDetails">
            <summary>
            Whether to show in details view
            </summary>
        </member>
        <member name="P:Crudible.Models.FieldUIConfiguration.Order">
            <summary>
            Field order in forms
            </summary>
        </member>
        <member name="P:Crudible.Models.FieldUIConfiguration.CssClasses">
            <summary>
            CSS classes for styling
            </summary>
        </member>
        <member name="P:Crudible.Models.FieldUIConfiguration.Placeholder">
            <summary>
            Placeholder text
            </summary>
        </member>
        <member name="P:Crudible.Models.FieldUIConfiguration.HelpText">
            <summary>
            Help text
            </summary>
        </member>
        <member name="T:Crudible.Models.FieldDataType">
            <summary>
            Field data types
            </summary>
        </member>
        <member name="T:Crudible.Models.ForeignKeyAction">
            <summary>
            Foreign key actions
            </summary>
        </member>
        <member name="T:Crudible.Models.SortDirection">
            <summary>
            Sort directions
            </summary>
        </member>
        <member name="T:Crudible.Models.InputType">
            <summary>
            Input types for UI
            </summary>
        </member>
        <member name="T:Crudible.Pages.Shared.Components.SmartInput.SmartInputViewComponent">
            <summary>
            View component for rendering smart input controls based on field definitions
            </summary>
        </member>
        <member name="M:Crudible.Pages.Shared.Components.SmartInput.SmartInputViewComponent.InvokeAsync(Crudible.Models.FieldDefinition,System.Object,System.String,System.Boolean)">
            <summary>
            Invoke the smart input component
            </summary>
            <param name="field">Field definition</param>
            <param name="value">Current value</param>
            <param name="propertyName">Property name for model binding</param>
            <param name="isReadOnly">Whether the input is read-only</param>
            <returns>View component result</returns>
        </member>
        <member name="T:Crudible.Pages.Shared.Components.SmartInput.SmartInputViewModel">
            <summary>
            View model for smart input component
            </summary>
        </member>
        <member name="T:Crudible.Pages.Shared.Components.SmartInput.SmartInputType">
            <summary>
            Smart input types
            </summary>
        </member>
        <member name="T:Crudible.Pages.Shared.Components.SmartInput.SelectOption">
            <summary>
            Select option for dropdown lists
            </summary>
        </member>
        <member name="T:Crudible.Services.AuditService">
            <summary>
            Service for managing audit trail functionality
            </summary>
        </member>
        <member name="M:Crudible.Services.AuditService.LogAuditEntryAsync(Crudible.Models.AuditEntry)">
            <summary>
            Log an audit entry
            </summary>
            <param name="entry">Audit entry to log</param>
        </member>
        <member name="M:Crudible.Services.AuditService.GetAuditEntriesAsync(System.String,System.String)">
            <summary>
            Get audit entries for a specific table and record
            </summary>
            <param name="tableName">Table name</param>
            <param name="recordId">Record ID</param>
            <returns>List of audit entries</returns>
        </member>
        <member name="M:Crudible.Services.AuditService.GetAuditEntriesAsync(System.DateTime,System.DateTime)">
            <summary>
            Get audit entries for a date range
            </summary>
            <param name="startDate">Start date</param>
            <param name="endDate">End date</param>
            <returns>List of audit entries</returns>
        </member>
        <member name="M:Crudible.Services.AuditService.GetAuditEntriesAsync(Crudible.Models.AuditFilter)">
            <summary>
            Get paged audit entries with filtering
            </summary>
            <param name="filter">Audit filter criteria</param>
            <returns>Paged result of audit entries</returns>
        </member>
        <member name="M:Crudible.Services.AuditService.GetAuditSummaryAsync(System.DateTime,System.DateTime)">
            <summary>
            Get audit summary for reporting
            </summary>
            <param name="startDate">Start date</param>
            <param name="endDate">End date</param>
            <returns>Audit summary</returns>
        </member>
        <member name="M:Crudible.Services.AuditService.CleanupOldEntriesAsync">
            <summary>
            Clean up old audit entries based on retention policy
            </summary>
        </member>
        <member name="M:Crudible.Services.AuditService.LogCrudOperationAsync(System.String,System.String,Crudible.Models.AuditOperation,System.Object,System.Object,System.Boolean,System.String)">
            <summary>
            Log CRUD operation
            </summary>
            <param name="tableName">Table name</param>
            <param name="recordId">Record ID</param>
            <param name="operation">CRUD operation</param>
            <param name="oldValues">Old values (for updates/deletes)</param>
            <param name="newValues">New values (for creates/updates)</param>
            <param name="success">Whether operation was successful</param>
            <param name="errorMessage">Error message if operation failed</param>
        </member>
        <member name="T:Crudible.Services.ConfigurationService">
            <summary>
            Service for managing application configuration
            </summary>
        </member>
        <member name="M:Crudible.Services.ConfigurationService.GetConfiguration">
            <summary>
            Get current configuration
            </summary>
            <returns>Current configuration</returns>
        </member>
        <member name="M:Crudible.Services.ConfigurationService.UpdateConfigurationAsync(Crudible.Configuration.CrudibleConfiguration)">
            <summary>
            Update configuration
            </summary>
            <param name="configuration">New configuration</param>
            <returns>Success status</returns>
        </member>
        <member name="M:Crudible.Services.ConfigurationService.LoadConfigurationAsync">
            <summary>
            Load configuration from file
            </summary>
            <returns>Configuration or default if file doesn't exist</returns>
        </member>
        <member name="M:Crudible.Services.ConfigurationService.UpdateDatabaseConfigurationAsync(Crudible.Configuration.DatabaseConfiguration)">
            <summary>
            Update database configuration
            </summary>
            <param name="databaseConfig">Database configuration</param>
            <returns>Success status</returns>
        </member>
        <member name="M:Crudible.Services.ConfigurationService.UpdateSchemaConfigurationAsync(Crudible.Configuration.SchemaConfiguration)">
            <summary>
            Update schema configuration
            </summary>
            <param name="schemaConfig">Schema configuration</param>
            <returns>Success status</returns>
        </member>
        <member name="M:Crudible.Services.ConfigurationService.UpdateUIConfigurationAsync(Crudible.Configuration.UIConfiguration)">
            <summary>
            Update UI configuration
            </summary>
            <param name="uiConfig">UI configuration</param>
            <returns>Success status</returns>
        </member>
        <member name="M:Crudible.Services.ConfigurationService.UpdateExportConfigurationAsync(Crudible.Configuration.ExportConfiguration)">
            <summary>
            Update export configuration
            </summary>
            <param name="exportConfig">Export configuration</param>
            <returns>Success status</returns>
        </member>
        <member name="M:Crudible.Services.ConfigurationService.UpdateAuditConfigurationAsync(Crudible.Configuration.AuditConfiguration)">
            <summary>
            Update audit configuration
            </summary>
            <param name="auditConfig">Audit configuration</param>
            <returns>Success status</returns>
        </member>
        <member name="M:Crudible.Services.ConfigurationService.ValidateConfiguration(Crudible.Configuration.CrudibleConfiguration)">
            <summary>
            Validate configuration
            </summary>
            <param name="configuration">Configuration to validate</param>
            <returns>Validation result</returns>
        </member>
        <member name="M:Crudible.Services.ConfigurationService.ResetToDefaultsAsync">
            <summary>
            Reset configuration to defaults
            </summary>
            <returns>Success status</returns>
        </member>
        <member name="M:Crudible.Services.ConfigurationService.ExportConfigurationAsync">
            <summary>
            Export configuration to JSON
            </summary>
            <returns>Configuration as JSON string</returns>
        </member>
        <member name="M:Crudible.Services.ConfigurationService.ImportConfigurationAsync(System.String)">
            <summary>
            Import configuration from JSON
            </summary>
            <param name="json">Configuration JSON</param>
            <returns>Success status</returns>
        </member>
        <member name="T:Crudible.Services.CrudPageGenerator">
            <summary>
            Service for generating CRUD pages dynamically
            </summary>
        </member>
        <member name="M:Crudible.Services.CrudPageGenerator.GenerateCrudPagesAsync(Crudible.Models.TableDefinition)">
            <summary>
            Generate all CRUD pages for a table
            </summary>
            <param name="tableDefinition">Table definition</param>
            <returns>Generated page information</returns>
        </member>
        <member name="M:Crudible.Services.CrudPageGenerator.GenerateListPageAsync(Crudible.Models.TableDefinition)">
            <summary>
            Generate list page for a table
            </summary>
            <param name="tableDefinition">Table definition</param>
            <returns>Generated page content</returns>
        </member>
        <member name="M:Crudible.Services.CrudPageGenerator.GenerateCreatePageAsync(Crudible.Models.TableDefinition)">
            <summary>
            Generate create page for a table
            </summary>
            <param name="tableDefinition">Table definition</param>
            <returns>Generated page content</returns>
        </member>
        <member name="M:Crudible.Services.CrudPageGenerator.GenerateEditPageAsync(Crudible.Models.TableDefinition)">
            <summary>
            Generate edit page for a table
            </summary>
            <param name="tableDefinition">Table definition</param>
            <returns>Generated page content</returns>
        </member>
        <member name="M:Crudible.Services.CrudPageGenerator.GenerateDetailsPageAsync(Crudible.Models.TableDefinition)">
            <summary>
            Generate details page for a table
            </summary>
            <param name="tableDefinition">Table definition</param>
            <returns>Generated page content</returns>
        </member>
        <member name="M:Crudible.Services.CrudPageGenerator.GenerateDeletePageAsync(Crudible.Models.TableDefinition)">
            <summary>
            Generate delete page for a table
            </summary>
            <param name="tableDefinition">Table definition</param>
            <returns>Generated page content</returns>
        </member>
        <member name="M:Crudible.Services.CrudPageGenerator.WritePagesToDiskAsync(Crudible.Models.CrudPageSet)">
            <summary>
            Write generated pages to disk
            </summary>
            <param name="pageSet">Page set to write</param>
        </member>
        <member name="T:Crudible.Services.DatabaseSchemaExtractor">
            <summary>
            Service for extracting schema information from database connections
            </summary>
        </member>
        <member name="M:Crudible.Services.DatabaseSchemaExtractor.ExtractSchemaAsync(System.String)">
            <summary>
            Extract table definitions from database connection string
            </summary>
            <param name="connectionString">Database connection string</param>
            <returns>List of table definitions</returns>
        </member>
        <member name="M:Crudible.Services.DatabaseSchemaExtractor.TestConnectionAsync(System.String)">
            <summary>
            Test database connection
            </summary>
            <param name="connectionString">Connection string to test</param>
            <returns>Connection test result</returns>
        </member>
        <member name="T:Crudible.Services.DatabaseService">
            <summary>
            Service for managing database operations with dynamic schemas
            </summary>
        </member>
        <member name="M:Crudible.Services.DatabaseService.GetDbContextAsync(System.Collections.Generic.List{Crudible.Models.TableDefinition},System.String)">
            <summary>
            Get or create a dynamic DbContext for the given tables
            </summary>
            <param name="tables">Table definitions</param>
            <param name="connectionString">Database connection string</param>
            <returns>DbContext instance</returns>
        </member>
        <member name="M:Crudible.Services.DatabaseService.GetTableDataAsync(System.String,System.Collections.Generic.List{Crudible.Models.TableDefinition},System.String,Crudible.Models.ListFilter)">
            <summary>
            Get all records from a table
            </summary>
            <param name="tableName">Table name</param>
            <param name="tables">Table definitions</param>
            <param name="connectionString">Database connection string</param>
            <param name="filter">Optional filter criteria</param>
            <returns>Paged result of records</returns>
        </member>
        <member name="M:Crudible.Services.DatabaseService.GetRecordByIdAsync(System.String,System.Object,System.Collections.Generic.List{Crudible.Models.TableDefinition},System.String)">
            <summary>
            Get a single record by ID
            </summary>
            <param name="tableName">Table name</param>
            <param name="id">Record ID</param>
            <param name="tables">Table definitions</param>
            <param name="connectionString">Database connection string</param>
            <returns>Record or null if not found</returns>
        </member>
        <member name="M:Crudible.Services.DatabaseService.CreateRecordAsync(System.String,System.Collections.Generic.Dictionary{System.String,System.Object},System.Collections.Generic.List{Crudible.Models.TableDefinition},System.String)">
            <summary>
            Create a new record
            </summary>
            <param name="tableName">Table name</param>
            <param name="values">Field values</param>
            <param name="tables">Table definitions</param>
            <param name="connectionString">Database connection string</param>
            <returns>Created record</returns>
        </member>
        <member name="M:Crudible.Services.DatabaseService.UpdateRecordAsync(System.String,System.Object,System.Collections.Generic.Dictionary{System.String,System.Object},System.Collections.Generic.List{Crudible.Models.TableDefinition},System.String)">
            <summary>
            Update an existing record
            </summary>
            <param name="tableName">Table name</param>
            <param name="id">Record ID</param>
            <param name="values">Updated field values</param>
            <param name="tables">Table definitions</param>
            <param name="connectionString">Database connection string</param>
            <returns>Updated record</returns>
        </member>
        <member name="M:Crudible.Services.DatabaseService.DeleteRecordAsync(System.String,System.Object,System.Collections.Generic.List{Crudible.Models.TableDefinition},System.String)">
            <summary>
            Delete a record
            </summary>
            <param name="tableName">Table name</param>
            <param name="id">Record ID</param>
            <param name="tables">Table definitions</param>
            <param name="connectionString">Database connection string</param>
            <returns>True if deleted, false if not found</returns>
        </member>
        <member name="M:Crudible.Services.DatabaseService.GetRelatedRecordsAsync(System.String,System.Object,System.String,System.String,System.Collections.Generic.List{Crudible.Models.TableDefinition},System.String)">
            <summary>
            Get related records for a foreign key relationship
            </summary>
            <param name="parentTableName">Parent table name</param>
            <param name="parentId">Parent record ID</param>
            <param name="childTableName">Child table name</param>
            <param name="foreignKeyField">Foreign key field name</param>
            <param name="tables">Table definitions</param>
            <param name="connectionString">Database connection string</param>
            <returns>List of related records</returns>
        </member>
        <member name="T:Crudible.Services.DynamicDbContextService">
            <summary>
            Service for creating and managing dynamic DbContext instances from table definitions
            </summary>
        </member>
        <member name="M:Crudible.Services.DynamicDbContextService.CreateDbContextAsync(System.Collections.Generic.List{Crudible.Models.TableDefinition},System.String,System.String)">
            <summary>
            Create a dynamic DbContext from table definitions
            </summary>
            <param name="tables">Table definitions</param>
            <param name="connectionString">Database connection string</param>
            <param name="contextName">Name for the DbContext class</param>
            <returns>DbContext instance</returns>
        </member>
        <member name="M:Crudible.Services.DynamicDbContextService.GetEntityType(Microsoft.EntityFrameworkCore.DbContext,System.String)">
            <summary>
            Get entity type for a table from the dynamic DbContext
            </summary>
            <param name="context">DbContext instance</param>
            <param name="tableName">Table name</param>
            <returns>Entity type</returns>
        </member>
        <member name="M:Crudible.Services.DynamicDbContextService.GetDbSetProperty(Microsoft.EntityFrameworkCore.DbContext,System.String)">
            <summary>
            Get DbSet property for a table from the dynamic DbContext
            </summary>
            <param name="context">DbContext instance</param>
            <param name="tableName">Table name</param>
            <returns>DbSet property info</returns>
        </member>
        <member name="M:Crudible.Services.DynamicDbContextService.QueryTableAsync(Microsoft.EntityFrameworkCore.DbContext,System.String,Crudible.Models.ListFilter)">
            <summary>
            Execute a query against a dynamic DbContext
            </summary>
            <param name="context">DbContext instance</param>
            <param name="tableName">Table name</param>
            <param name="filter">Optional filter criteria</param>
            <returns>Query results</returns>
        </member>
        <member name="M:Crudible.Services.DynamicDbContextService.CreateEntityAsync(Microsoft.EntityFrameworkCore.DbContext,System.String,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Create a new entity instance for a table
            </summary>
            <param name="context">DbContext instance</param>
            <param name="tableName">Table name</param>
            <param name="values">Property values</param>
            <returns>Created entity</returns>
        </member>
        <member name="T:Crudible.Services.EntityModelGenerator">
            <summary>
            Service for generating Entity Framework Core model classes from table definitions
            </summary>
        </member>
        <member name="M:Crudible.Services.EntityModelGenerator.GenerateEntityModel(Crudible.Models.TableDefinition,System.String)">
            <summary>
            Generate entity model class code for a table
            </summary>
            <param name="table">Table definition</param>
            <param name="namespaceName">Namespace for the generated class</param>
            <returns>Generated C# class code</returns>
        </member>
        <member name="M:Crudible.Services.EntityModelGenerator.GenerateDbContextConfiguration(Crudible.Models.TableDefinition)">
            <summary>
            Generate DbContext configuration for a table
            </summary>
            <param name="table">Table definition</param>
            <returns>DbContext configuration code</returns>
        </member>
        <member name="M:Crudible.Services.EntityModelGenerator.GenerateDbContext(System.Collections.Generic.List{Crudible.Models.TableDefinition},System.String,System.String)">
            <summary>
            Generate complete DbContext class
            </summary>
            <param name="tables">List of table definitions</param>
            <param name="contextName">Name of the DbContext class</param>
            <param name="namespaceName">Namespace for the generated class</param>
            <returns>Generated DbContext class code</returns>
        </member>
        <member name="T:Crudible.Services.ExportService">
            <summary>
            Service for exporting data in various formats
            </summary>
        </member>
        <member name="M:Crudible.Services.ExportService.ExportToExcelAsync(Crudible.Models.ExportRequest)">
            <summary>
            Export table data to Excel format
            </summary>
            <param name="request">Export request</param>
            <returns>Excel file as byte array</returns>
        </member>
        <member name="M:Crudible.Services.ExportService.ExportToCsvAsync(Crudible.Models.ExportRequest)">
            <summary>
            Export table data to CSV format
            </summary>
            <param name="request">Export request</param>
            <returns>CSV file as byte array</returns>
        </member>
        <member name="M:Crudible.Services.ExportService.ExportToJsonAsync(Crudible.Models.ExportRequest)">
            <summary>
            Export table data to JSON format
            </summary>
            <param name="request">Export request</param>
            <returns>JSON file as byte array</returns>
        </member>
        <member name="M:Crudible.Services.ExportService.GetExportFileNameAsync(System.String,Crudible.Models.ExportFormat)">
            <summary>
            Get export file name based on template
            </summary>
            <param name="tableName">Table name</param>
            <param name="format">Export format</param>
            <returns>Generated file name</returns>
        </member>
        <member name="T:Crudible.Services.FileService">
            <summary>
            Service for file operations
            </summary>
        </member>
        <member name="M:Crudible.Services.FileService.SaveFileAsync(System.String,System.String,System.String)">
            <summary>
            Save file to disk
            </summary>
            <param name="fileName">File name</param>
            <param name="content">File content</param>
            <param name="directory">Directory path (relative to wwwroot)</param>
            <returns>Full file path</returns>
        </member>
        <member name="M:Crudible.Services.FileService.SaveFileAsync(System.String,System.Byte[],System.String)">
            <summary>
            Save binary file to disk
            </summary>
            <param name="fileName">File name</param>
            <param name="content">File content</param>
            <param name="directory">Directory path (relative to wwwroot)</param>
            <returns>Full file path</returns>
        </member>
        <member name="M:Crudible.Services.FileService.ReadFileAsync(System.String)">
            <summary>
            Read file from disk
            </summary>
            <param name="filePath">File path</param>
            <returns>File content</returns>
        </member>
        <member name="M:Crudible.Services.FileService.ReadBinaryFileAsync(System.String)">
            <summary>
            Read binary file from disk
            </summary>
            <param name="filePath">File path</param>
            <returns>File content</returns>
        </member>
        <member name="M:Crudible.Services.FileService.DeleteFile(System.String)">
            <summary>
            Delete file from disk
            </summary>
            <param name="filePath">File path</param>
        </member>
        <member name="M:Crudible.Services.FileService.FileExists(System.String)">
            <summary>
            Check if file exists
            </summary>
            <param name="filePath">File path</param>
            <returns>True if file exists</returns>
        </member>
        <member name="M:Crudible.Services.FileService.GetFileSize(System.String)">
            <summary>
            Get file size
            </summary>
            <param name="filePath">File path</param>
            <returns>File size in bytes</returns>
        </member>
        <member name="M:Crudible.Services.FileService.GetFileExtension(System.String)">
            <summary>
            Get file extension
            </summary>
            <param name="fileName">File name</param>
            <returns>File extension</returns>
        </member>
        <member name="M:Crudible.Services.FileService.GenerateUniqueFileName(System.String)">
            <summary>
            Generate unique file name
            </summary>
            <param name="originalFileName">Original file name</param>
            <returns>Unique file name</returns>
        </member>
        <member name="T:Crudible.Services.Interfaces.ISchemaProcessor">
            <summary>
            Interface for processing database schemas from various sources
            </summary>
        </member>
        <member name="M:Crudible.Services.Interfaces.ISchemaProcessor.ProcessXmlSchemaAsync(System.String)">
            <summary>
            Process XML schema and extract table definitions
            </summary>
            <param name="xmlSchema">XML schema content</param>
            <returns>List of table definitions</returns>
        </member>
        <member name="M:Crudible.Services.Interfaces.ISchemaProcessor.ProcessConnectionStringAsync(System.String)">
            <summary>
            Process database connection string and extract table definitions
            </summary>
            <param name="connectionString">Database connection string</param>
            <returns>List of table definitions</returns>
        </member>
        <member name="M:Crudible.Services.Interfaces.ISchemaProcessor.ValidateSchemaAsync(System.String,Crudible.Services.Interfaces.SchemaType)">
            <summary>
            Validate schema format
            </summary>
            <param name="schema">Schema content</param>
            <param name="schemaType">Type of schema (XML or ConnectionString)</param>
            <returns>Validation result</returns>
        </member>
        <member name="M:Crudible.Services.Interfaces.ISchemaProcessor.ExtractRelationshipsAsync(System.Collections.Generic.List{Crudible.Models.TableDefinition})">
            <summary>
            Extract relationships between tables
            </summary>
            <param name="tables">List of table definitions</param>
            <returns>List of relationships</returns>
        </member>
        <member name="T:Crudible.Services.Interfaces.ICrudPageGenerator">
            <summary>
            Interface for generating CRUD pages dynamically
            </summary>
        </member>
        <member name="M:Crudible.Services.Interfaces.ICrudPageGenerator.GenerateCrudPagesAsync(Crudible.Models.TableDefinition)">
            <summary>
            Generate all CRUD pages for a table
            </summary>
            <param name="tableDefinition">Table definition</param>
            <returns>Generated page information</returns>
        </member>
        <member name="M:Crudible.Services.Interfaces.ICrudPageGenerator.GenerateListPageAsync(Crudible.Models.TableDefinition)">
            <summary>
            Generate list page for a table
            </summary>
            <param name="tableDefinition">Table definition</param>
            <returns>Generated page content</returns>
        </member>
        <member name="M:Crudible.Services.Interfaces.ICrudPageGenerator.GenerateCreatePageAsync(Crudible.Models.TableDefinition)">
            <summary>
            Generate create page for a table
            </summary>
            <param name="tableDefinition">Table definition</param>
            <returns>Generated page content</returns>
        </member>
        <member name="M:Crudible.Services.Interfaces.ICrudPageGenerator.GenerateEditPageAsync(Crudible.Models.TableDefinition)">
            <summary>
            Generate edit page for a table
            </summary>
            <param name="tableDefinition">Table definition</param>
            <returns>Generated page content</returns>
        </member>
        <member name="M:Crudible.Services.Interfaces.ICrudPageGenerator.GenerateDetailsPageAsync(Crudible.Models.TableDefinition)">
            <summary>
            Generate details page for a table
            </summary>
            <param name="tableDefinition">Table definition</param>
            <returns>Generated page content</returns>
        </member>
        <member name="M:Crudible.Services.Interfaces.ICrudPageGenerator.GenerateDeletePageAsync(Crudible.Models.TableDefinition)">
            <summary>
            Generate delete page for a table
            </summary>
            <param name="tableDefinition">Table definition</param>
            <returns>Generated page content</returns>
        </member>
        <member name="T:Crudible.Services.Interfaces.IAuditService">
            <summary>
            Interface for audit trail functionality
            </summary>
        </member>
        <member name="M:Crudible.Services.Interfaces.IAuditService.LogAuditEntryAsync(Crudible.Models.AuditEntry)">
            <summary>
            Log an audit entry
            </summary>
            <param name="entry">Audit entry to log</param>
        </member>
        <member name="M:Crudible.Services.Interfaces.IAuditService.GetAuditEntriesAsync(System.String,System.String)">
            <summary>
            Get audit entries for a specific table and record
            </summary>
            <param name="tableName">Table name</param>
            <param name="recordId">Record ID</param>
            <returns>List of audit entries</returns>
        </member>
        <member name="M:Crudible.Services.Interfaces.IAuditService.GetAuditEntriesAsync(System.DateTime,System.DateTime)">
            <summary>
            Get audit entries for a date range
            </summary>
            <param name="startDate">Start date</param>
            <param name="endDate">End date</param>
            <returns>List of audit entries</returns>
        </member>
        <member name="M:Crudible.Services.Interfaces.IAuditService.GetAuditEntriesAsync(Crudible.Models.AuditFilter)">
            <summary>
            Get paged audit entries with filtering
            </summary>
            <param name="filter">Audit filter criteria</param>
            <returns>Paged result of audit entries</returns>
        </member>
        <member name="M:Crudible.Services.Interfaces.IAuditService.GetAuditSummaryAsync(System.DateTime,System.DateTime)">
            <summary>
            Get audit summary for reporting
            </summary>
            <param name="startDate">Start date</param>
            <param name="endDate">End date</param>
            <returns>Audit summary</returns>
        </member>
        <member name="M:Crudible.Services.Interfaces.IAuditService.LogCrudOperationAsync(System.String,System.String,Crudible.Models.AuditOperation,System.Object,System.Object,System.Boolean,System.String)">
            <summary>
            Log CRUD operation
            </summary>
            <param name="tableName">Table name</param>
            <param name="recordId">Record ID</param>
            <param name="operation">CRUD operation</param>
            <param name="oldValues">Old values (for updates/deletes)</param>
            <param name="newValues">New values (for creates/updates)</param>
            <param name="success">Whether operation was successful</param>
            <param name="errorMessage">Error message if operation failed</param>
        </member>
        <member name="M:Crudible.Services.Interfaces.IAuditService.CleanupOldEntriesAsync">
            <summary>
            Clean up old audit entries based on retention policy
            </summary>
        </member>
        <member name="T:Crudible.Services.Interfaces.IExportService">
            <summary>
            Interface for data export functionality
            </summary>
        </member>
        <member name="M:Crudible.Services.Interfaces.IExportService.ExportToExcelAsync(Crudible.Models.ExportRequest)">
            <summary>
            Export table data to Excel format
            </summary>
            <param name="request">Export request</param>
            <returns>Excel file as byte array</returns>
        </member>
        <member name="M:Crudible.Services.Interfaces.IExportService.ExportToCsvAsync(Crudible.Models.ExportRequest)">
            <summary>
            Export table data to CSV format
            </summary>
            <param name="request">Export request</param>
            <returns>CSV file as byte array</returns>
        </member>
        <member name="M:Crudible.Services.Interfaces.IExportService.ExportToJsonAsync(Crudible.Models.ExportRequest)">
            <summary>
            Export table data to JSON format
            </summary>
            <param name="request">Export request</param>
            <returns>JSON file as byte array</returns>
        </member>
        <member name="M:Crudible.Services.Interfaces.IExportService.GetExportFileNameAsync(System.String,Crudible.Models.ExportFormat)">
            <summary>
            Get export file name based on template
            </summary>
            <param name="tableName">Table name</param>
            <param name="format">Export format</param>
            <returns>Generated file name</returns>
        </member>
        <member name="T:Crudible.Services.Interfaces.IValidationService">
            <summary>
            Interface for validation service
            </summary>
        </member>
        <member name="M:Crudible.Services.Interfaces.IValidationService.ValidateEntityAsync(System.Object,Crudible.Models.TableDefinition)">
            <summary>
            Validate entity based on table definition and custom rules
            </summary>
            <param name="entity">Entity to validate</param>
            <param name="tableDefinition">Table definition</param>
            <returns>Validation result</returns>
        </member>
        <member name="M:Crudible.Services.Interfaces.IValidationService.GetValidationAttributes(Crudible.Models.FieldDefinition)">
            <summary>
            Get validation attributes for a field
            </summary>
            <param name="fieldDefinition">Field definition</param>
            <returns>List of validation attributes</returns>
        </member>
        <member name="M:Crudible.Services.Interfaces.IValidationService.ValidateUniqueConstraintsAsync(System.Object,Crudible.Models.TableDefinition)">
            <summary>
            Validate unique constraints
            </summary>
            <param name="entity">Entity to validate</param>
            <param name="tableDefinition">Table definition</param>
            <returns>Validation result</returns>
        </member>
        <member name="T:Crudible.Services.Interfaces.SchemaType">
            <summary>
            Schema type enumeration
            </summary>
        </member>
        <member name="T:Crudible.Services.SchemaProcessor">
            <summary>
            Main schema processor service that coordinates XML and database schema processing
            </summary>
        </member>
        <member name="M:Crudible.Services.SchemaProcessor.ProcessXmlSchemaAsync(System.String)">
            <summary>
            Process XML schema and extract table definitions
            </summary>
            <param name="xmlSchema">XML schema content</param>
            <returns>List of table definitions</returns>
        </member>
        <member name="M:Crudible.Services.SchemaProcessor.ProcessConnectionStringAsync(System.String)">
            <summary>
            Process database connection string and extract table definitions
            </summary>
            <param name="connectionString">Database connection string</param>
            <returns>List of table definitions</returns>
        </member>
        <member name="M:Crudible.Services.SchemaProcessor.ValidateSchemaAsync(System.String,Crudible.Services.Interfaces.SchemaType)">
            <summary>
            Validate schema format
            </summary>
            <param name="schema">Schema content</param>
            <param name="schemaType">Type of schema (XML or ConnectionString)</param>
            <returns>Validation result</returns>
        </member>
        <member name="M:Crudible.Services.SchemaProcessor.ExtractRelationshipsAsync(System.Collections.Generic.List{Crudible.Models.TableDefinition})">
            <summary>
            Extract relationships between tables
            </summary>
            <param name="tables">List of table definitions</param>
            <returns>List of relationships</returns>
        </member>
        <member name="T:Crudible.Services.TemplateService">
            <summary>
            Service for generating Razor page templates
            </summary>
        </member>
        <member name="M:Crudible.Services.TemplateService.GenerateListPageTemplate(Crudible.Models.TableDefinition)">
            <summary>
            Generate list page template
            </summary>
            <param name="table">Table definition</param>
            <returns>Razor page content</returns>
        </member>
        <member name="M:Crudible.Services.TemplateService.GenerateEditPageTemplate(Crudible.Models.TableDefinition)">
            <summary>
            Generate edit page template
            </summary>
            <param name="table">Table definition</param>
            <returns>Razor page content</returns>
        </member>
        <member name="M:Crudible.Services.TemplateService.GenerateDetailsPageTemplate(Crudible.Models.TableDefinition)">
            <summary>
            Generate details page template
            </summary>
            <param name="table">Table definition</param>
            <returns>Razor page content</returns>
        </member>
        <member name="M:Crudible.Services.TemplateService.GenerateDeletePageTemplate(Crudible.Models.TableDefinition)">
            <summary>
            Generate delete page template
            </summary>
            <param name="table">Table definition</param>
            <returns>Razor page content</returns>
        </member>
        <member name="M:Crudible.Services.TemplateService.GenerateCreatePageTemplate(Crudible.Models.TableDefinition)">
            <summary>
            Generate create page template
            </summary>
            <param name="table">Table definition</param>
            <returns>Razor page content</returns>
        </member>
        <member name="T:Crudible.Services.ValidationService">
            <summary>
            Service for validating entities based on table definitions and custom rules
            </summary>
        </member>
        <member name="M:Crudible.Services.ValidationService.ValidateEntityAsync(System.Object,Crudible.Models.TableDefinition)">
            <summary>
            Validate entity based on table definition and custom rules
            </summary>
            <param name="entity">Entity to validate</param>
            <param name="tableDefinition">Table definition</param>
            <returns>Validation result</returns>
        </member>
        <member name="M:Crudible.Services.ValidationService.GetValidationAttributes(Crudible.Models.FieldDefinition)">
            <summary>
            Get validation attributes for a field
            </summary>
            <param name="fieldDefinition">Field definition</param>
            <returns>List of validation attributes</returns>
        </member>
        <member name="M:Crudible.Services.ValidationService.ValidateUniqueConstraintsAsync(System.Object,Crudible.Models.TableDefinition)">
            <summary>
            Validate unique constraints
            </summary>
            <param name="entity">Entity to validate</param>
            <param name="tableDefinition">Table definition</param>
            <returns>Validation result</returns>
        </member>
        <member name="T:Crudible.Services.XmlSchemaParser">
            <summary>
            Service for parsing XML schema definitions
            </summary>
        </member>
        <member name="M:Crudible.Services.XmlSchemaParser.ParseSchemaAsync(System.String)">
            <summary>
            Parse XML schema and extract table definitions
            </summary>
            <param name="xmlContent">XML schema content</param>
            <returns>List of table definitions</returns>
        </member>
        <member name="M:Crudible.Services.XmlSchemaParser.ValidateSchema(System.String)">
            <summary>
            Validate XML schema format
            </summary>
            <param name="xmlContent">XML content to validate</param>
            <returns>Validation result</returns>
        </member>
    </members>
</doc>
