/**
 * Crudible Application JavaScript
 * Main application functionality and UI enhancements
 */

class CrudibleApp {
    constructor() {
        this.init();
    }

    init() {
        document.addEventListener('DOMContentLoaded', () => {
            this.setupEventListeners();
            this.initializeComponents();
            this.setupTooltips();
            this.setupConfirmDialogs();
            this.setupAutoSave();
        });
    }

    setupEventListeners() {
        // Global loading overlay
        this.setupLoadingOverlay();
        
        // Form submissions
        this.setupFormSubmissions();
        
        // Table actions
        this.setupTableActions();
        
        // Search functionality
        this.setupSearch();
        
        // Export functionality
        this.setupExport();
    }

    setupLoadingOverlay() {
        const overlay = document.getElementById('loadingOverlay');
        
        // Show loading on form submissions
        document.addEventListener('submit', (e) => {
            if (!e.target.hasAttribute('data-no-loading')) {
                this.showLoading();
            }
        });

        // Show loading on navigation
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a[href]');
            if (link && !link.hasAttribute('data-no-loading') && 
                !link.href.startsWith('#') && !link.href.startsWith('javascript:')) {
                this.showLoading();
            }
        });

        // Hide loading on page load
        window.addEventListener('load', () => {
            this.hideLoading();
        });
    }

    setupFormSubmissions() {
        const forms = document.querySelectorAll('form');
        
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                // Validate form before submission
                if (!this.validateForm(form)) {
                    e.preventDefault();
                    return false;
                }

                // Show loading for async operations
                if (form.hasAttribute('data-async')) {
                    e.preventDefault();
                    this.submitFormAsync(form);
                }
            });
        });
    }

    setupTableActions() {
        // Bulk actions
        this.setupBulkActions();
        
        // Row selection
        this.setupRowSelection();
        
        // Inline editing
        this.setupInlineEditing();
        
        // Sorting
        this.setupTableSorting();
    }

    setupBulkActions() {
        const selectAllCheckbox = document.querySelector('#selectAll');
        const rowCheckboxes = document.querySelectorAll('.row-checkbox');
        const bulkActionButtons = document.querySelectorAll('.bulk-action');

        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                rowCheckboxes.forEach(checkbox => {
                    checkbox.checked = e.target.checked;
                });
                this.updateBulkActionButtons();
            });
        }

        rowCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                this.updateBulkActionButtons();
                
                // Update select all checkbox
                if (selectAllCheckbox) {
                    const checkedCount = document.querySelectorAll('.row-checkbox:checked').length;
                    selectAllCheckbox.checked = checkedCount === rowCheckboxes.length;
                    selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < rowCheckboxes.length;
                }
            });
        });

        bulkActionButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const selectedIds = this.getSelectedRowIds();
                if (selectedIds.length === 0) {
                    this.showAlert('Please select at least one item.', 'warning');
                    e.preventDefault();
                    return false;
                }

                const action = button.dataset.action;
                if (action === 'delete') {
                    if (!confirm(`Are you sure you want to delete ${selectedIds.length} item(s)?`)) {
                        e.preventDefault();
                        return false;
                    }
                }
            });
        });
    }

    setupRowSelection() {
        const tableRows = document.querySelectorAll('tbody tr[data-id]');
        
        tableRows.forEach(row => {
            row.addEventListener('click', (e) => {
                // Skip if clicking on action buttons or checkboxes
                if (e.target.closest('.btn, input[type="checkbox"], a')) {
                    return;
                }

                // Toggle row selection
                const checkbox = row.querySelector('.row-checkbox');
                if (checkbox) {
                    checkbox.checked = !checkbox.checked;
                    checkbox.dispatchEvent(new Event('change'));
                }
            });
        });
    }

    setupInlineEditing() {
        const editableFields = document.querySelectorAll('[data-editable]');
        
        editableFields.forEach(field => {
            field.addEventListener('dblclick', () => {
                this.enableInlineEdit(field);
            });
        });
    }

    setupTableSorting() {
        const sortableHeaders = document.querySelectorAll('th[data-sortable]');
        
        sortableHeaders.forEach(header => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', () => {
                const field = header.dataset.sortable;
                const currentSort = new URLSearchParams(window.location.search).get('sortField');
                const currentDirection = new URLSearchParams(window.location.search).get('sortDirection');
                
                let newDirection = 'asc';
                if (currentSort === field && currentDirection === 'asc') {
                    newDirection = 'desc';
                }

                const url = new URL(window.location);
                url.searchParams.set('sortField', field);
                url.searchParams.set('sortDirection', newDirection);
                url.searchParams.set('page', '1'); // Reset to first page
                
                window.location.href = url.toString();
            });
        });
    }

    setupSearch() {
        const searchForm = document.querySelector('#searchForm');
        const searchInput = document.querySelector('#searchInput');
        
        if (searchInput) {
            // Debounced search
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    if (searchForm) {
                        searchForm.submit();
                    }
                }, 500);
            });
        }

        // Advanced search toggle
        const advancedSearchToggle = document.querySelector('#advancedSearchToggle');
        const advancedSearchPanel = document.querySelector('#advancedSearchPanel');
        
        if (advancedSearchToggle && advancedSearchPanel) {
            advancedSearchToggle.addEventListener('click', () => {
                advancedSearchPanel.classList.toggle('d-none');
                const icon = advancedSearchToggle.querySelector('i');
                icon.classList.toggle('fa-chevron-down');
                icon.classList.toggle('fa-chevron-up');
            });
        }
    }

    setupExport() {
        const exportButtons = document.querySelectorAll('.export-btn');
        
        exportButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                
                const format = button.dataset.format;
                const table = button.dataset.table;
                const currentFilters = this.getCurrentFilters();
                
                this.exportData(table, format, currentFilters);
            });
        });
    }

    initializeComponents() {
        // Initialize date pickers
        this.initializeDatePickers();
        
        // Initialize select2 for foreign key dropdowns
        this.initializeSelect2();
        
        // Initialize file upload components
        this.initializeFileUploads();
        
        // Initialize rich text editors
        this.initializeRichTextEditors();
    }

    initializeDatePickers() {
        const dateInputs = document.querySelectorAll('input[type="date"], input[type="datetime-local"]');
        
        dateInputs.forEach(input => {
            // Add calendar icon
            const wrapper = document.createElement('div');
            wrapper.className = 'input-group';
            input.parentNode.insertBefore(wrapper, input);
            wrapper.appendChild(input);
            
            const iconSpan = document.createElement('span');
            iconSpan.className = 'input-group-text';
            iconSpan.innerHTML = '<i class="fas fa-calendar-alt"></i>';
            wrapper.appendChild(iconSpan);
        });
    }

    initializeSelect2() {
        // This would initialize Select2 for better dropdown UX
        // For now, just add enhanced styling
        const selects = document.querySelectorAll('select[data-foreign-key]');
        
        selects.forEach(select => {
            select.classList.add('form-select-lg');
        });
    }

    initializeFileUploads() {
        const fileInputs = document.querySelectorAll('input[type="file"]');
        
        fileInputs.forEach(input => {
            // Add drag and drop functionality
            const wrapper = input.closest('.file-input-container') || input.parentElement;
            
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                wrapper.addEventListener(eventName, this.preventDefaults, false);
            });

            ['dragenter', 'dragover'].forEach(eventName => {
                wrapper.addEventListener(eventName, () => wrapper.classList.add('drag-over'), false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                wrapper.addEventListener(eventName, () => wrapper.classList.remove('drag-over'), false);
            });

            wrapper.addEventListener('drop', (e) => {
                const files = e.dataTransfer.files;
                input.files = files;
                input.dispatchEvent(new Event('change'));
            }, false);
        });
    }

    initializeRichTextEditors() {
        const textareas = document.querySelectorAll('textarea[data-rich-text]');
        
        textareas.forEach(textarea => {
            // This would initialize a rich text editor like TinyMCE or CKEditor
            // For now, just add enhanced styling
            textarea.classList.add('rich-text-editor');
        });
    }

    setupTooltips() {
        // Initialize Bootstrap tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(tooltipTriggerEl => {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    setupConfirmDialogs() {
        const confirmButtons = document.querySelectorAll('[data-confirm]');
        
        confirmButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const message = button.dataset.confirm;
                if (!confirm(message)) {
                    e.preventDefault();
                    return false;
                }
            });
        });
    }

    setupAutoSave() {
        const autoSaveForms = document.querySelectorAll('form[data-auto-save]');
        
        autoSaveForms.forEach(form => {
            const inputs = form.querySelectorAll('input, select, textarea');
            let saveTimeout;
            
            inputs.forEach(input => {
                input.addEventListener('input', () => {
                    clearTimeout(saveTimeout);
                    saveTimeout = setTimeout(() => {
                        this.autoSaveForm(form);
                    }, 2000);
                });
            });
        });
    }

    // Utility methods
    showLoading(message = 'Loading...') {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.querySelector('h5').textContent = message;
            overlay.classList.remove('d-none');
        }
    }

    hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.classList.add('d-none');
        }
    }

    showAlert(message, type = 'info') {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                <i class="fas fa-${this.getAlertIcon(type)} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        
        const container = document.querySelector('.container-fluid');
        if (container) {
            container.insertAdjacentHTML('afterbegin', alertHtml);
        }
    }

    getAlertIcon(type) {
        const icons = {
            success: 'check-circle',
            danger: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    validateForm(form) {
        // Use the validation service
        return crudibleValidator ? crudibleValidator.validateForm(form) : true;
    }

    async submitFormAsync(form) {
        try {
            this.showLoading('Saving...');
            
            const formData = new FormData(form);
            const response = await fetch(form.action, {
                method: form.method,
                body: formData
            });

            if (response.ok) {
                const result = await response.json();
                this.showAlert(result.message || 'Operation completed successfully.', 'success');
                
                if (result.redirectUrl) {
                    window.location.href = result.redirectUrl;
                }
            } else {
                throw new Error('Operation failed');
            }
        } catch (error) {
            this.showAlert('An error occurred. Please try again.', 'danger');
        } finally {
            this.hideLoading();
        }
    }

    updateBulkActionButtons() {
        const selectedCount = document.querySelectorAll('.row-checkbox:checked').length;
        const bulkActionButtons = document.querySelectorAll('.bulk-action');
        
        bulkActionButtons.forEach(button => {
            button.disabled = selectedCount === 0;
            const countSpan = button.querySelector('.selected-count');
            if (countSpan) {
                countSpan.textContent = selectedCount;
            }
        });
    }

    getSelectedRowIds() {
        const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');
        return Array.from(checkedBoxes).map(checkbox => checkbox.value);
    }

    getCurrentFilters() {
        const params = new URLSearchParams(window.location.search);
        const filters = {};
        
        for (const [key, value] of params) {
            if (value) {
                filters[key] = value;
            }
        }
        
        return filters;
    }

    async exportData(table, format, filters) {
        try {
            this.showLoading(`Exporting to ${format.toUpperCase()}...`);
            
            const params = new URLSearchParams(filters);
            const url = `/api/export/${table}/${format}?${params}`;
            
            const response = await fetch(url);
            if (response.ok) {
                const blob = await response.blob();
                const downloadUrl = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = downloadUrl;
                a.download = `${table}_${new Date().toISOString().split('T')[0]}.${format}`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(downloadUrl);
                
                this.showAlert('Export completed successfully.', 'success');
            } else {
                throw new Error('Export failed');
            }
        } catch (error) {
            this.showAlert('Export failed. Please try again.', 'danger');
        } finally {
            this.hideLoading();
        }
    }

    enableInlineEdit(field) {
        const currentValue = field.textContent;
        const input = document.createElement('input');
        input.type = 'text';
        input.value = currentValue;
        input.className = 'form-control form-control-sm';
        
        field.innerHTML = '';
        field.appendChild(input);
        input.focus();
        input.select();
        
        const saveEdit = async () => {
            const newValue = input.value;
            if (newValue !== currentValue) {
                try {
                    await this.saveInlineEdit(field, newValue);
                    field.textContent = newValue;
                    this.showAlert('Updated successfully.', 'success');
                } catch (error) {
                    field.textContent = currentValue;
                    this.showAlert('Update failed.', 'danger');
                }
            } else {
                field.textContent = currentValue;
            }
        };
        
        input.addEventListener('blur', saveEdit);
        input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                saveEdit();
            } else if (e.key === 'Escape') {
                field.textContent = currentValue;
            }
        });
    }

    async saveInlineEdit(field, value) {
        const row = field.closest('tr');
        const id = row.dataset.id;
        const fieldName = field.dataset.field;
        const table = field.dataset.table;
        
        const response = await fetch(`/api/tables/${table}/${id}`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                [fieldName]: value
            })
        });
        
        if (!response.ok) {
            throw new Error('Save failed');
        }
    }

    async autoSaveForm(form) {
        try {
            const formData = new FormData(form);
            const response = await fetch(form.action + '?autoSave=true', {
                method: 'POST',
                body: formData
            });
            
            if (response.ok) {
                // Show subtle indication of auto-save
                const indicator = document.createElement('small');
                indicator.className = 'text-muted';
                indicator.textContent = 'Auto-saved';
                form.appendChild(indicator);
                
                setTimeout(() => {
                    indicator.remove();
                }, 2000);
            }
        } catch (error) {
            // Silently fail for auto-save
            console.warn('Auto-save failed:', error);
        }
    }

    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
}

// Initialize the application
const crudibleApp = new CrudibleApp();
