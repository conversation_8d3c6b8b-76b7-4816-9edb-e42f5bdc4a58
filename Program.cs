using Crudible.Configuration;
using Crudible.Data;
using Crudible.Extensions;
using Microsoft.EntityFrameworkCore;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddRazorPages(options =>
{
    options.Conventions.AddPageRoute("/Configuration/Index", "/config");
    options.Conventions.AddPageRoute("/Tables/Index", "/tables");
});

// Add runtime compilation for development
if (builder.Environment.IsDevelopment())
{
    builder.Services.AddRazorPages().AddRazorRuntimeCompilation();
}

// Configure Crudible
builder.Services.Configure<CrudibleConfiguration>(
    builder.Configuration.GetSection("Crudible"));

// Add Crudible services
builder.Services.AddCrudibleServices(builder.Configuration);

// Add database context
var connectionString = builder.Configuration.GetConnectionString("DefaultConnection")
    ?? "Server=(localdb)\\mssqllocaldb;Database=CrudibleDb;Trusted_Connection=true;MultipleActiveResultSets=true";

builder.Services.AddCrudibleDbContext(connectionString);

// Add logging
builder.Logging.ClearProviders();
builder.Logging.AddConsole();
builder.Logging.AddDebug();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}
else
{
    app.UseDeveloperExceptionPage();
}

app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();

// Use Crudible middleware
app.UseCrudible();

app.UseAuthorization();

app.MapRazorPages();


// Initialize database (disabled for now due to EF configuration issues)
// app.InitializeCrudibleDatabase();

app.Run();
