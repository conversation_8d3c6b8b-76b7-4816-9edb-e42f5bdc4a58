using System.Text;
using Crudible.Models;
using Crudible.Services.Interfaces;

namespace Crudible.Services;

/// <summary>
/// Service for generating CRUD pages dynamically
/// </summary>
public class CrudPageGenerator : ICrudPageGenerator
{
    private readonly TemplateService _templateService;
    private readonly ILogger<CrudPageGenerator> _logger;
    private readonly IWebHostEnvironment _environment;

    public CrudPageGenerator(
        TemplateService templateService,
        ILogger<CrudPageGenerator> logger,
        IWebHostEnvironment environment)
    {
        _templateService = templateService;
        _logger = logger;
        _environment = environment;
    }

    /// <summary>
    /// Generate all CRUD pages for a table
    /// </summary>
    /// <param name="tableDefinition">Table definition</param>
    /// <returns>Generated page information</returns>
    public async Task<CrudPageSet> GenerateCrudPagesAsync(TableDefinition tableDefinition)
    {
        try
        {
            _logger.LogInformation("Generating CRUD pages for table {TableName}", tableDefinition.Name);

            var pageSet = new CrudPageSet
            {
                TableDefinition = tableDefinition,
                ListPageContent = await GenerateListPageAsync(tableDefinition),
                CreatePageContent = await GenerateCreatePageAsync(tableDefinition),
                EditPageContent = await GenerateEditPageAsync(tableDefinition),
                DetailsPageContent = await GenerateDetailsPageAsync(tableDefinition),
                DeletePageContent = await GenerateDeletePageAsync(tableDefinition),
                PageModelContent = GeneratePageModelClass(tableDefinition),
                EntityModelContent = GenerateEntityModelClass(tableDefinition)
            };

            // Write pages to disk
            await WritePagesToDiskAsync(pageSet);

            _logger.LogInformation("Successfully generated CRUD pages for table {TableName}", tableDefinition.Name);
            return pageSet;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate CRUD pages for table {TableName}", tableDefinition.Name);
            throw;
        }
    }

    /// <summary>
    /// Generate list page for a table
    /// </summary>
    /// <param name="tableDefinition">Table definition</param>
    /// <returns>Generated page content</returns>
    public async Task<string> GenerateListPageAsync(TableDefinition tableDefinition)
    {
        return await Task.FromResult(_templateService.GenerateListPageTemplate(tableDefinition));
    }

    /// <summary>
    /// Generate create page for a table
    /// </summary>
    /// <param name="tableDefinition">Table definition</param>
    /// <returns>Generated page content</returns>
    public async Task<string> GenerateCreatePageAsync(TableDefinition tableDefinition)
    {
        return await Task.FromResult(_templateService.GenerateCreatePageTemplate(tableDefinition));
    }

    /// <summary>
    /// Generate edit page for a table
    /// </summary>
    /// <param name="tableDefinition">Table definition</param>
    /// <returns>Generated page content</returns>
    public async Task<string> GenerateEditPageAsync(TableDefinition tableDefinition)
    {
        return await Task.FromResult(_templateService.GenerateEditPageTemplate(tableDefinition));
    }

    /// <summary>
    /// Generate details page for a table
    /// </summary>
    /// <param name="tableDefinition">Table definition</param>
    /// <returns>Generated page content</returns>
    public async Task<string> GenerateDetailsPageAsync(TableDefinition tableDefinition)
    {
        return await Task.FromResult(_templateService.GenerateDetailsPageTemplate(tableDefinition));
    }

    /// <summary>
    /// Generate delete page for a table
    /// </summary>
    /// <param name="tableDefinition">Table definition</param>
    /// <returns>Generated page content</returns>
    public async Task<string> GenerateDeletePageAsync(TableDefinition tableDefinition)
    {
        return await Task.FromResult(_templateService.GenerateDeletePageTemplate(tableDefinition));
    }

    /// <summary>
    /// Write generated pages to disk
    /// </summary>
    /// <param name="pageSet">Page set to write</param>
    private async Task WritePagesToDiskAsync(CrudPageSet pageSet)
    {
        try
        {
            var tableName = pageSet.TableDefinition.Name;
            var pagesPath = Path.Combine(_environment.ContentRootPath, "Pages", "Tables", tableName);

            _logger.LogInformation("Writing CRUD pages to disk for table: {TableName} at {Path}", tableName, pagesPath);

            // Create directory if it doesn't exist
            Directory.CreateDirectory(pagesPath);

            // Write Razor pages with error handling
            var writeOperations = new List<Task>
            {
                WriteFileWithRetryAsync(Path.Combine(pagesPath, "Index.cshtml"), pageSet.ListPageContent),
                WriteFileWithRetryAsync(Path.Combine(pagesPath, "Create.cshtml"), pageSet.CreatePageContent),
                WriteFileWithRetryAsync(Path.Combine(pagesPath, "Edit.cshtml"), pageSet.EditPageContent),
                WriteFileWithRetryAsync(Path.Combine(pagesPath, "Details.cshtml"), pageSet.DetailsPageContent),
                WriteFileWithRetryAsync(Path.Combine(pagesPath, "Delete.cshtml"), pageSet.DeletePageContent),

                // Write page model classes
                WriteFileWithRetryAsync(Path.Combine(pagesPath, "Index.cshtml.cs"), GenerateListPageModel(pageSet.TableDefinition)),
                WriteFileWithRetryAsync(Path.Combine(pagesPath, "Create.cshtml.cs"), GenerateCreatePageModel(pageSet.TableDefinition)),
                WriteFileWithRetryAsync(Path.Combine(pagesPath, "Edit.cshtml.cs"), GenerateEditPageModel(pageSet.TableDefinition)),
                WriteFileWithRetryAsync(Path.Combine(pagesPath, "Details.cshtml.cs"), GenerateDetailsPageModel(pageSet.TableDefinition)),
                WriteFileWithRetryAsync(Path.Combine(pagesPath, "Delete.cshtml.cs"), GenerateDeletePageModel(pageSet.TableDefinition))
            };

            await Task.WhenAll(writeOperations);

            _logger.LogInformation("Successfully wrote all CRUD pages for table: {TableName}", tableName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to write CRUD pages for table: {TableName}", pageSet.TableDefinition.Name);
            throw;
        }
    }

    private async Task WriteFileWithRetryAsync(string filePath, string content, int maxRetries = 3)
    {
        for (int i = 0; i < maxRetries; i++)
        {
            try
            {
                await File.WriteAllTextAsync(filePath, content);
                _logger.LogDebug("Successfully wrote file: {FilePath}", filePath);
                return;
            }
            catch (IOException ex) when (i < maxRetries - 1)
            {
                _logger.LogWarning(ex, "Failed to write file {FilePath}, attempt {Attempt}/{MaxRetries}", filePath, i + 1, maxRetries);
                await Task.Delay(100 * (i + 1)); // Progressive delay
            }
        }
    }

    private string GeneratePageModelClass(TableDefinition table)
    {
        // This would generate the complete page model classes
        // For brevity, returning a placeholder
        return $"// Page model classes for {table.Name}";
    }

    private string GenerateEntityModelClass(TableDefinition table)
    {
        // This would generate the entity model class
        // For brevity, returning a placeholder
        return $"// Entity model class for {table.Name}";
    }

    private string GenerateListPageModel(TableDefinition table)
    {
        var sb = new StringBuilder();
        var modelName = GetModelName(table.Name);

        sb.AppendLine("using Microsoft.AspNetCore.Mvc;");
        sb.AppendLine("using Microsoft.AspNetCore.Mvc.RazorPages;");
        sb.AppendLine("using Crudible.Models;");
        sb.AppendLine("using Crudible.Services;");
        sb.AppendLine();
        sb.AppendLine($"namespace Crudible.Pages.Tables.{table.Name};");
        sb.AppendLine();
        sb.AppendLine($"public class {modelName}ListModel : PageModel");
        sb.AppendLine("{");
        sb.AppendLine("    private readonly DatabaseService _databaseService;");
        sb.AppendLine("    private readonly ConfigurationService _configurationService;");
        sb.AppendLine();
        sb.AppendLine($"    public {modelName}ListModel(DatabaseService databaseService, ConfigurationService configurationService)");
        sb.AppendLine("    {");
        sb.AppendLine("        _databaseService = databaseService;");
        sb.AppendLine("        _configurationService = configurationService;");
        sb.AppendLine("    }");
        sb.AppendLine();
        sb.AppendLine("    public PagedResult<object> Data { get; set; } = new();");
        sb.AppendLine("    public ListFilter Filter { get; set; } = new();");
        sb.AppendLine();
        sb.AppendLine("    public async Task OnGetAsync(string? searchTerm, int? page, int? pageSize, string? sortField, SortDirection? sortDirection)");
        sb.AppendLine("    {");
        sb.AppendLine("        Filter = new ListFilter");
        sb.AppendLine("        {");
        sb.AppendLine("            SearchTerm = searchTerm,");
        sb.AppendLine("            Page = page ?? 1,");
        sb.AppendLine($"            PageSize = pageSize ?? {table.UIConfiguration.ItemsPerPage},");
        sb.AppendLine("            SortField = sortField,");
        sb.AppendLine("            SortDirection = sortDirection ?? SortDirection.Ascending");
        sb.AppendLine("        };");
        sb.AppendLine();
        sb.AppendLine("        var config = await _configurationService.LoadConfigurationAsync();");
        sb.AppendLine("        var tables = new List<TableDefinition>(); // Load from schema");
        sb.AppendLine("        var connectionString = config.Database.ConnectionString ?? \"\";");
        sb.AppendLine();
        sb.AppendLine($"        Data = await _databaseService.GetTableDataAsync(\"{table.Name}\", tables, connectionString, Filter);");
        sb.AppendLine("    }");
        sb.AppendLine("}");

        return sb.ToString();
    }

    private string GenerateCreatePageModel(TableDefinition table)
    {
        var sb = new StringBuilder();
        var modelName = GetModelName(table.Name);

        sb.AppendLine("using Microsoft.AspNetCore.Mvc;");
        sb.AppendLine("using Microsoft.AspNetCore.Mvc.RazorPages;");
        sb.AppendLine("using System.ComponentModel.DataAnnotations;");
        sb.AppendLine("using Crudible.Models;");
        sb.AppendLine("using Crudible.Services;");
        sb.AppendLine();
        sb.AppendLine($"namespace Crudible.Pages.Tables.{table.Name};");
        sb.AppendLine();
        sb.AppendLine("public class CreateModel : PageModel");
        sb.AppendLine("{");
        sb.AppendLine("    private readonly TableDataService _dataService;");
        sb.AppendLine("    private readonly ILogger<CreateModel> _logger;");
        sb.AppendLine();
        sb.AppendLine("    public CreateModel(TableDataService dataService, ILogger<CreateModel> logger)");
        sb.AppendLine("    {");
        sb.AppendLine("        _dataService = dataService;");
        sb.AppendLine("        _logger = logger;");
        sb.AppendLine("    }");
        sb.AppendLine();

        // Generate properties for each editable field
        var editableFields = table.Fields.Where(f => !f.IsPrimaryKey && !f.IsIdentity).ToList();
        foreach (var field in editableFields)
        {
            var csharpType = GetCSharpType(field);
            sb.AppendLine($"    [BindProperty]");
            if (!field.IsNullable && field.DataType == FieldDataType.String)
            {
                sb.AppendLine($"    [Required]");
            }
            if (field.MaxLength.HasValue && field.DataType == FieldDataType.String)
            {
                sb.AppendLine($"    [StringLength({field.MaxLength.Value})]");
            }
            sb.AppendLine($"    public {csharpType} {field.Name} {{ get; set; }}");
            sb.AppendLine();
        }
        sb.AppendLine();
        sb.AppendLine("    public async Task<IActionResult> OnGetAsync()");
        sb.AppendLine("    {");
        sb.AppendLine("        return Page();");
        sb.AppendLine("    }");
        sb.AppendLine();
        sb.AppendLine("    public async Task<IActionResult> OnPostAsync()");
        sb.AppendLine("    {");
        sb.AppendLine("        try");
        sb.AppendLine("        {");
        sb.AppendLine("            if (!ModelState.IsValid)");
        sb.AppendLine("            {");
        sb.AppendLine("                return Page();");
        sb.AppendLine("            }");
        sb.AppendLine();
        sb.AppendLine("            var data = new Dictionary<string, object>");
        sb.AppendLine("            {");

        var editableFieldsForPost = table.Fields.Where(f => !f.IsPrimaryKey && !f.IsIdentity).ToList();
        foreach (var field in editableFieldsForPost)
        {
            sb.AppendLine($"                [\"{field.Name}\"] = {field.Name},");
        }

        sb.AppendLine("            };");
        sb.AppendLine();
        sb.AppendLine($"            var success = await _dataService.InsertRecordAsync(\"{table.Name}\", data);");
        sb.AppendLine();
        sb.AppendLine("            if (success)");
        sb.AppendLine("            {");
        sb.AppendLine("                TempData[\"SuccessMessage\"] = \"Record created successfully.\";");
        sb.AppendLine("                return RedirectToPage(\"./Index\");");
        sb.AppendLine("            }");
        sb.AppendLine("            else");
        sb.AppendLine("            {");
        sb.AppendLine("                ModelState.AddModelError(\"\", \"Failed to create record.\");");
        sb.AppendLine("                return Page();");
        sb.AppendLine("            }");
        sb.AppendLine("        }");
        sb.AppendLine("        catch (Exception ex)");
        sb.AppendLine("        {");
        sb.AppendLine($"            _logger.LogError(ex, \"Error creating {table.Name} record\");");
        sb.AppendLine("            ModelState.AddModelError(\"\", \"An error occurred while creating the record.\");");
        sb.AppendLine("            return Page();");
        sb.AppendLine("        }");
        sb.AppendLine("    }");
        sb.AppendLine("}");

        return sb.ToString();
    }

    private string GenerateEditPageModel(TableDefinition table)
    {
        var content = GenerateCreatePageModel(table);
        return content.Replace("Create", "Edit").Replace("OnGetAsync()", "OnGetAsync(int id)");
    }

    private string GenerateDetailsPageModel(TableDefinition table)
    {
        var sb = new StringBuilder();
        var modelName = GetModelName(table.Name);

        sb.AppendLine("using Microsoft.AspNetCore.Mvc;");
        sb.AppendLine("using Microsoft.AspNetCore.Mvc.RazorPages;");
        sb.AppendLine("using Crudible.Models;");
        sb.AppendLine("using Crudible.Services;");
        sb.AppendLine();
        sb.AppendLine($"namespace Crudible.Pages.Tables.{table.Name};");
        sb.AppendLine();
        sb.AppendLine($"public class {modelName}DetailsModel : PageModel");
        sb.AppendLine("{");
        sb.AppendLine("    private readonly DatabaseService _databaseService;");
        sb.AppendLine("    private readonly ConfigurationService _configurationService;");
        sb.AppendLine();
        sb.AppendLine($"    public {modelName}DetailsModel(DatabaseService databaseService, ConfigurationService configurationService)");
        sb.AppendLine("    {");
        sb.AppendLine("        _databaseService = databaseService;");
        sb.AppendLine("        _configurationService = configurationService;");
        sb.AppendLine("    }");
        sb.AppendLine();
        sb.AppendLine("    public dynamic Entity { get; set; } = new ExpandoObject();");
        sb.AppendLine();
        sb.AppendLine("    public async Task<IActionResult> OnGetAsync(int id)");
        sb.AppendLine("    {");
        sb.AppendLine("        var config = await _configurationService.LoadConfigurationAsync();");
        sb.AppendLine("        var tables = new List<TableDefinition>(); // Load from schema");
        sb.AppendLine("        var connectionString = config.Database.ConnectionString ?? \"\";");
        sb.AppendLine();
        sb.AppendLine($"        Entity = await _databaseService.GetRecordByIdAsync(\"{table.Name}\", id, tables, connectionString);");
        sb.AppendLine();
        sb.AppendLine("        if (Entity == null)");
        sb.AppendLine("        {");
        sb.AppendLine("            return NotFound();");
        sb.AppendLine("        }");
        sb.AppendLine();
        sb.AppendLine("        return Page();");
        sb.AppendLine("    }");
        sb.AppendLine("}");

        return sb.ToString();
    }

    private string GenerateDeletePageModel(TableDefinition table)
    {
        var content = GenerateDetailsPageModel(table);
        return content.Replace("Details", "Delete").Replace("DetailsModel", "DeleteModel");
    }

    private string GetModelName(string tableName)
    {
        return ToPascalCase(tableName);
    }

    private string ToPascalCase(string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;

        // Handle snake_case
        if (input.Contains('_'))
        {
            var parts = input.Split('_', StringSplitOptions.RemoveEmptyEntries);
            return string.Join("", parts.Select(part => 
                char.ToUpper(part[0]) + part.Substring(1).ToLower()));
        }

        // Handle camelCase or PascalCase
        return char.ToUpper(input[0]) + input.Substring(1);
    }

    private string GetCSharpType(FieldDefinition field)
    {
        var baseType = field.DataType switch
        {
            FieldDataType.String => "string",
            FieldDataType.Integer => "int",
            FieldDataType.Long => "long",
            FieldDataType.Decimal => "decimal",
            FieldDataType.Boolean => "bool",
            FieldDataType.DateTime => "DateTime",
            FieldDataType.Guid => "Guid",
            FieldDataType.Binary => "byte[]",
            _ => "object"
        };

        return field.IsNullable && baseType != "string" && baseType != "byte[]" ? $"{baseType}?" : baseType;
    }
}
