using System.Text;
using Crudible.Models;

namespace Crudible.Services;

/// <summary>
/// Service for generating Entity Framework Core model classes from table definitions
/// </summary>
public class EntityModelGenerator
{
    private readonly ILogger<EntityModelGenerator> _logger;

    public EntityModelGenerator(ILogger<EntityModelGenerator> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Generate entity model class code for a table
    /// </summary>
    /// <param name="table">Table definition</param>
    /// <param name="namespaceName">Namespace for the generated class</param>
    /// <returns>Generated C# class code</returns>
    public string GenerateEntityModel(TableDefinition table, string namespaceName = "Crudible.Models.Generated")
    {
        var sb = new StringBuilder();

        // Add using statements
        sb.AppendLine("using System;");
        sb.AppendLine("using System.ComponentModel.DataAnnotations;");
        sb.AppendLine("using System.ComponentModel.DataAnnotations.Schema;");
        sb.AppendLine("using System.Collections.Generic;");
        sb.AppendLine();

        // Add namespace
        sb.AppendLine($"namespace {namespaceName};");
        sb.AppendLine();

        // Add class documentation
        sb.AppendLine("/// <summary>");
        sb.AppendLine($"/// Entity model for {table.DisplayName}");
        if (!string.IsNullOrEmpty(table.Description))
        {
            sb.AppendLine($"/// {table.Description}");
        }
        sb.AppendLine("/// </summary>");

        // Add table attribute
        if (!string.IsNullOrEmpty(table.Schema) && table.Schema != "dbo")
        {
            sb.AppendLine($"[Table(\"{table.Name}\", Schema = \"{table.Schema}\")]");
        }
        else
        {
            sb.AppendLine($"[Table(\"{table.Name}\")]");
        }

        // Add class declaration
        var className = GetEntityClassName(table.Name);
        sb.AppendLine($"public partial class {className}");
        sb.AppendLine("{");

        // Add constructor for navigation properties
        var navigationProperties = GetNavigationProperties(table);
        if (navigationProperties.Any())
        {
            sb.AppendLine($"    public {className}()");
            sb.AppendLine("    {");
            foreach (var navProp in navigationProperties)
            {
                sb.AppendLine($"        {navProp.PropertyName} = new HashSet<{navProp.RelatedEntityName}>();");
            }
            sb.AppendLine("    }");
            sb.AppendLine();
        }

        // Add properties for each field
        foreach (var field in table.Fields.OrderBy(f => f.UIConfiguration.Order))
        {
            GenerateProperty(sb, field, table);
            sb.AppendLine();
        }

        // Add navigation properties
        foreach (var navProp in navigationProperties)
        {
            GenerateNavigationProperty(sb, navProp);
            sb.AppendLine();
        }

        sb.AppendLine("}");

        return sb.ToString();
    }

    /// <summary>
    /// Generate DbContext configuration for a table
    /// </summary>
    /// <param name="table">Table definition</param>
    /// <returns>DbContext configuration code</returns>
    public string GenerateDbContextConfiguration(TableDefinition table)
    {
        var sb = new StringBuilder();
        var entityName = GetEntityClassName(table.Name);

        sb.AppendLine($"        // Configure {entityName}");
        sb.AppendLine($"        modelBuilder.Entity<{entityName}>(entity =>");
        sb.AppendLine("        {");

        // Configure primary key
        if (table.PrimaryKeys.Count == 1)
        {
            var pkField = table.Fields.First(f => f.Name == table.PrimaryKeys[0]);
            sb.AppendLine($"            entity.HasKey(e => e.{GetPropertyName(pkField.Name)});");
        }
        else if (table.PrimaryKeys.Count > 1)
        {
            var pkProperties = string.Join(", ", table.PrimaryKeys.Select(pk => $"e.{GetPropertyName(pk)}"));
            sb.AppendLine($"            entity.HasKey(e => new {{ {pkProperties} }});");
        }

        // Configure table name and schema
        if (!string.IsNullOrEmpty(table.Schema) && table.Schema != "dbo")
        {
            sb.AppendLine($"            entity.ToTable(\"{table.Name}\", \"{table.Schema}\");");
        }
        else
        {
            sb.AppendLine($"            entity.ToTable(\"{table.Name}\");");
        }

        // Configure properties
        foreach (var field in table.Fields)
        {
            GeneratePropertyConfiguration(sb, field);
        }

        // Configure indexes
        foreach (var index in table.Indexes.Where(i => !i.Fields.SequenceEqual(table.PrimaryKeys)))
        {
            GenerateIndexConfiguration(sb, index);
        }

        // Configure foreign keys
        foreach (var fk in table.ForeignKeys)
        {
            GenerateForeignKeyConfiguration(sb, fk, table);
        }

        sb.AppendLine("        });");
        sb.AppendLine();

        return sb.ToString();
    }

    /// <summary>
    /// Generate complete DbContext class
    /// </summary>
    /// <param name="tables">List of table definitions</param>
    /// <param name="contextName">Name of the DbContext class</param>
    /// <param name="namespaceName">Namespace for the generated class</param>
    /// <returns>Generated DbContext class code</returns>
    public string GenerateDbContext(List<TableDefinition> tables, string contextName = "GeneratedDbContext", string namespaceName = "Crudible.Data.Generated")
    {
        var sb = new StringBuilder();

        // Add using statements
        sb.AppendLine("using Microsoft.EntityFrameworkCore;");
        sb.AppendLine("using Crudible.Models.Generated;");
        sb.AppendLine();

        // Add namespace
        sb.AppendLine($"namespace {namespaceName};");
        sb.AppendLine();

        // Add class documentation
        sb.AppendLine("/// <summary>");
        sb.AppendLine($"/// Generated DbContext for {tables.Count} tables");
        sb.AppendLine("/// </summary>");

        // Add class declaration
        sb.AppendLine($"public partial class {contextName} : DbContext");
        sb.AppendLine("{");

        // Add constructor
        sb.AppendLine($"    public {contextName}(DbContextOptions<{contextName}> options) : base(options)");
        sb.AppendLine("    {");
        sb.AppendLine("    }");
        sb.AppendLine();

        // Add DbSet properties
        foreach (var table in tables.OrderBy(t => t.Name))
        {
            var entityName = GetEntityClassName(table.Name);
            var dbSetName = GetDbSetName(table.Name);
            
            sb.AppendLine("    /// <summary>");
            sb.AppendLine($"    /// {table.DisplayName}");
            if (!string.IsNullOrEmpty(table.Description))
            {
                sb.AppendLine($"    /// {table.Description}");
            }
            sb.AppendLine("    /// </summary>");
            sb.AppendLine($"    public virtual DbSet<{entityName}> {dbSetName} {{ get; set; }}");
            sb.AppendLine();
        }

        // Add OnModelCreating method
        sb.AppendLine("    protected override void OnModelCreating(ModelBuilder modelBuilder)");
        sb.AppendLine("    {");
        sb.AppendLine("        base.OnModelCreating(modelBuilder);");
        sb.AppendLine();

        // Add configuration for each table
        foreach (var table in tables.OrderBy(t => t.Name))
        {
            sb.Append(GenerateDbContextConfiguration(table));
        }

        sb.AppendLine("    }");
        sb.AppendLine("}");

        return sb.ToString();
    }

    private void GenerateProperty(StringBuilder sb, FieldDefinition field, TableDefinition table)
    {
        var propertyName = GetPropertyName(field.Name);
        var propertyType = GetPropertyType(field);

        // Add property documentation
        sb.AppendLine("    /// <summary>");
        sb.AppendLine($"    /// {field.DisplayName}");
        if (!string.IsNullOrEmpty(field.Description))
        {
            sb.AppendLine($"    /// {field.Description}");
        }
        sb.AppendLine("    /// </summary>");

        // Add data annotations
        var annotations = GetDataAnnotations(field);
        foreach (var annotation in annotations)
        {
            sb.AppendLine($"    {annotation}");
        }

        // Add property declaration
        sb.AppendLine($"    public {propertyType} {propertyName} {{ get; set; }}");
    }

    private void GenerateNavigationProperty(StringBuilder sb, NavigationProperty navProp)
    {
        sb.AppendLine("    /// <summary>");
        sb.AppendLine($"    /// Navigation property for {navProp.Description}");
        sb.AppendLine("    /// </summary>");
        
        if (navProp.IsCollection)
        {
            sb.AppendLine($"    public virtual ICollection<{navProp.RelatedEntityName}> {navProp.PropertyName} {{ get; set; }}");
        }
        else
        {
            sb.AppendLine($"    public virtual {navProp.RelatedEntityName}? {navProp.PropertyName} {{ get; set; }}");
        }
    }

    private void GeneratePropertyConfiguration(StringBuilder sb, FieldDefinition field)
    {
        var propertyName = GetPropertyName(field.Name);

        sb.AppendLine($"            entity.Property(e => e.{propertyName})");

        // Configure column name if different from property name
        if (field.Name != propertyName)
        {
            sb.AppendLine($"                .HasColumnName(\"{field.Name}\")");
        }

        // Configure data type
        if (!string.IsNullOrEmpty(field.SqlDataType))
        {
            if (field.MaxLength.HasValue)
            {
                sb.AppendLine($"                .HasColumnType(\"{field.SqlDataType}({field.MaxLength})\")");
            }
            else if (field.Precision.HasValue && field.Scale.HasValue)
            {
                sb.AppendLine($"                .HasColumnType(\"{field.SqlDataType}({field.Precision},{field.Scale})\")");
            }
            else
            {
                sb.AppendLine($"                .HasColumnType(\"{field.SqlDataType}\")");
            }
        }

        // Configure nullable
        if (!field.IsNullable && field.DataType != FieldDataType.String)
        {
            sb.AppendLine("                .IsRequired()");
        }

        // Configure max length for strings
        if (field.DataType == FieldDataType.String && field.MaxLength.HasValue)
        {
            sb.AppendLine($"                .HasMaxLength({field.MaxLength})");
        }

        // Configure default value
        if (field.DefaultValue != null)
        {
            var defaultValueString = GetDefaultValueString(field.DefaultValue, field.DataType);
            if (!string.IsNullOrEmpty(defaultValueString))
            {
                sb.AppendLine($"                .HasDefaultValue({defaultValueString})");
            }
        }

        sb.AppendLine("                ;");
    }

    private void GenerateIndexConfiguration(StringBuilder sb, IndexDefinition index)
    {
        var properties = string.Join(", ", index.Fields.Select(f => $"e.{GetPropertyName(f)}"));
        
        if (index.IsUnique)
        {
            sb.AppendLine($"            entity.HasIndex(e => new {{ {properties} }})");
            sb.AppendLine($"                .HasDatabaseName(\"{index.Name}\")");
            sb.AppendLine("                .IsUnique();");
        }
        else
        {
            sb.AppendLine($"            entity.HasIndex(e => new {{ {properties} }})");
            sb.AppendLine($"                .HasDatabaseName(\"{index.Name}\");");
        }
    }

    private void GenerateForeignKeyConfiguration(StringBuilder sb, ForeignKeyDefinition fk, TableDefinition table)
    {
        var localProperties = string.Join(", ", fk.LocalFields.Select(f => $"e.{GetPropertyName(f)}"));
        var referencedEntity = GetEntityClassName(fk.ReferencedTable);
        
        sb.AppendLine($"            entity.HasOne<{referencedEntity}>()");
        sb.AppendLine($"                .WithMany()");
        sb.AppendLine($"                .HasForeignKey(e => new {{ {localProperties} }})");
        sb.AppendLine($"                .HasConstraintName(\"{fk.Name}\")");
        
        if (fk.OnDelete != ForeignKeyAction.NoAction)
        {
            sb.AppendLine($"                .OnDelete(DeleteBehavior.{MapDeleteBehavior(fk.OnDelete)})");
        }
        
        sb.AppendLine("                ;");
    }

    private List<string> GetDataAnnotations(FieldDefinition field)
    {
        var annotations = new List<string>();

        // Key attribute
        if (field.IsPrimaryKey)
        {
            annotations.Add("[Key]");
        }

        // DatabaseGenerated attribute
        if (field.IsIdentity)
        {
            annotations.Add("[DatabaseGenerated(DatabaseGeneratedOption.Identity)]");
        }

        // Required attribute
        if (!field.IsNullable && field.DataType == FieldDataType.String)
        {
            annotations.Add("[Required]");
        }

        // MaxLength attribute
        if (field.DataType == FieldDataType.String && field.MaxLength.HasValue)
        {
            annotations.Add($"[MaxLength({field.MaxLength})]");
        }

        // Column attribute
        if (field.Name != GetPropertyName(field.Name))
        {
            annotations.Add($"[Column(\"{field.Name}\")]");
        }

        return annotations;
    }

    private List<NavigationProperty> GetNavigationProperties(TableDefinition table)
    {
        var navProps = new List<NavigationProperty>();

        // Add collection navigation properties for tables that reference this table
        // This would be populated based on relationships analysis
        // For now, return empty list - this would be enhanced with relationship data

        return navProps;
    }

    private string GetEntityClassName(string tableName)
    {
        // Convert table name to singular entity class name
        var className = tableName;
        
        // Remove common table prefixes
        if (className.StartsWith("tbl", StringComparison.OrdinalIgnoreCase))
            className = className.Substring(3);
        
        // Convert to PascalCase
        className = ToPascalCase(className);
        
        // Make singular (basic rules)
        if (className.EndsWith("ies", StringComparison.OrdinalIgnoreCase))
            className = className.Substring(0, className.Length - 3) + "y";
        else if (className.EndsWith("s", StringComparison.OrdinalIgnoreCase) && className.Length > 1)
            className = className.Substring(0, className.Length - 1);
        
        return className;
    }

    private string GetDbSetName(string tableName)
    {
        // Keep table name as-is for DbSet, but ensure PascalCase
        return ToPascalCase(tableName);
    }

    private string GetPropertyName(string fieldName)
    {
        return ToPascalCase(fieldName);
    }

    private string GetPropertyType(FieldDefinition field)
    {
        var baseType = field.DataType switch
        {
            FieldDataType.String => "string",
            FieldDataType.Integer => "int",
            FieldDataType.Long => "long",
            FieldDataType.Decimal => "decimal",
            FieldDataType.Double => "double",
            FieldDataType.Boolean => "bool",
            FieldDataType.DateTime => "DateTime",
            FieldDataType.Date => "DateTime",
            FieldDataType.Time => "TimeSpan",
            FieldDataType.Guid => "Guid",
            FieldDataType.Binary => "byte[]",
            _ => "string"
        };

        // Add nullable suffix for value types
        if (field.IsNullable && baseType != "string" && baseType != "byte[]")
        {
            baseType += "?";
        }

        return baseType;
    }

    private string GetDefaultValueString(object defaultValue, FieldDataType dataType)
    {
        return dataType switch
        {
            FieldDataType.String => $"\"{defaultValue}\"",
            FieldDataType.Boolean => defaultValue.ToString()?.ToLower() ?? "false",
            FieldDataType.DateTime => "DateTime.UtcNow",
            _ => defaultValue.ToString() ?? ""
        };
    }

    private string MapDeleteBehavior(ForeignKeyAction action)
    {
        return action switch
        {
            ForeignKeyAction.Cascade => "Cascade",
            ForeignKeyAction.SetNull => "SetNull",
            ForeignKeyAction.Restrict => "Restrict",
            _ => "NoAction"
        };
    }

    private string ToPascalCase(string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;

        // Handle snake_case
        if (input.Contains('_'))
        {
            var parts = input.Split('_', StringSplitOptions.RemoveEmptyEntries);
            return string.Join("", parts.Select(part => 
                char.ToUpper(part[0]) + part.Substring(1).ToLower()));
        }

        // Handle camelCase or PascalCase
        return char.ToUpper(input[0]) + input.Substring(1);
    }

    private class NavigationProperty
    {
        public string PropertyName { get; set; } = string.Empty;
        public string RelatedEntityName { get; set; } = string.Empty;
        public bool IsCollection { get; set; }
        public string Description { get; set; } = string.Empty;
    }
}
