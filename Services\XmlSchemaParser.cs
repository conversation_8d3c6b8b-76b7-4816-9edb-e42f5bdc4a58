using System.Xml;
using System.Xml.Linq;
using Crudible.Models;

namespace Crudible.Services;

/// <summary>
/// Service for parsing XML schema definitions
/// </summary>
public class XmlSchemaParser
{
    private readonly ILogger<XmlSchemaParser> _logger;

    public XmlSchemaParser(ILogger<XmlSchemaParser> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Parse XML schema and extract table definitions
    /// </summary>
    /// <param name="xmlContent">XML schema content</param>
    /// <returns>List of table definitions</returns>
    public Task<List<TableDefinition>> ParseSchemaAsync(string xmlContent)
    {
        try
        {
            var document = XDocument.Parse(xmlContent);
            var tables = new List<TableDefinition>();

            // Support multiple XML schema formats
            if (IsCustomCrudibleSchema(document))
            {
                tables = ParseCrudibleSchema(document);
            }
            else if (IsXmlSchemaDefinition(document))
            {
                tables = ParseXmlSchemaDefinition(document);
            }
            else if (IsSqlServerSchema(document))
            {
                tables = ParseSqlServerSchema(document);
            }
            else
            {
                throw new InvalidOperationException("Unsupported XML schema format");
            }

            _logger.LogInformation("Successfully parsed {TableCount} tables from XML schema", tables.Count);
            return Task.FromResult(tables);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to parse XML schema");
            throw;
        }
    }

    /// <summary>
    /// Validate XML schema format
    /// </summary>
    /// <param name="xmlContent">XML content to validate</param>
    /// <returns>Validation result</returns>
    public SchemaValidationResult ValidateSchema(string xmlContent)
    {
        var result = new SchemaValidationResult();

        try
        {
            var document = XDocument.Parse(xmlContent);
            
            if (!IsValidSchemaFormat(document))
            {
                result.Errors.Add("Unsupported XML schema format");
                return result;
            }

            // Validate structure
            var tables = ParseSchemaAsync(xmlContent).Result;
            result.TableCount = tables.Count;

            // Validate each table
            foreach (var table in tables)
            {
                ValidateTable(table, result);
            }

            result.IsValid = !result.Errors.Any();
        }
        catch (XmlException ex)
        {
            result.Errors.Add($"Invalid XML format: {ex.Message}");
        }
        catch (Exception ex)
        {
            result.Errors.Add($"Schema validation error: {ex.Message}");
        }

        return result;
    }

    private bool IsCustomCrudibleSchema(XDocument document)
    {
        return document.Root?.Name.LocalName == "CrudibleSchema" ||
               document.Root?.Attribute("type")?.Value == "crudible";
    }

    private bool IsXmlSchemaDefinition(XDocument document)
    {
        return document.Root?.Name.LocalName == "schema" &&
               document.Root?.Name.NamespaceName == "http://www.w3.org/2001/XMLSchema";
    }

    private bool IsSqlServerSchema(XDocument document)
    {
        return document.Root?.Name.LocalName == "Database" ||
               document.Root?.Elements().Any(e => e.Name.LocalName == "Table") == true;
    }

    private bool IsValidSchemaFormat(XDocument document)
    {
        return IsCustomCrudibleSchema(document) || 
               IsXmlSchemaDefinition(document) || 
               IsSqlServerSchema(document);
    }

    private List<TableDefinition> ParseCrudibleSchema(XDocument document)
    {
        var tables = new List<TableDefinition>();
        var tablesElement = document.Root?.Element("Tables");

        if (tablesElement == null)
            return tables;

        foreach (var tableElement in tablesElement.Elements("Table"))
        {
            var table = ParseCrudibleTable(tableElement);
            if (table != null)
                tables.Add(table);
        }

        return tables;
    }

    private TableDefinition? ParseCrudibleTable(XElement tableElement)
    {
        var name = tableElement.Attribute("name")?.Value;
        if (string.IsNullOrEmpty(name))
            return null;

        var table = new TableDefinition
        {
            Name = name,
            Schema = tableElement.Attribute("schema")?.Value,
            DisplayName = tableElement.Attribute("displayName")?.Value ?? name,
            Description = tableElement.Element("Description")?.Value
        };

        // Parse fields
        var fieldsElement = tableElement.Element("Fields");
        if (fieldsElement != null)
        {
            foreach (var fieldElement in fieldsElement.Elements("Field"))
            {
                var field = ParseCrudibleField(fieldElement);
                if (field != null)
                    table.Fields.Add(field);
            }
        }

        // Parse primary keys
        var primaryKeysElement = tableElement.Element("PrimaryKeys");
        if (primaryKeysElement != null)
        {
            foreach (var keyElement in primaryKeysElement.Elements("Key"))
            {
                var keyName = keyElement.Attribute("field")?.Value;
                if (!string.IsNullOrEmpty(keyName))
                    table.PrimaryKeys.Add(keyName);
            }
        }

        // Parse foreign keys
        var foreignKeysElement = tableElement.Element("ForeignKeys");
        if (foreignKeysElement != null)
        {
            foreach (var fkElement in foreignKeysElement.Elements("ForeignKey"))
            {
                var fk = ParseCrudibleForeignKey(fkElement);
                if (fk != null)
                    table.ForeignKeys.Add(fk);
            }
        }

        return table;
    }

    private FieldDefinition? ParseCrudibleField(XElement fieldElement)
    {
        var name = fieldElement.Attribute("name")?.Value;
        var dataType = fieldElement.Attribute("type")?.Value;

        if (string.IsNullOrEmpty(name) || string.IsNullOrEmpty(dataType))
            return null;

        var field = new FieldDefinition
        {
            Name = name,
            DisplayName = fieldElement.Attribute("displayName")?.Value ?? name,
            Description = fieldElement.Element("Description")?.Value,
            SqlDataType = dataType,
            IsNullable = bool.Parse(fieldElement.Attribute("nullable")?.Value ?? "true"),
            IsPrimaryKey = bool.Parse(fieldElement.Attribute("primaryKey")?.Value ?? "false"),
            IsIdentity = bool.Parse(fieldElement.Attribute("identity")?.Value ?? "false"),
            IsUnique = bool.Parse(fieldElement.Attribute("unique")?.Value ?? "false")
        };

        // Parse data type
        field.DataType = MapSqlTypeToFieldDataType(dataType);
        field.DotNetType = MapSqlTypeToDotNetType(dataType, field.IsNullable);

        // Parse length, precision, scale
        if (int.TryParse(fieldElement.Attribute("maxLength")?.Value, out var maxLength))
            field.MaxLength = maxLength;

        if (int.TryParse(fieldElement.Attribute("precision")?.Value, out var precision))
            field.Precision = precision;

        if (int.TryParse(fieldElement.Attribute("scale")?.Value, out var scale))
            field.Scale = scale;

        // Parse default value
        var defaultValue = fieldElement.Attribute("defaultValue")?.Value;
        if (!string.IsNullOrEmpty(defaultValue))
        {
            field.DefaultValue = ConvertDefaultValue(defaultValue, field.DataType);
        }

        // Parse foreign key reference
        var referencedTable = fieldElement.Attribute("referencedTable")?.Value;
        var referencedField = fieldElement.Attribute("referencedField")?.Value;
        if (!string.IsNullOrEmpty(referencedTable) && !string.IsNullOrEmpty(referencedField))
        {
            field.ForeignKeyReference = new ForeignKeyReference
            {
                ReferencedTable = referencedTable,
                ReferencedField = referencedField,
                DisplayField = fieldElement.Attribute("displayField")?.Value
            };
        }

        return field;
    }

    private ForeignKeyDefinition? ParseCrudibleForeignKey(XElement fkElement)
    {
        var name = fkElement.Attribute("name")?.Value;
        var referencedTable = fkElement.Attribute("referencedTable")?.Value;

        if (string.IsNullOrEmpty(name) || string.IsNullOrEmpty(referencedTable))
            return null;

        var fk = new ForeignKeyDefinition
        {
            Name = name,
            ReferencedTable = referencedTable
        };

        // Parse local fields
        var localFieldsElement = fkElement.Element("LocalFields");
        if (localFieldsElement != null)
        {
            foreach (var fieldElement in localFieldsElement.Elements("Field"))
            {
                var fieldName = fieldElement.Attribute("name")?.Value;
                if (!string.IsNullOrEmpty(fieldName))
                    fk.LocalFields.Add(fieldName);
            }
        }

        // Parse referenced fields
        var referencedFieldsElement = fkElement.Element("ReferencedFields");
        if (referencedFieldsElement != null)
        {
            foreach (var fieldElement in referencedFieldsElement.Elements("Field"))
            {
                var fieldName = fieldElement.Attribute("name")?.Value;
                if (!string.IsNullOrEmpty(fieldName))
                    fk.ReferencedFields.Add(fieldName);
            }
        }

        // Parse actions
        if (Enum.TryParse<ForeignKeyAction>(fkElement.Attribute("onDelete")?.Value, out var onDelete))
            fk.OnDelete = onDelete;

        if (Enum.TryParse<ForeignKeyAction>(fkElement.Attribute("onUpdate")?.Value, out var onUpdate))
            fk.OnUpdate = onUpdate;

        return fk;
    }

    private List<TableDefinition> ParseXmlSchemaDefinition(XDocument document)
    {
        // Implementation for W3C XML Schema format
        // This would parse XSD files that define database structures
        var tables = new List<TableDefinition>();
        
        // TODO: Implement XSD parsing logic
        _logger.LogWarning("XSD parsing not yet implemented");
        
        return tables;
    }

    private List<TableDefinition> ParseSqlServerSchema(XDocument document)
    {
        // Implementation for SQL Server XML schema export format
        var tables = new List<TableDefinition>();
        
        // TODO: Implement SQL Server schema parsing logic
        _logger.LogWarning("SQL Server schema parsing not yet implemented");
        
        return tables;
    }

    private FieldDataType MapSqlTypeToFieldDataType(string sqlType)
    {
        return sqlType.ToLower() switch
        {
            "varchar" or "nvarchar" or "char" or "nchar" or "text" or "ntext" => FieldDataType.String,
            "int" or "smallint" or "tinyint" => FieldDataType.Integer,
            "bigint" => FieldDataType.Long,
            "decimal" or "numeric" or "money" or "smallmoney" => FieldDataType.Decimal,
            "float" or "real" => FieldDataType.Double,
            "bit" => FieldDataType.Boolean,
            "datetime" or "datetime2" or "smalldatetime" => FieldDataType.DateTime,
            "date" => FieldDataType.Date,
            "time" => FieldDataType.Time,
            "uniqueidentifier" => FieldDataType.Guid,
            "varbinary" or "binary" or "image" => FieldDataType.Binary,
            _ => FieldDataType.String
        };
    }

    private string MapSqlTypeToDotNetType(string sqlType, bool isNullable)
    {
        var baseType = sqlType.ToLower() switch
        {
            "varchar" or "nvarchar" or "char" or "nchar" or "text" or "ntext" => "string",
            "int" => "int",
            "smallint" => "short",
            "tinyint" => "byte",
            "bigint" => "long",
            "decimal" or "numeric" or "money" or "smallmoney" => "decimal",
            "float" => "double",
            "real" => "float",
            "bit" => "bool",
            "datetime" or "datetime2" or "smalldatetime" or "date" or "time" => "DateTime",
            "uniqueidentifier" => "Guid",
            "varbinary" or "binary" or "image" => "byte[]",
            _ => "string"
        };

        // Add nullable suffix for value types
        if (isNullable && baseType != "string" && baseType != "byte[]")
        {
            baseType += "?";
        }

        return baseType;
    }

    private object? ConvertDefaultValue(string defaultValue, FieldDataType dataType)
    {
        try
        {
            return dataType switch
            {
                FieldDataType.String => defaultValue,
                FieldDataType.Integer => int.Parse(defaultValue),
                FieldDataType.Long => long.Parse(defaultValue),
                FieldDataType.Decimal => decimal.Parse(defaultValue),
                FieldDataType.Double => double.Parse(defaultValue),
                FieldDataType.Boolean => bool.Parse(defaultValue),
                FieldDataType.DateTime => DateTime.Parse(defaultValue),
                FieldDataType.Date => DateTime.Parse(defaultValue).Date,
                FieldDataType.Guid => Guid.Parse(defaultValue),
                _ => defaultValue
            };
        }
        catch
        {
            return null;
        }
    }

    private void ValidateTable(TableDefinition table, SchemaValidationResult result)
    {
        if (string.IsNullOrEmpty(table.Name))
        {
            result.Errors.Add("Table name cannot be empty");
        }

        if (!table.Fields.Any())
        {
            result.Warnings.Add($"Table '{table.Name}' has no fields defined");
        }

        if (!table.PrimaryKeys.Any())
        {
            result.Warnings.Add($"Table '{table.Name}' has no primary key defined");
        }

        // Validate field names are unique
        var duplicateFields = table.Fields.GroupBy(f => f.Name)
            .Where(g => g.Count() > 1)
            .Select(g => g.Key);

        foreach (var duplicateField in duplicateFields)
        {
            result.Errors.Add($"Table '{table.Name}' has duplicate field '{duplicateField}'");
        }
    }
}
