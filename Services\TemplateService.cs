using System.Text;
using Crudible.Models;

namespace Crudible.Services;

/// <summary>
/// Service for generating Razor page templates
/// </summary>
public class TemplateService
{
    private readonly ILogger<TemplateService> _logger;

    public TemplateService(ILogger<TemplateService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Generate list page template
    /// </summary>
    /// <param name="table">Table definition</param>
    /// <returns>Razor page content</returns>
    public string GenerateListPageTemplate(TableDefinition table)
    {
        var sb = new StringBuilder();
        var modelName = GetModelName(table.Name);
        var displayFields = table.Fields.Where(f => f.UIConfiguration.ShowInList).Take(6).ToList();

        sb.AppendLine("@page");
        sb.AppendLine($"@page \"/tables/{table.Name.ToLower()}\"");
        sb.AppendLine($"@model {modelName}ListModel");
        sb.AppendLine("@{");
        sb.AppendLine($"    ViewData[\"Title\"] = \"{table.DisplayName}\";");
        sb.AppendLine("}");
        sb.AppendLine();

        // Page header
        sb.AppendLine("<div class=\"d-flex justify-content-between align-items-center mb-4\">");
        sb.AppendLine($"    <h1><i class=\"{table.UIConfiguration.Icon ?? "fas fa-table"}\"></i> {table.DisplayName}</h1>");
        sb.AppendLine("    <div>");
        sb.AppendLine($"        <a asp-page=\"./{table.Name}/Create\" class=\"btn btn-primary\">");
        sb.AppendLine("            <i class=\"fas fa-plus\"></i> Add New");
        sb.AppendLine("        </a>");
        sb.AppendLine("        <div class=\"btn-group\" role=\"group\">");
        sb.AppendLine($"            <a href=\"/api/export/{table.Name}/excel\" class=\"btn btn-outline-success\">");
        sb.AppendLine("                <i class=\"fas fa-file-excel\"></i> Excel");
        sb.AppendLine("            </a>");
        sb.AppendLine($"            <a href=\"/api/export/{table.Name}/pdf\" class=\"btn btn-outline-danger\">");
        sb.AppendLine("                <i class=\"fas fa-file-pdf\"></i> PDF");
        sb.AppendLine("            </a>");
        sb.AppendLine("        </div>");
        sb.AppendLine("    </div>");
        sb.AppendLine("</div>");
        sb.AppendLine();

        // Search and filters
        sb.AppendLine("<div class=\"card mb-4\">");
        sb.AppendLine("    <div class=\"card-body\">");
        sb.AppendLine("        <form method=\"get\" class=\"row g-3\">");
        sb.AppendLine("            <div class=\"col-md-6\">");
        sb.AppendLine("                <label class=\"form-label\">Search</label>");
        sb.AppendLine("                <input type=\"text\" name=\"searchTerm\" value=\"@Model.Filter.SearchTerm\" class=\"form-control\" placeholder=\"Search...\">");
        sb.AppendLine("            </div>");
        sb.AppendLine("            <div class=\"col-md-3\">");
        sb.AppendLine("                <label class=\"form-label\">Page Size</label>");
        sb.AppendLine("                <select name=\"pageSize\" class=\"form-select\">");
        sb.AppendLine("                    <option value=\"10\" @(Model.Filter.PageSize == 10 ? \"selected\" : \"\")>10</option>");
        sb.AppendLine("                    <option value=\"25\" @(Model.Filter.PageSize == 25 ? \"selected\" : \"\")>25</option>");
        sb.AppendLine("                    <option value=\"50\" @(Model.Filter.PageSize == 50 ? \"selected\" : \"\")>50</option>");
        sb.AppendLine("                    <option value=\"100\" @(Model.Filter.PageSize == 100 ? \"selected\" : \"\")>100</option>");
        sb.AppendLine("                </select>");
        sb.AppendLine("            </div>");
        sb.AppendLine("            <div class=\"col-md-3 d-flex align-items-end\">");
        sb.AppendLine("                <button type=\"submit\" class=\"btn btn-primary me-2\">");
        sb.AppendLine("                    <i class=\"fas fa-search\"></i> Search");
        sb.AppendLine("                </button>");
        sb.AppendLine("                <a asp-page=\"./Index\" class=\"btn btn-outline-secondary\">");
        sb.AppendLine("                    <i class=\"fas fa-times\"></i> Clear");
        sb.AppendLine("                </a>");
        sb.AppendLine("            </div>");
        sb.AppendLine("        </form>");
        sb.AppendLine("    </div>");
        sb.AppendLine("</div>");
        sb.AppendLine();

        // Data table
        sb.AppendLine("<div class=\"card\">");
        sb.AppendLine("    <div class=\"card-body\">");
        sb.AppendLine("        @if (Model.Data.Items.Any())");
        sb.AppendLine("        {");
        sb.AppendLine("            <div class=\"table-responsive\">");
        sb.AppendLine("                <table class=\"table table-striped table-hover\">");
        sb.AppendLine("                    <thead class=\"table-dark\">");
        sb.AppendLine("                        <tr>");

        // Table headers
        foreach (var field in displayFields)
        {
            sb.AppendLine($"                            <th>");
            sb.AppendLine($"                                <a asp-page=\"./Index\" asp-route-sortField=\"{field.Name}\" ");
            sb.AppendLine($"                                   asp-route-sortDirection=\"@(Model.Filter.SortField == \"{field.Name}\" && Model.Filter.SortDirection == SortDirection.Ascending ? SortDirection.Descending : SortDirection.Ascending)\"");
            sb.AppendLine($"                                   class=\"text-white text-decoration-none\">");
            sb.AppendLine($"                                    {field.DisplayName}");
            sb.AppendLine($"                                    @if (Model.Filter.SortField == \"{field.Name}\")");
            sb.AppendLine("                                    {");
            sb.AppendLine("                                        <i class=\"fas fa-sort-@(Model.Filter.SortDirection == SortDirection.Ascending ? \"up\" : \"down\")\"></i>");
            sb.AppendLine("                                    }");
            sb.AppendLine("                                </a>");
            sb.AppendLine("                            </th>");
        }

        sb.AppendLine("                            <th width=\"150\">Actions</th>");
        sb.AppendLine("                        </tr>");
        sb.AppendLine("                    </thead>");
        sb.AppendLine("                    <tbody>");
        sb.AppendLine("                        @foreach (var item in Model.Data.Items)");
        sb.AppendLine("                        {");
        sb.AppendLine("                            <tr>");

        // Table data cells
        foreach (var field in displayFields)
        {
            sb.AppendLine("                                <td>");
            if (field.DataType == FieldDataType.Boolean)
            {
                sb.AppendLine($"                                    <span class=\"badge bg-@(GetPropertyValue(item, \"{field.Name}\")?.ToString() == \"True\" ? \"success\" : \"secondary\")\">");
                sb.AppendLine($"                                        @(GetPropertyValue(item, \"{field.Name}\")?.ToString() == \"True\" ? \"Yes\" : \"No\")");
                sb.AppendLine("                                    </span>");
            }
            else if (field.DataType == FieldDataType.DateTime || field.DataType == FieldDataType.Date)
            {
                sb.AppendLine($"                                    @(GetPropertyValue(item, \"{field.Name}\") is DateTime dt ? dt.ToString(\"yyyy-MM-dd HH:mm\") : \"\")");
            }
            else if (field.MaxLength > 100)
            {
                sb.AppendLine($"                                    @(GetPropertyValue(item, \"{field.Name}\")?.ToString()?.Length > 50 ? ");
                sb.AppendLine($"                                      GetPropertyValue(item, \"{field.Name}\")?.ToString()?.Substring(0, 50) + \"...\" : ");
                sb.AppendLine($"                                      GetPropertyValue(item, \"{field.Name}\")?.ToString())");
            }
            else
            {
                sb.AppendLine($"                                    @GetPropertyValue(item, \"{field.Name}\")");
            }
            sb.AppendLine("                                </td>");
        }

        // Actions column
        var primaryKey = table.PrimaryKeys.FirstOrDefault() ?? table.Fields.First().Name;
        sb.AppendLine("                                <td>");
        sb.AppendLine("                                    <div class=\"btn-group btn-group-sm\" role=\"group\">");
        sb.AppendLine($"                                        <a asp-page=\"./{table.Name}/Details\" asp-route-id=\"@GetPropertyValue(item, \"{primaryKey}\")\" ");
        sb.AppendLine("                                           class=\"btn btn-outline-info\" title=\"View Details\">");
        sb.AppendLine("                                            <i class=\"fas fa-eye\"></i>");
        sb.AppendLine("                                        </a>");
        sb.AppendLine($"                                        <a asp-page=\"./{table.Name}/Edit\" asp-route-id=\"@GetPropertyValue(item, \"{primaryKey}\")\" ");
        sb.AppendLine("                                           class=\"btn btn-outline-warning\" title=\"Edit\">");
        sb.AppendLine("                                            <i class=\"fas fa-edit\"></i>");
        sb.AppendLine("                                        </a>");
        sb.AppendLine($"                                        <a asp-page=\"./{table.Name}/Delete\" asp-route-id=\"@GetPropertyValue(item, \"{primaryKey}\")\" ");
        sb.AppendLine("                                           class=\"btn btn-outline-danger\" title=\"Delete\">");
        sb.AppendLine("                                            <i class=\"fas fa-trash\"></i>");
        sb.AppendLine("                                        </a>");
        sb.AppendLine("                                    </div>");
        sb.AppendLine("                                </td>");
        sb.AppendLine("                            </tr>");
        sb.AppendLine("                        }");
        sb.AppendLine("                    </tbody>");
        sb.AppendLine("                </table>");
        sb.AppendLine("            </div>");
        sb.AppendLine();

        // Pagination
        sb.AppendLine("            <!-- Pagination -->");
        sb.AppendLine("            @if (Model.Data.TotalPages > 1)");
        sb.AppendLine("            {");
        sb.AppendLine("                <nav aria-label=\"Page navigation\">");
        sb.AppendLine("                    <ul class=\"pagination justify-content-center\">");
        sb.AppendLine("                        <li class=\"page-item @(!Model.Data.HasPreviousPage ? \"disabled\" : \"\")\">");
        sb.AppendLine("                            <a class=\"page-link\" asp-page=\"./Index\" asp-route-page=\"@(Model.Data.CurrentPage - 1)\" ");
        sb.AppendLine("                               asp-route-searchTerm=\"@Model.Filter.SearchTerm\" asp-route-pageSize=\"@Model.Filter.PageSize\">");
        sb.AppendLine("                                Previous");
        sb.AppendLine("                            </a>");
        sb.AppendLine("                        </li>");
        sb.AppendLine();
        sb.AppendLine("                        @for (int i = Math.Max(1, Model.Data.CurrentPage - 2); i <= Math.Min(Model.Data.TotalPages, Model.Data.CurrentPage + 2); i++)");
        sb.AppendLine("                        {");
        sb.AppendLine("                            <li class=\"page-item @(i == Model.Data.CurrentPage ? \"active\" : \"\")\">");
        sb.AppendLine("                                <a class=\"page-link\" asp-page=\"./Index\" asp-route-page=\"@i\" ");
        sb.AppendLine("                                   asp-route-searchTerm=\"@Model.Filter.SearchTerm\" asp-route-pageSize=\"@Model.Filter.PageSize\">");
        sb.AppendLine("                                    @i");
        sb.AppendLine("                                </a>");
        sb.AppendLine("                            </li>");
        sb.AppendLine("                        }");
        sb.AppendLine();
        sb.AppendLine("                        <li class=\"page-item @(!Model.Data.HasNextPage ? \"disabled\" : \"\")\">");
        sb.AppendLine("                            <a class=\"page-link\" asp-page=\"./Index\" asp-route-page=\"@(Model.Data.CurrentPage + 1)\" ");
        sb.AppendLine("                               asp-route-searchTerm=\"@Model.Filter.SearchTerm\" asp-route-pageSize=\"@Model.Filter.PageSize\">");
        sb.AppendLine("                                Next");
        sb.AppendLine("                            </a>");
        sb.AppendLine("                        </li>");
        sb.AppendLine("                    </ul>");
        sb.AppendLine("                </nav>");
        sb.AppendLine("            }");
        sb.AppendLine();

        // Summary
        sb.AppendLine("            <div class=\"d-flex justify-content-between align-items-center mt-3\">");
        sb.AppendLine("                <small class=\"text-muted\">");
        sb.AppendLine("                    Showing @((Model.Data.CurrentPage - 1) * Model.Data.PageSize + 1) to ");
        sb.AppendLine("                    @Math.Min(Model.Data.CurrentPage * Model.Data.PageSize, Model.Data.TotalItems) ");
        sb.AppendLine("                    of @Model.Data.TotalItems entries");
        sb.AppendLine("                </small>");
        sb.AppendLine("            </div>");
        sb.AppendLine("        }");
        sb.AppendLine("        else");
        sb.AppendLine("        {");
        sb.AppendLine("            <div class=\"text-center py-5\">");
        sb.AppendLine("                <i class=\"fas fa-inbox fa-3x text-muted mb-3\"></i>");
        sb.AppendLine("                <h5 class=\"text-muted\">No records found</h5>");
        sb.AppendLine("                <p class=\"text-muted\">There are no records to display.</p>");
        sb.AppendLine($"                <a asp-page=\"./{table.Name}/Create\" class=\"btn btn-primary\">");
        sb.AppendLine("                    <i class=\"fas fa-plus\"></i> Add First Record");
        sb.AppendLine("                </a>");
        sb.AppendLine("            </div>");
        sb.AppendLine("        }");
        sb.AppendLine("    </div>");
        sb.AppendLine("</div>");
        sb.AppendLine();

        // Helper functions
        sb.AppendLine("@functions {");
        sb.AppendLine("    private object? GetPropertyValue(object obj, string propertyName)");
        sb.AppendLine("    {");
        sb.AppendLine("        return obj?.GetType()?.GetProperty(propertyName)?.GetValue(obj);");
        sb.AppendLine("    }");
        sb.AppendLine("}");

        return sb.ToString();
    }

    /// <summary>
    /// Generate edit page template
    /// </summary>
    /// <param name="table">Table definition</param>
    /// <returns>Razor page content</returns>
    public string GenerateEditPageTemplate(TableDefinition table)
    {
        // Similar to create but with edit-specific logic
        var content = GenerateCreatePageTemplate(table);
        return content.Replace("Create", "Edit").Replace("@page", "@page \"{id:int}\"");
    }

    /// <summary>
    /// Generate details page template
    /// </summary>
    /// <param name="table">Table definition</param>
    /// <returns>Razor page content</returns>
    public string GenerateDetailsPageTemplate(TableDefinition table)
    {
        var sb = new StringBuilder();
        var modelName = GetModelName(table.Name);
        var displayFields = table.Fields.Where(f => f.UIConfiguration.ShowInDetails).ToList();

        sb.AppendLine("@page \"{id:int}\"");
        sb.AppendLine($"@model {modelName}DetailsModel");
        sb.AppendLine("@{");
        sb.AppendLine($"    ViewData[\"Title\"] = \"{table.DisplayName} Details\";");
        sb.AppendLine("}");
        sb.AppendLine();

        // Page header
        sb.AppendLine("<div class=\"d-flex justify-content-between align-items-center mb-4\">");
        sb.AppendLine($"    <h1><i class=\"fas fa-eye\"></i> {table.DisplayName} Details</h1>");
        sb.AppendLine("    <div>");
        sb.AppendLine($"        <a asp-page=\"./Edit\" asp-route-id=\"@Model.Entity.Id\" class=\"btn btn-warning\">");
        sb.AppendLine("            <i class=\"fas fa-edit\"></i> Edit");
        sb.AppendLine("        </a>");
        sb.AppendLine($"        <a asp-page=\"./Index\" class=\"btn btn-outline-secondary\">");
        sb.AppendLine("            <i class=\"fas fa-arrow-left\"></i> Back to List");
        sb.AppendLine("        </a>");
        sb.AppendLine("    </div>");
        sb.AppendLine("</div>");
        sb.AppendLine();

        // Details card
        sb.AppendLine("<div class=\"card\">");
        sb.AppendLine("    <div class=\"card-body\">");
        sb.AppendLine("        <dl class=\"row\">");

        foreach (var field in displayFields.OrderBy(f => f.UIConfiguration.Order))
        {
            var propertyName = GetPropertyName(field.Name);
            sb.AppendLine("            <dt class=\"col-sm-3\">");
            sb.AppendLine($"                {field.DisplayName}");
            sb.AppendLine("            </dt>");
            sb.AppendLine("            <dd class=\"col-sm-9\">");
            sb.AppendLine($"                @Model.Entity.{propertyName}");
            sb.AppendLine("            </dd>");
        }

        sb.AppendLine("        </dl>");
        sb.AppendLine("    </div>");
        sb.AppendLine("</div>");

        return sb.ToString();
    }

    /// <summary>
    /// Generate delete page template
    /// </summary>
    /// <param name="table">Table definition</param>
    /// <returns>Razor page content</returns>
    public string GenerateDeletePageTemplate(TableDefinition table)
    {
        var sb = new StringBuilder();
        var modelName = GetModelName(table.Name);

        sb.AppendLine("@page \"{id:int}\"");
        sb.AppendLine($"@model {modelName}DeleteModel");
        sb.AppendLine("@{");
        sb.AppendLine($"    ViewData[\"Title\"] = \"Delete {table.DisplayName}\";");
        sb.AppendLine("}");
        sb.AppendLine();

        sb.AppendLine("<div class=\"alert alert-danger\">");
        sb.AppendLine("    <h4><i class=\"fas fa-exclamation-triangle\"></i> Confirm Delete</h4>");
        sb.AppendLine($"    <p>Are you sure you want to delete this {table.DisplayName.ToLower()}?</p>");
        sb.AppendLine("</div>");
        sb.AppendLine();

        sb.AppendLine("<div class=\"card\">");
        sb.AppendLine("    <div class=\"card-body\">");
        sb.AppendLine("        <form method=\"post\">");
        sb.AppendLine("            <button type=\"submit\" class=\"btn btn-danger\">");
        sb.AppendLine("                <i class=\"fas fa-trash\"></i> Delete");
        sb.AppendLine("            </button>");
        sb.AppendLine($"            <a asp-page=\"./Index\" class=\"btn btn-secondary ms-2\">");
        sb.AppendLine("                <i class=\"fas fa-times\"></i> Cancel");
        sb.AppendLine("            </a>");
        sb.AppendLine("        </form>");
        sb.AppendLine("    </div>");
        sb.AppendLine("</div>");

        return sb.ToString();
    }

    /// <summary>
    /// Generate create page template
    /// </summary>
    /// <param name="table">Table definition</param>
    /// <returns>Razor page content</returns>
    public string GenerateCreatePageTemplate(TableDefinition table)
    {
        var sb = new StringBuilder();
        var modelName = GetModelName(table.Name);
        var createFields = table.Fields.Where(f => f.UIConfiguration.ShowInCreate && !f.IsIdentity).ToList();

        sb.AppendLine("@page");
        sb.AppendLine($"@model {modelName}CreateModel");
        sb.AppendLine("@{");
        sb.AppendLine($"    ViewData[\"Title\"] = \"Create {table.DisplayName}\";");
        sb.AppendLine("}");
        sb.AppendLine();

        // Page header
        sb.AppendLine("<div class=\"d-flex justify-content-between align-items-center mb-4\">");
        sb.AppendLine($"    <h1><i class=\"fas fa-plus\"></i> Create {table.DisplayName}</h1>");
        sb.AppendLine("    <div>");
        sb.AppendLine($"        <a asp-page=\"./Index\" class=\"btn btn-outline-secondary\">");
        sb.AppendLine("            <i class=\"fas fa-arrow-left\"></i> Back to List");
        sb.AppendLine("        </a>");
        sb.AppendLine("    </div>");
        sb.AppendLine("</div>");
        sb.AppendLine();

        // Form
        sb.AppendLine("<div class=\"card\">");
        sb.AppendLine("    <div class=\"card-body\">");
        sb.AppendLine("        <form method=\"post\">");
        sb.AppendLine("            <div asp-validation-summary=\"ModelOnly\" class=\"text-danger mb-3\"></div>");
        sb.AppendLine();

        // Form fields
        foreach (var field in createFields.OrderBy(f => f.UIConfiguration.Order))
        {
            GenerateFormField(sb, field, "Model.Entity");
        }

        // Form buttons
        sb.AppendLine("            <div class=\"row mt-4\">");
        sb.AppendLine("                <div class=\"col-12\">");
        sb.AppendLine("                    <button type=\"submit\" class=\"btn btn-primary\">");
        sb.AppendLine("                        <i class=\"fas fa-save\"></i> Create");
        sb.AppendLine("                    </button>");
        sb.AppendLine($"                    <a asp-page=\"./Index\" class=\"btn btn-secondary ms-2\">");
        sb.AppendLine("                        <i class=\"fas fa-times\"></i> Cancel");
        sb.AppendLine("                    </a>");
        sb.AppendLine("                </div>");
        sb.AppendLine("            </div>");
        sb.AppendLine("        </form>");
        sb.AppendLine("    </div>");
        sb.AppendLine("</div>");
        sb.AppendLine();

        // Validation scripts
        sb.AppendLine("@section Scripts {");
        sb.AppendLine("    @{await Html.RenderPartialAsync(\"_ValidationScriptsPartial\");}");
        sb.AppendLine("}");

        return sb.ToString();
    }

    private void GenerateFormField(StringBuilder sb, FieldDefinition field, string modelPrefix)
    {
        var propertyName = GetPropertyName(field.Name);
        var fieldId = $"{modelPrefix.Replace(".", "_")}_{propertyName}";

        sb.AppendLine("            <div class=\"mb-3\">");
        sb.AppendLine($"                <label asp-for=\"{modelPrefix}.{propertyName}\" class=\"form-label\">");
        sb.AppendLine($"                    {field.DisplayName}");
        if (!field.IsNullable)
        {
            sb.AppendLine("                    <span class=\"text-danger\">*</span>");
        }
        sb.AppendLine("                </label>");

        // Generate input based on field type
        switch (field.UIConfiguration.InputType)
        {
            case InputType.Checkbox:
                sb.AppendLine("                <div class=\"form-check\">");
                sb.AppendLine($"                    <input asp-for=\"{modelPrefix}.{propertyName}\" class=\"form-check-input\" type=\"checkbox\" id=\"{fieldId}\">");
                sb.AppendLine($"                    <label class=\"form-check-label\" for=\"{fieldId}\">");
                sb.AppendLine($"                        {field.DisplayName}");
                sb.AppendLine("                    </label>");
                sb.AppendLine("                </div>");
                break;

            case InputType.Select:
                if (field.ForeignKeyReference != null)
                {
                    sb.AppendLine($"                <select asp-for=\"{modelPrefix}.{propertyName}\" class=\"form-select\">");
                    sb.AppendLine("                    <option value=\"\">-- Select --</option>");
                    sb.AppendLine($"                    @foreach (var option in Model.{propertyName}Options)");
                    sb.AppendLine("                    {");
                    sb.AppendLine("                        <option value=\"@option.Value\">@option.Text</option>");
                    sb.AppendLine("                    }");
                    sb.AppendLine("                </select>");
                }
                else
                {
                    sb.AppendLine($"                <select asp-for=\"{modelPrefix}.{propertyName}\" class=\"form-select\">");
                    sb.AppendLine("                    <option value=\"\">-- Select --</option>");
                    sb.AppendLine("                </select>");
                }
                break;

            case InputType.Textarea:
                sb.AppendLine($"                <textarea asp-for=\"{modelPrefix}.{propertyName}\" class=\"form-control\" rows=\"4\"");
                if (!string.IsNullOrEmpty(field.UIConfiguration.Placeholder))
                {
                    sb.AppendLine($"                          placeholder=\"{field.UIConfiguration.Placeholder}\"");
                }
                sb.AppendLine("></textarea>");
                break;

            default:
                var inputType = field.UIConfiguration.InputType.ToString().ToLower();
                sb.AppendLine($"                <input asp-for=\"{modelPrefix}.{propertyName}\" class=\"form-control\" type=\"{inputType}\"");
                if (!string.IsNullOrEmpty(field.UIConfiguration.Placeholder))
                {
                    sb.AppendLine($"                       placeholder=\"{field.UIConfiguration.Placeholder}\"");
                }
                if (field.MaxLength.HasValue)
                {
                    sb.AppendLine($"                       maxlength=\"{field.MaxLength}\"");
                }
                sb.AppendLine("                />");
                break;
        }

        sb.AppendLine($"                <span asp-validation-for=\"{modelPrefix}.{propertyName}\" class=\"text-danger\"></span>");
        
        if (!string.IsNullOrEmpty(field.UIConfiguration.HelpText))
        {
            sb.AppendLine($"                <div class=\"form-text\">{field.UIConfiguration.HelpText}</div>");
        }
        
        sb.AppendLine("            </div>");
        sb.AppendLine();
    }

    private string GetModelName(string tableName)
    {
        return ToPascalCase(tableName);
    }

    private string GetPropertyName(string fieldName)
    {
        return ToPascalCase(fieldName);
    }

    private string ToPascalCase(string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;

        // Handle snake_case
        if (input.Contains('_'))
        {
            var parts = input.Split('_', StringSplitOptions.RemoveEmptyEntries);
            return string.Join("", parts.Select(part => 
                char.ToUpper(part[0]) + part.Substring(1).ToLower()));
        }

        // Handle camelCase or PascalCase
        return char.ToUpper(input[0]) + input.Substring(1);
    }
}
