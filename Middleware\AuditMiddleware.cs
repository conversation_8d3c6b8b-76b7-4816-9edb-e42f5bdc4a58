using Crudible.Services.Interfaces;
using Crudible.Models;
using System.Diagnostics;

namespace Crudible.Middleware;

/// <summary>
/// Middleware for automatic audit logging of HTTP requests
/// </summary>
public class AuditMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<AuditMiddleware> _logger;

    public AuditMiddleware(RequestDelegate next, ILogger<AuditMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // Skip audit for certain paths
        if (ShouldSkipAudit(context.Request.Path))
        {
            await _next(context);
            return;
        }

        var stopwatch = Stopwatch.StartNew();
        var originalBodyStream = context.Response.Body;

        try
        {
            using var responseBody = new MemoryStream();
            context.Response.Body = responseBody;

            await _next(context);

            stopwatch.Stop();

            // Log the request if it's a data modification operation
            if (ShouldLogRequest(context))
            {
                await LogRequestAsync(context, stopwatch.ElapsedMilliseconds);
            }

            // Copy response back to original stream
            responseBody.Seek(0, SeekOrigin.Begin);
            await responseBody.CopyToAsync(originalBodyStream);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            
            // Log failed request
            if (ShouldLogRequest(context))
            {
                await LogRequestAsync(context, stopwatch.ElapsedMilliseconds, ex);
            }
            
            throw;
        }
        finally
        {
            context.Response.Body = originalBodyStream;
        }
    }

    private bool ShouldSkipAudit(PathString path)
    {
        var pathValue = path.Value?.ToLower() ?? "";
        
        // Skip static files, health checks, and system endpoints
        return pathValue.StartsWith("/css/") ||
               pathValue.StartsWith("/js/") ||
               pathValue.StartsWith("/images/") ||
               pathValue.StartsWith("/lib/") ||
               pathValue.StartsWith("/favicon") ||
               pathValue.StartsWith("/health") ||
               pathValue.StartsWith("/api/health") ||
               pathValue.Contains("/audit/") || // Avoid recursive audit logging
               pathValue.EndsWith(".map");
    }

    private bool ShouldLogRequest(HttpContext context)
    {
        var method = context.Request.Method.ToUpper();
        var path = context.Request.Path.Value?.ToLower() ?? "";

        // Log data modification operations
        if (method == "POST" || method == "PUT" || method == "PATCH" || method == "DELETE")
        {
            return true;
        }

        // Log specific GET operations (exports, reports)
        if (method == "GET" && (path.Contains("/export") || path.Contains("/report")))
        {
            return true;
        }

        return false;
    }

    private async Task LogRequestAsync(HttpContext context, long durationMs, Exception? exception = null)
    {
        try
        {
            var auditService = context.RequestServices.GetService<IAuditService>();
            if (auditService == null)
            {
                return;
            }

            var operation = DetermineOperation(context);
            var (tableName, recordId) = ExtractTableAndRecordInfo(context);

            if (string.IsNullOrEmpty(tableName))
            {
                return; // Skip if we can't determine the table
            }

            var entry = new AuditEntry
            {
                TableName = tableName,
                RecordId = recordId ?? "UNKNOWN",
                Operation = operation,
                Success = exception == null && context.Response.StatusCode < 400,
                ErrorMessage = exception?.Message,
                DurationMs = durationMs,
                Metadata = CreateMetadata(context)
            };

            await auditService.LogAuditEntryAsync(entry);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to log audit entry for request {Method} {Path}", 
                context.Request.Method, context.Request.Path);
        }
    }

    private AuditOperation DetermineOperation(HttpContext context)
    {
        var method = context.Request.Method.ToUpper();
        var path = context.Request.Path.Value?.ToLower() ?? "";

        if (path.Contains("/export"))
        {
            return AuditOperation.Export;
        }

        return method switch
        {
            "POST" => AuditOperation.Create,
            "PUT" or "PATCH" => AuditOperation.Update,
            "DELETE" => AuditOperation.Delete,
            "GET" => AuditOperation.Read,
            _ => AuditOperation.Read
        };
    }

    private (string? tableName, string? recordId) ExtractTableAndRecordInfo(HttpContext context)
    {
        var path = context.Request.Path.Value ?? "";
        var segments = path.Split('/', StringSplitOptions.RemoveEmptyEntries);

        // Pattern: /tables/{tableName} or /tables/{tableName}/{action} or /tables/{tableName}/{id}
        if (segments.Length >= 2 && segments[0].Equals("tables", StringComparison.OrdinalIgnoreCase))
        {
            var tableName = segments[1];
            string? recordId = null;

            // Try to extract record ID
            if (segments.Length >= 3)
            {
                // Check if the third segment is a number (likely an ID)
                if (int.TryParse(segments[2], out _) || Guid.TryParse(segments[2], out _))
                {
                    recordId = segments[2];
                }
            }

            // Try to get ID from route values
            if (string.IsNullOrEmpty(recordId) && context.Request.RouteValues.ContainsKey("id"))
            {
                recordId = context.Request.RouteValues["id"]?.ToString();
            }

            // Try to get ID from query string
            if (string.IsNullOrEmpty(recordId) && context.Request.Query.ContainsKey("id"))
            {
                recordId = context.Request.Query["id"].FirstOrDefault();
            }

            return (tableName, recordId);
        }

        // Pattern: /api/tables/{tableName}
        if (segments.Length >= 3 && 
            segments[0].Equals("api", StringComparison.OrdinalIgnoreCase) &&
            segments[1].Equals("tables", StringComparison.OrdinalIgnoreCase))
        {
            var tableName = segments[2];
            string? recordId = null;

            if (segments.Length >= 4)
            {
                recordId = segments[3];
            }

            return (tableName, recordId);
        }

        // Pattern: /api/export/{tableName}
        if (segments.Length >= 3 && 
            segments[0].Equals("api", StringComparison.OrdinalIgnoreCase) &&
            segments[1].Equals("export", StringComparison.OrdinalIgnoreCase))
        {
            var tableName = segments[2];
            return (tableName, "EXPORT");
        }

        return (null, null);
    }

    private string CreateMetadata(HttpContext context)
    {
        var metadata = new Dictionary<string, object>
        {
            ["Method"] = context.Request.Method,
            ["Path"] = context.Request.Path.Value ?? "",
            ["QueryString"] = context.Request.QueryString.Value ?? "",
            ["StatusCode"] = context.Response.StatusCode,
            ["ContentType"] = context.Request.ContentType ?? "",
            ["UserAgent"] = context.Request.Headers["User-Agent"].FirstOrDefault() ?? "",
            ["Referer"] = context.Request.Headers["Referer"].FirstOrDefault() ?? ""
        };

        // Add form data for POST requests (excluding sensitive fields)
        if (context.Request.Method.Equals("POST", StringComparison.OrdinalIgnoreCase) && 
            context.Request.HasFormContentType)
        {
            try
            {
                var formData = new Dictionary<string, string>();
                foreach (var item in context.Request.Form)
                {
                    // Skip sensitive fields
                    if (IsSensitiveField(item.Key))
                    {
                        formData[item.Key] = "[REDACTED]";
                    }
                    else
                    {
                        formData[item.Key] = item.Value.ToString();
                    }
                }
                metadata["FormData"] = formData;
            }
            catch
            {
                // Ignore form parsing errors
            }
        }

        return System.Text.Json.JsonSerializer.Serialize(metadata);
    }

    private bool IsSensitiveField(string fieldName)
    {
        var sensitiveFields = new[]
        {
            "password", "pwd", "secret", "token", "key", "connectionstring",
            "ssn", "socialsecurity", "creditcard", "cvv", "pin"
        };

        return sensitiveFields.Any(field => 
            fieldName.Contains(field, StringComparison.OrdinalIgnoreCase));
    }
}

/// <summary>
/// Middleware for handling errors and logging them
/// </summary>
public class ErrorHandlingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<ErrorHandlingMiddleware> _logger;

    public ErrorHandlingMiddleware(RequestDelegate next, ILogger<ErrorHandlingMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unhandled exception occurred for request {Method} {Path}", 
                context.Request.Method, context.Request.Path);

            await HandleExceptionAsync(context, ex);
        }
    }

    private async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        context.Response.StatusCode = 500;
        context.Response.ContentType = "application/json";

        var response = new
        {
            error = new
            {
                message = "An error occurred while processing your request.",
                details = context.RequestServices.GetRequiredService<IWebHostEnvironment>().IsDevelopment() 
                    ? exception.ToString() 
                    : "Please contact support if the problem persists."
            }
        };

        var jsonResponse = System.Text.Json.JsonSerializer.Serialize(response);
        await context.Response.WriteAsync(jsonResponse);
    }
}
