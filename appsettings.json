{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Crudible": "Information"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=CrudibleDb;Trusted_Connection=true;MultipleActiveResultSets=true"}, "Crudible": {"Database": {"Provider": "SqlServer", "CommandTimeout": 30, "EnablePooling": true}, "Schema": {"AutoDetectRelationships": true, "IncludedTables": [], "ExcludedTables": [], "ValidationRules": {}}, "UI": {"ApplicationTitle": "Crudible - Enterprise CRUD Generator", "ItemsPerPage": 25, "EnableResponsiveDesign": true, "BootstrapTheme": "default", "ShowAuditInfo": true}, "Export": {"EnablePdfExport": true, "EnableExcelExport": true, "MaxExportRecords": 10000, "FileNameTemplate": "{TableName}_{DateTime:yyyyMMdd_HHmmss}"}, "Audit": {"EnableAuditTrail": true, "AuditTableName": "AuditLog", "RetentionDays": 365, "LogReadOperations": false, "LogCreateOperations": true, "LogUpdateOperations": true, "LogDeleteOperations": true}}}