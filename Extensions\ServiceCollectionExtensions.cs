using Crudible.Configuration;
using Crudible.Services;
using Crudible.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using Crudible.Data;
using Crudible.Middleware;

namespace Crudible.Extensions;

/// <summary>
/// Extension methods for IServiceCollection
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Add Crudible services to the service collection
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configuration">Configuration</param>
    /// <returns>Service collection</returns>
    public static IServiceCollection AddCrudibleServices(
        this IServiceCollection services, 
        IConfiguration configuration)
    {
        // Configure options
        services.Configure<CrudibleConfiguration>(configuration.GetSection("Crudible"));

        // Add core services
        services.AddScoped<ConfigurationService>();
        services.AddScoped<ISchemaProcessor, SchemaProcessor>();
        services.AddScoped<SchemaProcessor>();
        services.AddScoped<ICrudPageGenerator, CrudPageGenerator>();
        services.AddScoped<IAuditService, AuditService>();
        services.AddScoped<IExportService, ExportService>();
        services.AddScoped<IValidationService, ValidationService>();

        // Add utility services
        services.AddScoped<DatabaseService>();
        services.AddScoped<EntityModelGenerator>();
        services.AddScoped<DynamicDbContextService>();
        services.AddScoped<TemplateService>();
        services.AddScoped<FileService>();
        services.AddScoped<XmlSchemaParser>();
        services.AddScoped<DatabaseSchemaExtractor>();
        services.AddScoped<TableDataService>();

        // Add Entity Framework
        var connectionString = configuration.GetConnectionString("DefaultConnection");
        if (!string.IsNullOrEmpty(connectionString))
        {
            services.AddDbContext<CrudibleDbContext>(options =>
                options.UseSqlServer(connectionString));
        }

        // Add memory cache
        services.AddMemoryCache();

        // Add HTTP context accessor
        services.AddHttpContextAccessor();

        // Add HTTP client
        services.AddHttpClient();

        return services;
    }

    /// <summary>
    /// Add Crudible database context
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="connectionString">Database connection string</param>
    /// <returns>Service collection</returns>
    public static IServiceCollection AddCrudibleDbContext(
        this IServiceCollection services,
        string connectionString)
    {
        services.AddDbContext<CrudibleDbContext>(options =>
            options.UseSqlServer(connectionString, sqlOptions =>
            {
                sqlOptions.EnableRetryOnFailure(
                    maxRetryCount: 3,
                    maxRetryDelay: TimeSpan.FromSeconds(30),
                    errorNumbersToAdd: null);
            }));

        return services;
    }

    /// <summary>
    /// Add Crudible configuration
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configureOptions">Configuration action</param>
    /// <returns>Service collection</returns>
    public static IServiceCollection AddCrudibleConfiguration(
        this IServiceCollection services,
        Action<CrudibleConfiguration> configureOptions)
    {
        services.Configure(configureOptions);
        return services;
    }

    /// <summary>
    /// Add Crudible audit services
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection</returns>
    public static IServiceCollection AddCrudibleAudit(this IServiceCollection services)
    {
        services.AddScoped<IAuditService, AuditService>();
        services.AddScoped<AuditMiddleware>();
        return services;
    }

    /// <summary>
    /// Add Crudible export services
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection</returns>
    public static IServiceCollection AddCrudibleExport(this IServiceCollection services)
    {
        services.AddScoped<IExportService, ExportService>();
        
        // Configure EPPlus
        OfficeOpenXml.ExcelPackage.LicenseContext = OfficeOpenXml.LicenseContext.NonCommercial;
        
        return services;
    }

    /// <summary>
    /// Add Crudible validation services
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection</returns>
    public static IServiceCollection AddCrudibleValidation(this IServiceCollection services)
    {
        services.AddScoped<IValidationService, ValidationService>();
        return services;
    }

    /// <summary>
    /// Add Crudible schema processing services
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection</returns>
    public static IServiceCollection AddCrudibleSchemaProcessing(this IServiceCollection services)
    {
        services.AddScoped<ISchemaProcessor, SchemaProcessor>();
        services.AddScoped<XmlSchemaParser>();
        services.AddScoped<DatabaseSchemaExtractor>();
        return services;
    }

    /// <summary>
    /// Add Crudible page generation services
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection</returns>
    public static IServiceCollection AddCrudiblePageGeneration(this IServiceCollection services)
    {
        services.AddScoped<ICrudPageGenerator, CrudPageGenerator>();
        services.AddScoped<TemplateService>();

        return services;
    }
}

/// <summary>
/// Extension methods for IApplicationBuilder
/// </summary>
public static class ApplicationBuilderExtensions
{
    /// <summary>
    /// Use Crudible middleware
    /// </summary>
    /// <param name="app">Application builder</param>
    /// <returns>Application builder</returns>
    public static IApplicationBuilder UseCrudible(this IApplicationBuilder app)
    {
        // Add audit middleware
        app.UseMiddleware<AuditMiddleware>();

        // Add error handling middleware
        app.UseMiddleware<ErrorHandlingMiddleware>();

        return app;
    }

    /// <summary>
    /// Use Crudible audit middleware
    /// </summary>
    /// <param name="app">Application builder</param>
    /// <returns>Application builder</returns>
    public static IApplicationBuilder UseCrudibleAudit(this IApplicationBuilder app)
    {
        app.UseMiddleware<AuditMiddleware>();
        return app;
    }

    /// <summary>
    /// Use Crudible error handling
    /// </summary>
    /// <param name="app">Application builder</param>
    /// <returns>Application builder</returns>
    public static IApplicationBuilder UseCrudibleErrorHandling(this IApplicationBuilder app)
    {
        app.UseMiddleware<ErrorHandlingMiddleware>();
        return app;
    }

    /// <summary>
    /// Initialize Crudible database
    /// </summary>
    /// <param name="app">Application builder</param>
    /// <returns>Application builder</returns>
    public static IApplicationBuilder InitializeCrudibleDatabase(this IApplicationBuilder app)
    {
        using var scope = app.ApplicationServices.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<CrudibleDbContext>();
        
        try
        {
            context.Database.EnsureCreated();
        }
        catch (Exception ex)
        {
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<CrudibleDbContext>>();
            logger.LogError(ex, "Failed to initialize Crudible database");
        }

        return app;
    }
}
