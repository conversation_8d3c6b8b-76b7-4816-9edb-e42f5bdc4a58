using System.Text;
using OfficeOpenXml;
using Crudible.Models;
using Crudible.Services.Interfaces;
using Newtonsoft.Json;

namespace Crudible.Services;

/// <summary>
/// Service for exporting data in various formats
/// </summary>
public class ExportService : IExportService
{
    private readonly DatabaseService _databaseService;
    private readonly ConfigurationService _configurationService;
    private readonly IAuditService _auditService;
    private readonly ILogger<ExportService> _logger;

    public ExportService(
        DatabaseService databaseService,
        ConfigurationService configurationService,
        IAuditService auditService,
        ILogger<ExportService> logger)
    {
        _databaseService = databaseService;
        _configurationService = configurationService;
        _auditService = auditService;
        _logger = logger;
    }

    /// <summary>
    /// Export table data to Excel format
    /// </summary>
    /// <param name="request">Export request</param>
    /// <returns>Excel file as byte array</returns>
    public async Task<byte[]> ExportToExcelAsync(ExportRequest request)
    {
        try
        {
            _logger.LogInformation("Starting Excel export for table {TableName}", request.TableName);

            var config = await _configurationService.LoadConfigurationAsync();
            var tables = await GetTableDefinitionsAsync();
            var tableDefinition = tables.FirstOrDefault(t => t.Name == request.TableName);

            if (tableDefinition == null)
            {
                throw new ArgumentException($"Table '{request.TableName}' not found");
            }

            // Get data with filters
            var filter = request.Filter ?? new ListFilter();
            filter.PageSize = Math.Min(filter.PageSize, config.Export.MaxExportRecords);
            
            var data = await _databaseService.GetTableDataAsync(
                request.TableName, 
                tables, 
                config.Database.ConnectionString ?? "", 
                filter);

            // Create Excel file
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add(tableDefinition.DisplayName);

            // Add headers
            var fieldsToExport = GetFieldsToExport(tableDefinition, request.IncludeFields);
            for (int i = 0; i < fieldsToExport.Count; i++)
            {
                worksheet.Cells[1, i + 1].Value = fieldsToExport[i].DisplayName;
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
                worksheet.Cells[1, i + 1].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                worksheet.Cells[1, i + 1].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
            }

            // Add data rows
            for (int row = 0; row < data.Items.Count; row++)
            {
                var item = data.Items[row];
                for (int col = 0; col < fieldsToExport.Count; col++)
                {
                    var field = fieldsToExport[col];
                    var value = GetPropertyValue(item, field.Name);
                    worksheet.Cells[row + 2, col + 1].Value = FormatValueForExport(value, field);
                }
            }

            // Auto-fit columns
            worksheet.Cells.AutoFitColumns();

            // Add metadata sheet
            var metadataSheet = package.Workbook.Worksheets.Add("Export Info");
            metadataSheet.Cells[1, 1].Value = "Export Information";
            metadataSheet.Cells[1, 1].Style.Font.Bold = true;
            metadataSheet.Cells[2, 1].Value = "Table:";
            metadataSheet.Cells[2, 2].Value = tableDefinition.DisplayName;
            metadataSheet.Cells[3, 1].Value = "Exported:";
            metadataSheet.Cells[3, 2].Value = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss UTC");
            metadataSheet.Cells[4, 1].Value = "Records:";
            metadataSheet.Cells[4, 2].Value = data.Items.Count;
            metadataSheet.Cells[5, 1].Value = "Total Available:";
            metadataSheet.Cells[5, 2].Value = data.TotalItems;

            var result = package.GetAsByteArray();

            // Log export
            await _auditService.LogCrudOperationAsync(
                request.TableName,
                "EXPORT",
                AuditOperation.Export,
                newValues: new { Format = "Excel", RecordCount = data.Items.Count });

            _logger.LogInformation("Excel export completed for table {TableName}, {RecordCount} records", 
                request.TableName, data.Items.Count);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to export table {TableName} to Excel", request.TableName);
            
            await _auditService.LogCrudOperationAsync(
                request.TableName,
                "EXPORT",
                AuditOperation.Export,
                success: false,
                errorMessage: ex.Message);
            
            throw;
        }
    }

    /// <summary>
    /// Export table data to CSV format
    /// </summary>
    /// <param name="request">Export request</param>
    /// <returns>CSV file as byte array</returns>
    public async Task<byte[]> ExportToCsvAsync(ExportRequest request)
    {
        try
        {
            _logger.LogInformation("Starting CSV export for table {TableName}", request.TableName);

            var config = await _configurationService.LoadConfigurationAsync();
            var tables = await GetTableDefinitionsAsync();
            var tableDefinition = tables.FirstOrDefault(t => t.Name == request.TableName);

            if (tableDefinition == null)
            {
                throw new ArgumentException($"Table '{request.TableName}' not found");
            }

            // Get data with filters
            var filter = request.Filter ?? new ListFilter();
            filter.PageSize = Math.Min(filter.PageSize, config.Export.MaxExportRecords);
            
            var data = await _databaseService.GetTableDataAsync(
                request.TableName, 
                tables, 
                config.Database.ConnectionString ?? "", 
                filter);

            var csv = new StringBuilder();
            var fieldsToExport = GetFieldsToExport(tableDefinition, request.IncludeFields);

            // Add headers
            csv.AppendLine(string.Join(",", fieldsToExport.Select(f => EscapeCsvValue(f.DisplayName))));

            // Add data rows
            foreach (var item in data.Items)
            {
                var values = fieldsToExport.Select(field =>
                {
                    var value = GetPropertyValue(item, field.Name);
                    return EscapeCsvValue(FormatValueForExport(value, field)?.ToString() ?? "");
                });
                csv.AppendLine(string.Join(",", values));
            }

            var result = Encoding.UTF8.GetBytes(csv.ToString());

            // Log export
            await _auditService.LogCrudOperationAsync(
                request.TableName,
                "EXPORT",
                AuditOperation.Export,
                newValues: new { Format = "CSV", RecordCount = data.Items.Count });

            _logger.LogInformation("CSV export completed for table {TableName}, {RecordCount} records", 
                request.TableName, data.Items.Count);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to export table {TableName} to CSV", request.TableName);
            
            await _auditService.LogCrudOperationAsync(
                request.TableName,
                "EXPORT",
                AuditOperation.Export,
                success: false,
                errorMessage: ex.Message);
            
            throw;
        }
    }

    /// <summary>
    /// Export table data to JSON format
    /// </summary>
    /// <param name="request">Export request</param>
    /// <returns>JSON file as byte array</returns>
    public async Task<byte[]> ExportToJsonAsync(ExportRequest request)
    {
        try
        {
            _logger.LogInformation("Starting JSON export for table {TableName}", request.TableName);

            var config = await _configurationService.LoadConfigurationAsync();
            var tables = await GetTableDefinitionsAsync();
            var tableDefinition = tables.FirstOrDefault(t => t.Name == request.TableName);

            if (tableDefinition == null)
            {
                throw new ArgumentException($"Table '{request.TableName}' not found");
            }

            // Get data with filters
            var filter = request.Filter ?? new ListFilter();
            filter.PageSize = Math.Min(filter.PageSize, config.Export.MaxExportRecords);
            
            var data = await _databaseService.GetTableDataAsync(
                request.TableName, 
                tables, 
                config.Database.ConnectionString ?? "", 
                filter);

            var fieldsToExport = GetFieldsToExport(tableDefinition, request.IncludeFields);

            // Convert to JSON-friendly format
            var exportData = data.Items.Select(item =>
            {
                var record = new Dictionary<string, object?>();
                foreach (var field in fieldsToExport)
                {
                    var value = GetPropertyValue(item, field.Name);
                    record[field.Name] = FormatValueForExport(value, field);
                }
                return record;
            }).ToList();

            var exportObject = new
            {
                TableName = request.TableName,
                DisplayName = tableDefinition.DisplayName,
                ExportedAt = DateTime.UtcNow,
                RecordCount = data.Items.Count,
                TotalRecords = data.TotalItems,
                Data = exportData
            };

            var json = JsonConvert.SerializeObject(exportObject, Formatting.Indented);
            var result = Encoding.UTF8.GetBytes(json);

            // Log export
            await _auditService.LogCrudOperationAsync(
                request.TableName,
                "EXPORT",
                AuditOperation.Export,
                newValues: new { Format = "JSON", RecordCount = data.Items.Count });

            _logger.LogInformation("JSON export completed for table {TableName}, {RecordCount} records", 
                request.TableName, data.Items.Count);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to export table {TableName} to JSON", request.TableName);
            
            await _auditService.LogCrudOperationAsync(
                request.TableName,
                "EXPORT",
                AuditOperation.Export,
                success: false,
                errorMessage: ex.Message);
            
            throw;
        }
    }

    /// <summary>
    /// Get export file name based on template
    /// </summary>
    /// <param name="tableName">Table name</param>
    /// <param name="format">Export format</param>
    /// <returns>Generated file name</returns>
    public async Task<string> GetExportFileNameAsync(string tableName, Models.ExportFormat format)
    {
        var config = await _configurationService.LoadConfigurationAsync();
        var template = config.Export.FileNameTemplate ?? "{TableName}_{DateTime:yyyyMMdd_HHmmss}";
        
        var fileName = template
            .Replace("{TableName}", tableName)
            .Replace("{DateTime:yyyyMMdd_HHmmss}", DateTime.UtcNow.ToString("yyyyMMdd_HHmmss"))
            .Replace("{DateTime:yyyy-MM-dd}", DateTime.UtcNow.ToString("yyyy-MM-dd"));

        var extension = format switch
        {
            Models.ExportFormat.Excel => ".xlsx",
            Models.ExportFormat.Csv => ".csv",
            Models.ExportFormat.Json => ".json",
            Models.ExportFormat.Pdf => ".pdf",
            _ => ".txt"
        };

        return fileName + extension;
    }

    private async Task<List<TableDefinition>> GetTableDefinitionsAsync()
    {
        // This would normally load from the schema processor
        // For now, return sample table definitions
        return new List<TableDefinition>
        {
            new TableDefinition
            {
                Name = "Categories",
                DisplayName = "Categories",
                Fields = new List<FieldDefinition>
                {
                    new() { Name = "CategoryId", DisplayName = "ID", DataType = FieldDataType.Integer },
                    new() { Name = "CategoryName", DisplayName = "Name", DataType = FieldDataType.String },
                    new() { Name = "Description", DisplayName = "Description", DataType = FieldDataType.String }
                }
            }
        };
    }

    private List<FieldDefinition> GetFieldsToExport(TableDefinition table, List<string>? includeFields)
    {
        if (includeFields?.Any() == true)
        {
            return table.Fields.Where(f => includeFields.Contains(f.Name)).ToList();
        }

        // Export all fields except binary and large text fields
        return table.Fields
            .Where(f => f.DataType != FieldDataType.Binary && 
                       (f.MaxLength ?? 0) <= 1000)
            .ToList();
    }

    private object? GetPropertyValue(object obj, string propertyName)
    {
        return obj?.GetType()?.GetProperty(propertyName)?.GetValue(obj);
    }

    private object? FormatValueForExport(object? value, FieldDefinition field)
    {
        if (value == null)
            return null;

        return field.DataType switch
        {
            FieldDataType.DateTime => ((DateTime)value).ToString("yyyy-MM-dd HH:mm:ss"),
            FieldDataType.Date => ((DateTime)value).ToString("yyyy-MM-dd"),
            FieldDataType.Boolean => (bool)value ? "Yes" : "No",
            FieldDataType.Decimal => decimal.Parse(value.ToString()!).ToString("F2"),
            _ => value.ToString()
        };
    }

    private string EscapeCsvValue(string value)
    {
        if (string.IsNullOrEmpty(value))
            return "";

        // Escape quotes and wrap in quotes if contains comma, quote, or newline
        if (value.Contains(',') || value.Contains('"') || value.Contains('\n') || value.Contains('\r'))
        {
            return "\"" + value.Replace("\"", "\"\"") + "\"";
        }

        return value;
    }
}
