using System.Reflection;
using Microsoft.EntityFrameworkCore;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp;
using Crudible.Models;
using System.Runtime.Loader;

namespace Crudible.Services;

/// <summary>
/// Service for creating and managing dynamic DbContext instances from table definitions
/// </summary>
public class DynamicDbContextService
{
    private readonly EntityModelGenerator _modelGenerator;
    private readonly ILogger<DynamicDbContextService> _logger;
    private readonly Dictionary<string, Assembly> _compiledAssemblies = new();
    private readonly Dictionary<string, Type> _dbContextTypes = new();

    public DynamicDbContextService(
        EntityModelGenerator modelGenerator,
        ILogger<DynamicDbContextService> logger)
    {
        _modelGenerator = modelGenerator;
        _logger = logger;
    }

    /// <summary>
    /// Create a dynamic DbContext from table definitions
    /// </summary>
    /// <param name="tables">Table definitions</param>
    /// <param name="connectionString">Database connection string</param>
    /// <param name="contextName">Name for the DbContext class</param>
    /// <returns>DbContext instance</returns>
    public async Task<DbContext> CreateDbContextAsync(List<TableDefinition> tables, string connectionString, string contextName = "DynamicDbContext")
    {
        try
        {
            _logger.LogInformation("Creating dynamic DbContext for {TableCount} tables", tables.Count);

            // Generate assembly key for caching
            var assemblyKey = GenerateAssemblyKey(tables, contextName);

            // Check if we already have a compiled assembly for this schema
            if (!_dbContextTypes.ContainsKey(assemblyKey))
            {
                await CompileDbContextAsync(tables, contextName, assemblyKey);
            }

            // Create DbContext instance
            var contextType = _dbContextTypes[assemblyKey];
            var optionsBuilder = new DbContextOptionsBuilder();
            optionsBuilder.UseSqlServer(connectionString);

            var context = (DbContext)Activator.CreateInstance(contextType, optionsBuilder.Options)!;
            
            _logger.LogInformation("Successfully created dynamic DbContext");
            return context;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create dynamic DbContext");
            throw;
        }
    }

    /// <summary>
    /// Get entity type for a table from the dynamic DbContext
    /// </summary>
    /// <param name="context">DbContext instance</param>
    /// <param name="tableName">Table name</param>
    /// <returns>Entity type</returns>
    public Type? GetEntityType(DbContext context, string tableName)
    {
        var entityTypeName = GetEntityClassName(tableName);
        return context.Model.GetEntityTypes()
            .FirstOrDefault(et => et.ClrType.Name == entityTypeName)?.ClrType;
    }

    /// <summary>
    /// Get DbSet property for a table from the dynamic DbContext
    /// </summary>
    /// <param name="context">DbContext instance</param>
    /// <param name="tableName">Table name</param>
    /// <returns>DbSet property info</returns>
    public PropertyInfo? GetDbSetProperty(DbContext context, string tableName)
    {
        var dbSetName = GetDbSetName(tableName);
        return context.GetType().GetProperty(dbSetName);
    }

    /// <summary>
    /// Execute a query against a dynamic DbContext
    /// </summary>
    /// <param name="context">DbContext instance</param>
    /// <param name="tableName">Table name</param>
    /// <param name="filter">Optional filter criteria</param>
    /// <returns>Query results</returns>
    public async Task<List<object>> QueryTableAsync(DbContext context, string tableName, ListFilter? filter = null)
    {
        try
        {
            var dbSetProperty = GetDbSetProperty(context, tableName);
            if (dbSetProperty == null)
            {
                throw new InvalidOperationException($"DbSet for table '{tableName}' not found");
            }

            var dbSet = dbSetProperty.GetValue(context);
            if (dbSet == null)
            {
                return new List<object>();
            }

            // Use reflection to call ToListAsync
            var queryableType = dbSet.GetType();
            var toListMethod = typeof(EntityFrameworkQueryableExtensions)
                .GetMethods()
                .First(m => m.Name == "ToListAsync" && m.GetParameters().Length == 2)
                .MakeGenericMethod(queryableType.GetGenericArguments()[0]);

            var result = await (Task<object>)toListMethod.Invoke(null, new[] { dbSet, CancellationToken.None })!;
            
            if (result is System.Collections.IEnumerable enumerable)
            {
                return enumerable.Cast<object>().ToList();
            }

            return new List<object>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to query table {TableName}", tableName);
            throw;
        }
    }

    /// <summary>
    /// Create a new entity instance for a table
    /// </summary>
    /// <param name="context">DbContext instance</param>
    /// <param name="tableName">Table name</param>
    /// <param name="values">Property values</param>
    /// <returns>Created entity</returns>
    public async Task<object> CreateEntityAsync(DbContext context, string tableName, Dictionary<string, object?> values)
    {
        try
        {
            var entityType = GetEntityType(context, tableName);
            if (entityType == null)
            {
                throw new InvalidOperationException($"Entity type for table '{tableName}' not found");
            }

            var entity = Activator.CreateInstance(entityType)!;

            // Set property values
            foreach (var kvp in values)
            {
                var property = entityType.GetProperty(kvp.Key);
                if (property != null && property.CanWrite)
                {
                    var convertedValue = ConvertValue(kvp.Value, property.PropertyType);
                    property.SetValue(entity, convertedValue);
                }
            }

            // Add to context
            var dbSetProperty = GetDbSetProperty(context, tableName);
            if (dbSetProperty != null)
            {
                var dbSet = dbSetProperty.GetValue(context);
                var addMethod = dbSet?.GetType().GetMethod("Add");
                addMethod?.Invoke(dbSet, new[] { entity });
            }

            await context.SaveChangesAsync();
            return entity;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create entity for table {TableName}", tableName);
            throw;
        }
    }

    private async Task CompileDbContextAsync(List<TableDefinition> tables, string contextName, string assemblyKey)
    {
        _logger.LogInformation("Compiling dynamic DbContext assembly");

        // Generate source code
        var sourceFiles = new Dictionary<string, string>();

        // Generate entity models
        foreach (var table in tables)
        {
            var entityCode = _modelGenerator.GenerateEntityModel(table);
            var entityName = GetEntityClassName(table.Name);
            sourceFiles[$"{entityName}.cs"] = entityCode;
        }

        // Generate DbContext
        var contextCode = _modelGenerator.GenerateDbContext(tables, contextName);
        sourceFiles[$"{contextName}.cs"] = contextCode;

        // Compile assembly
        var assembly = await CompileAssemblyAsync(sourceFiles, assemblyKey);
        _compiledAssemblies[assemblyKey] = assembly;

        // Get DbContext type
        var contextType = assembly.GetType($"Crudible.Data.Generated.{contextName}");
        if (contextType == null)
        {
            throw new InvalidOperationException($"DbContext type '{contextName}' not found in compiled assembly");
        }

        _dbContextTypes[assemblyKey] = contextType;
        _logger.LogInformation("Successfully compiled dynamic DbContext assembly");
    }

    private async Task<Assembly> CompileAssemblyAsync(Dictionary<string, string> sourceFiles, string assemblyName)
    {
        var syntaxTrees = sourceFiles.Select(sf => 
            CSharpSyntaxTree.ParseText(sf.Value, path: sf.Key)).ToArray();

        var references = new[]
        {
            MetadataReference.CreateFromFile(typeof(object).Assembly.Location),
            MetadataReference.CreateFromFile(typeof(DbContext).Assembly.Location),
            MetadataReference.CreateFromFile(typeof(System.ComponentModel.DataAnnotations.KeyAttribute).Assembly.Location),
            MetadataReference.CreateFromFile(Assembly.GetExecutingAssembly().Location),
            MetadataReference.CreateFromFile(typeof(System.Runtime.AssemblyTargetedPatchBandAttribute).Assembly.Location),
            MetadataReference.CreateFromFile(typeof(Microsoft.CSharp.RuntimeBinder.CSharpArgumentInfo).Assembly.Location),
        };

        // Add additional references for .NET runtime
        var runtimePath = Path.GetDirectoryName(typeof(object).Assembly.Location)!;
        var additionalReferences = new[]
        {
            "System.Runtime.dll",
            "System.Collections.dll",
            "System.Linq.dll",
            "System.ComponentModel.Annotations.dll",
            "netstandard.dll"
        }.Where(dll => File.Exists(Path.Combine(runtimePath, dll)))
         .Select(dll => MetadataReference.CreateFromFile(Path.Combine(runtimePath, dll)));

        var allReferences = references.Concat(additionalReferences).ToArray();

        var compilation = CSharpCompilation.Create(
            assemblyName,
            syntaxTrees,
            allReferences,
            new CSharpCompilationOptions(OutputKind.DynamicallyLinkedLibrary));

        using var ms = new MemoryStream();
        var result = compilation.Emit(ms);

        if (!result.Success)
        {
            var errors = string.Join("\n", result.Diagnostics
                .Where(d => d.Severity == DiagnosticSeverity.Error)
                .Select(d => d.ToString()));
            
            throw new InvalidOperationException($"Compilation failed:\n{errors}");
        }

        ms.Seek(0, SeekOrigin.Begin);
        var assembly = AssemblyLoadContext.Default.LoadFromStream(ms);
        return assembly;
    }

    private string GenerateAssemblyKey(List<TableDefinition> tables, string contextName)
    {
        var tableSignature = string.Join("|", tables.OrderBy(t => t.Name)
            .Select(t => $"{t.Name}:{t.Fields.Count}:{string.Join(",", t.PrimaryKeys)}"));
        
        return $"{contextName}_{tableSignature.GetHashCode():X}";
    }

    private object? ConvertValue(object? value, Type targetType)
    {
        if (value == null)
            return null;

        if (targetType.IsAssignableFrom(value.GetType()))
            return value;

        // Handle nullable types
        if (targetType.IsGenericType && targetType.GetGenericTypeDefinition() == typeof(Nullable<>))
        {
            targetType = Nullable.GetUnderlyingType(targetType)!;
        }

        try
        {
            return Convert.ChangeType(value, targetType);
        }
        catch
        {
            return null;
        }
    }

    private string GetEntityClassName(string tableName)
    {
        // Convert table name to singular entity class name
        var className = tableName;
        
        // Remove common table prefixes
        if (className.StartsWith("tbl", StringComparison.OrdinalIgnoreCase))
            className = className.Substring(3);
        
        // Convert to PascalCase
        className = ToPascalCase(className);
        
        // Make singular (basic rules)
        if (className.EndsWith("ies", StringComparison.OrdinalIgnoreCase))
            className = className.Substring(0, className.Length - 3) + "y";
        else if (className.EndsWith("s", StringComparison.OrdinalIgnoreCase) && className.Length > 1)
            className = className.Substring(0, className.Length - 1);
        
        return className;
    }

    private string GetDbSetName(string tableName)
    {
        return ToPascalCase(tableName);
    }

    private string ToPascalCase(string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;

        // Handle snake_case
        if (input.Contains('_'))
        {
            var parts = input.Split('_', StringSplitOptions.RemoveEmptyEntries);
            return string.Join("", parts.Select(part => 
                char.ToUpper(part[0]) + part.Substring(1).ToLower()));
        }

        // Handle camelCase or PascalCase
        return char.ToUpper(input[0]) + input.Substring(1);
    }
}
