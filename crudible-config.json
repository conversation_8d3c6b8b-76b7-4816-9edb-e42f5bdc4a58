{"Database": {"ConnectionString": null, "Provider": 0, "CommandTimeout": 30, "EnablePooling": true}, "Schema": {"XmlSchema": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<CrudibleSchema version=\"1.0\" type=\"crudible\">\n  <Description>Test schema</Description>\n  \n  <Tables>\n    <Table name=\"Users\" schema=\"dbo\" displayName=\"Users\">\n      <Description>User accounts</Description>\n      \n      <Fields>\n        <Field name=\"UserId\" type=\"int\" primaryKey=\"true\" identity=\"true\" nullable=\"false\" displayName=\"User ID\">\n          <Description>Unique identifier</Description>\n        </Field>\n        <Field name=\"Username\" type=\"nvarchar\" maxLength=\"50\" nullable=\"false\" unique=\"true\" displayName=\"Username\">\n          <Description>Username</Description>\n        </Field>\n        <Field name=\"Email\" type=\"nvarchar\" maxLength=\"100\" nullable=\"false\" displayName=\"Email\">\n          <Description>Email address</Description>\n        </Field>\n        <Field name=\"IsActive\" type=\"bit\" nullable=\"false\" defaultValue=\"1\" displayName=\"Is Active\">\n          <Description>Whether the user is active</Description>\n        </Field>\n      </Fields>\n      \n      <PrimaryKeys>\n        <Key field=\"UserId\" />\n      </PrimaryKeys>\n    </Table>\n  </Tables>\n</CrudibleSchema>\n", "IncludedTables": ["Users"], "ExcludedTables": [], "ValidationRules": {}, "AutoDetectRelationships": true}, "UI": {"ApplicationTitle": "Crudible - Enterprise CRUD Generator", "ItemsPerPage": 25, "EnableResponsiveDesign": true, "BootstrapTheme": "default", "ShowAuditInfo": true}, "Export": {"EnablePdfExport": true, "EnableExcelExport": true, "MaxExportRecords": 10000, "FileNameTemplate": "{TableName}_{DateTime:yyyyMMdd_HHmmss}"}, "Audit": {"EnableAuditTrail": true, "AuditTableName": "AuditLog", "RetentionDays": 365, "LogReadOperations": false, "LogCreateOperations": true, "LogUpdateOperations": true, "LogDeleteOperations": true}}