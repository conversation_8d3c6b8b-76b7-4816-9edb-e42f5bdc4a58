using Microsoft.AspNetCore.Mvc;
using Crudible.Models;
using Crudible.Services;

namespace Crudible.Pages.Shared.Components.SmartInput;

/// <summary>
/// View component for rendering smart input controls based on field definitions
/// </summary>
public class SmartInputViewComponent : ViewComponent
{
    private readonly DatabaseService _databaseService;
    private readonly ConfigurationService _configurationService;

    public SmartInputViewComponent(
        DatabaseService databaseService,
        ConfigurationService configurationService)
    {
        _databaseService = databaseService;
        _configurationService = configurationService;
    }

    /// <summary>
    /// Invoke the smart input component
    /// </summary>
    /// <param name="field">Field definition</param>
    /// <param name="value">Current value</param>
    /// <param name="propertyName">Property name for model binding</param>
    /// <param name="isReadOnly">Whether the input is read-only</param>
    /// <returns>View component result</returns>
    public async Task<IViewComponentResult> InvokeAsync(
        FieldDefinition field, 
        object? value = null, 
        string? propertyName = null, 
        bool isReadOnly = false)
    {
        var model = new SmartInputViewModel
        {
            Field = field,
            Value = value,
            PropertyName = propertyName ?? field.Name,
            IsReadOnly = isReadOnly,
            InputType = DetermineInputType(field),
            CssClasses = GetCssClasses(field),
            Attributes = GetHtmlAttributes(field)
        };

        // Load options for select inputs
        if (model.InputType == SmartInputType.Select && field.ForeignKeyReference != null)
        {
            model.SelectOptions = await LoadSelectOptionsAsync(field.ForeignKeyReference);
        }
        else if (model.InputType == SmartInputType.Select)
        {
            model.SelectOptions = GetEnumOptions(field);
        }

        return View(model);
    }

    private SmartInputType DetermineInputType(FieldDefinition field)
    {
        // Foreign key fields become select dropdowns
        if (field.ForeignKeyReference != null)
        {
            return SmartInputType.Select;
        }

        // Determine input type based on field data type and configuration
        return field.DataType switch
        {
            FieldDataType.Boolean => SmartInputType.Checkbox,
            FieldDataType.Date => SmartInputType.Date,
            FieldDataType.DateTime => SmartInputType.DateTime,
            FieldDataType.Time => SmartInputType.Time,
            FieldDataType.Integer or FieldDataType.Long or FieldDataType.Decimal or FieldDataType.Double => SmartInputType.Number,
            FieldDataType.Binary => SmartInputType.File,
            FieldDataType.String when IsEmailField(field) => SmartInputType.Email,
            FieldDataType.String when IsUrlField(field) => SmartInputType.Url,
            FieldDataType.String when IsPhoneField(field) => SmartInputType.Tel,
            FieldDataType.String when IsPasswordField(field) => SmartInputType.Password,
            FieldDataType.String when IsColorField(field) => SmartInputType.Color,
            FieldDataType.String when field.MaxLength > 255 => SmartInputType.Textarea,
            _ => SmartInputType.Text
        };
    }

    private string GetCssClasses(FieldDefinition field)
    {
        var classes = new List<string> { "form-control" };

        // Add custom CSS classes
        if (!string.IsNullOrEmpty(field.UIConfiguration.CssClasses))
        {
            classes.Add(field.UIConfiguration.CssClasses);
        }

        // Add validation classes
        if (!field.IsNullable)
        {
            classes.Add("required");
        }

        // Add data type specific classes
        switch (field.DataType)
        {
            case FieldDataType.Integer:
            case FieldDataType.Long:
            case FieldDataType.Decimal:
            case FieldDataType.Double:
                classes.Add("numeric");
                break;
            case FieldDataType.Date:
            case FieldDataType.DateTime:
                classes.Add("date-input");
                break;
        }

        return string.Join(" ", classes);
    }

    private Dictionary<string, object> GetHtmlAttributes(FieldDefinition field)
    {
        var attributes = new Dictionary<string, object>();

        // Add placeholder
        if (!string.IsNullOrEmpty(field.UIConfiguration.Placeholder))
        {
            attributes["placeholder"] = field.UIConfiguration.Placeholder;
        }

        // Add max length for string fields
        if (field.DataType == FieldDataType.String && field.MaxLength.HasValue)
        {
            attributes["maxlength"] = field.MaxLength.Value;
        }

        // Add step for decimal fields
        if (field.DataType == FieldDataType.Decimal && field.Scale.HasValue)
        {
            var step = 1.0 / Math.Pow(10, field.Scale.Value);
            attributes["step"] = step.ToString("F" + field.Scale.Value);
        }

        // Add min/max for numeric fields
        var validationRule = field.ValidationRules.FirstOrDefault();
        if (validationRule != null)
        {
            if (validationRule.MinValue.HasValue)
            {
                attributes["min"] = validationRule.MinValue.Value;
            }
            if (validationRule.MaxValue.HasValue)
            {
                attributes["max"] = validationRule.MaxValue.Value;
            }
        }

        // Add pattern for regex validation
        if (validationRule?.RegexPattern != null)
        {
            attributes["pattern"] = validationRule.RegexPattern;
            attributes["title"] = validationRule.ValidationMessage ?? "Invalid format";
        }

        // Add required attribute
        if (!field.IsNullable)
        {
            attributes["required"] = "required";
        }

        // Add data attributes for client-side validation
        attributes["data-field-type"] = field.DataType.ToString();
        attributes["data-field-name"] = field.Name;

        return attributes;
    }

    private async Task<List<SelectOption>> LoadSelectOptionsAsync(ForeignKeyReference foreignKey)
    {
        try
        {
            var config = await _configurationService.LoadConfigurationAsync();
            var connectionString = config.Database.ConnectionString;
            
            if (string.IsNullOrEmpty(connectionString))
            {
                return new List<SelectOption>();
            }

            // This would load the actual options from the referenced table
            // For now, return empty list
            return new List<SelectOption>();
        }
        catch (Exception)
        {
            return new List<SelectOption>();
        }
    }

    private List<SelectOption> GetEnumOptions(FieldDefinition field)
    {
        // Handle enum-like fields (e.g., status fields)
        if (field.Name.ToLower().Contains("status"))
        {
            return new List<SelectOption>
            {
                new() { Value = "Active", Text = "Active" },
                new() { Value = "Inactive", Text = "Inactive" },
                new() { Value = "Pending", Text = "Pending" },
                new() { Value = "Completed", Text = "Completed" }
            };
        }

        if (field.Name.ToLower().Contains("type"))
        {
            return new List<SelectOption>
            {
                new() { Value = "Type1", Text = "Type 1" },
                new() { Value = "Type2", Text = "Type 2" },
                new() { Value = "Type3", Text = "Type 3" }
            };
        }

        return new List<SelectOption>();
    }

    private bool IsEmailField(FieldDefinition field)
    {
        return field.Name.ToLower().Contains("email") ||
               field.Name.ToLower().Contains("mail");
    }

    private bool IsUrlField(FieldDefinition field)
    {
        return field.Name.ToLower().Contains("url") ||
               field.Name.ToLower().Contains("website") ||
               field.Name.ToLower().Contains("link");
    }

    private bool IsPhoneField(FieldDefinition field)
    {
        return field.Name.ToLower().Contains("phone") ||
               field.Name.ToLower().Contains("mobile") ||
               field.Name.ToLower().Contains("tel");
    }

    private bool IsPasswordField(FieldDefinition field)
    {
        return field.Name.ToLower().Contains("password") ||
               field.Name.ToLower().Contains("pwd");
    }

    private bool IsColorField(FieldDefinition field)
    {
        return field.Name.ToLower().Contains("color") ||
               field.Name.ToLower().Contains("colour");
    }
}

/// <summary>
/// View model for smart input component
/// </summary>
public class SmartInputViewModel
{
    public FieldDefinition Field { get; set; } = new();
    public object? Value { get; set; }
    public string PropertyName { get; set; } = string.Empty;
    public bool IsReadOnly { get; set; }
    public SmartInputType InputType { get; set; }
    public string CssClasses { get; set; } = string.Empty;
    public Dictionary<string, object> Attributes { get; set; } = new();
    public List<SelectOption> SelectOptions { get; set; } = new();
    public bool HasError { get; set; }
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// Smart input types
/// </summary>
public enum SmartInputType
{
    Text,
    Password,
    Email,
    Number,
    Date,
    DateTime,
    Time,
    Checkbox,
    Radio,
    Select,
    Textarea,
    File,
    Hidden,
    Color,
    Range,
    Url,
    Tel
}

/// <summary>
/// Select option for dropdown lists
/// </summary>
public class SelectOption
{
    public string Value { get; set; } = string.Empty;
    public string Text { get; set; } = string.Empty;
    public bool Selected { get; set; }
    public bool Disabled { get; set; }
    public Dictionary<string, object> Attributes { get; set; } = new();
}
