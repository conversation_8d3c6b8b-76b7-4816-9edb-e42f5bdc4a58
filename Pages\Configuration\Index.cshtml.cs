using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Crudible.Configuration;
using Crudible.Services;
using Crudible.Services.Interfaces;

namespace Crudible.Pages.Configuration;

public class ConfigurationIndexModel : PageModel
{
    private readonly ConfigurationService _configurationService;
    private readonly ISchemaProcessor _schemaProcessor;
    private readonly ILogger<ConfigurationIndexModel> _logger;

    public ConfigurationIndexModel(
        ConfigurationService configurationService,
        ISchemaProcessor schemaProcessor,
        ILogger<ConfigurationIndexModel> logger)
    {
        _configurationService = configurationService;
        _schemaProcessor = schemaProcessor;
        _logger = logger;
    }

    [BindProperty]
    public CrudibleConfiguration Configuration { get; set; } = new();

    public List<string> AvailableTables { get; set; } = new();

    public async Task OnGetAsync()
    {
        try
        {
            Configuration = await _configurationService.LoadConfigurationAsync();
            await LoadAvailableTablesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading configuration");
            TempData["ErrorMessage"] = "Error loading configuration. Using defaults.";
            Configuration = new CrudibleConfiguration();
        }
    }

    public async Task<IActionResult> OnPostAsync()
    {
        try
        {
            // Validate configuration
            var validationResult = _configurationService.ValidateConfiguration(Configuration);
            if (!validationResult.IsValid)
            {
                foreach (var error in validationResult.Errors)
                {
                    ModelState.AddModelError(error.FieldName, error.ErrorMessage);
                }
                
                await LoadAvailableTablesAsync();
                return Page();
            }

            // Save configuration
            var success = await _configurationService.UpdateConfigurationAsync(Configuration);
            if (success)
            {
                TempData["SuccessMessage"] = "Configuration saved successfully.";
                return RedirectToPage();
            }
            else
            {
                TempData["ErrorMessage"] = "Failed to save configuration.";
                await LoadAvailableTablesAsync();
                return Page();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving configuration");
            TempData["ErrorMessage"] = "An error occurred while saving configuration.";
            await LoadAvailableTablesAsync();
            return Page();
        }
    }

    public async Task<IActionResult> OnPostTestConnectionAsync([FromBody] TestConnectionRequest request)
    {
        try
        {
            if (string.IsNullOrEmpty(request.ConnectionString))
            {
                return new JsonResult(new { success = false, message = "Connection string is required." });
            }

            _logger.LogInformation("Testing database connection");

            // Test the actual database connection
            var validationResult = await _schemaProcessor.ValidateSchemaAsync(
                request.ConnectionString,
                Services.Interfaces.SchemaType.ConnectionString);

            if (validationResult.IsValid)
            {
                // Try to get table count to verify connection works
                try
                {
                    var tables = await _schemaProcessor.ProcessConnectionStringAsync(request.ConnectionString);
                    var tableCount = tables.Count;

                    _logger.LogInformation("Database connection successful, found {TableCount} tables", tableCount);

                    return new JsonResult(new
                    {
                        success = true,
                        message = $"Connection successful! Found {tableCount} tables in the database.",
                        tableCount = tableCount
                    });
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Connection succeeded but failed to enumerate tables");
                    return new JsonResult(new
                    {
                        success = true,
                        message = "Connection successful, but unable to enumerate tables. Check permissions.",
                        tableCount = 0
                    });
                }
            }
            else
            {
                var errorMessage = string.Join(", ", validationResult.Errors);
                _logger.LogWarning("Database connection failed: {Errors}", errorMessage);

                return new JsonResult(new
                {
                    success = false,
                    message = $"Connection failed: {errorMessage}"
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error testing database connection");
            return new JsonResult(new {
                success = false,
                message = $"Connection test failed: {ex.Message}"
            });
        }
    }

    public async Task<IActionResult> OnPostUploadSchemaAsync(IFormFile schemaFile)
    {
        try
        {
            if (schemaFile == null || schemaFile.Length == 0)
            {
                TempData["ErrorMessage"] = "Please select a schema file to upload.";
                await LoadAvailableTablesAsync();
                return Page();
            }

            // Validate file type
            var allowedExtensions = new[] { ".xml", ".xsd" };
            var fileExtension = Path.GetExtension(schemaFile.FileName).ToLowerInvariant();
            if (!allowedExtensions.Contains(fileExtension))
            {
                TempData["ErrorMessage"] = "Only XML (.xml) and XSD (.xsd) files are supported.";
                await LoadAvailableTablesAsync();
                return Page();
            }

            // Validate file size (max 10MB)
            if (schemaFile.Length > 10 * 1024 * 1024)
            {
                TempData["ErrorMessage"] = "File size cannot exceed 10MB.";
                await LoadAvailableTablesAsync();
                return Page();
            }

            using var reader = new StreamReader(schemaFile.OpenReadStream());
            var schemaContent = await reader.ReadToEndAsync();

            _logger.LogInformation("Processing uploaded schema file: {FileName}, Size: {Size} bytes",
                schemaFile.FileName, schemaFile.Length);

            // Validate and parse the schema
            var validationResult = await _schemaProcessor.ValidateSchemaAsync(
                schemaContent,
                Services.Interfaces.SchemaType.Xml);

            if (!validationResult.IsValid)
            {
                var errorMessage = $"Invalid schema file: {string.Join(", ", validationResult.Errors)}";
                TempData["ErrorMessage"] = errorMessage;
                _logger.LogWarning("Schema validation failed: {Errors}", errorMessage);
                await LoadAvailableTablesAsync();
                return Page();
            }

            // Parse the schema to get table definitions
            var tables = await _schemaProcessor.ProcessXmlSchemaAsync(schemaContent);

            if (!tables.Any())
            {
                TempData["ErrorMessage"] = "No valid tables found in the schema file.";
                await LoadAvailableTablesAsync();
                return Page();
            }

            // Update configuration with schema
            Configuration.Schema.XmlSchema = schemaContent;
            Configuration.Schema.IncludedTables = tables.Select(t => t.Name).ToList();

            var success = await _configurationService.UpdateSchemaConfigurationAsync(Configuration.Schema);

            if (success)
            {
                // Update available tables list
                AvailableTables = tables.Select(t => t.Name).ToList();

                TempData["SuccessMessage"] = $"Schema uploaded successfully! Found {tables.Count} tables: {string.Join(", ", tables.Take(5).Select(t => t.Name))}{(tables.Count > 5 ? "..." : "")}";
                _logger.LogInformation("Schema uploaded successfully with {TableCount} tables", tables.Count);
            }
            else
            {
                TempData["ErrorMessage"] = "Schema was valid but failed to save configuration.";
            }

            await LoadAvailableTablesAsync();
            return Page();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading schema file: {FileName}", schemaFile?.FileName ?? "unknown");
            TempData["ErrorMessage"] = $"Error processing schema file: {ex.Message}";
            await LoadAvailableTablesAsync();
            return Page();
        }
    }

    public async Task<IActionResult> OnPostExtractSchemaAsync()
    {
        try
        {
            if (string.IsNullOrEmpty(Configuration.Database.ConnectionString))
            {
                TempData["ErrorMessage"] = "Please configure database connection first.";
                await LoadAvailableTablesAsync();
                return Page();
            }

            _logger.LogInformation("Extracting schema from database");

            // First validate the connection
            var validationResult = await _schemaProcessor.ValidateSchemaAsync(
                Configuration.Database.ConnectionString,
                Services.Interfaces.SchemaType.ConnectionString);

            if (!validationResult.IsValid)
            {
                var errorMessage = $"Database connection failed: {string.Join(", ", validationResult.Errors)}";
                TempData["ErrorMessage"] = errorMessage;
                _logger.LogWarning("Schema extraction failed due to connection issues: {Errors}", errorMessage);
                await LoadAvailableTablesAsync();
                return Page();
            }

            // Extract table definitions from the database
            var tables = await _schemaProcessor.ProcessConnectionStringAsync(Configuration.Database.ConnectionString);

            if (tables.Any())
            {
                // Update available tables
                AvailableTables = tables.Select(t => t.Name).ToList();
                Configuration.Schema.IncludedTables = AvailableTables;

                // Clear any existing XML schema since we're using database extraction
                Configuration.Schema.XmlSchema = null;

                var success = await _configurationService.UpdateSchemaConfigurationAsync(Configuration.Schema);

                if (success)
                {
                    var tableDetails = tables.Take(5).Select(t => $"{t.Name} ({t.Fields.Count} fields)");
                    var tableList = string.Join(", ", tableDetails);
                    if (tables.Count > 5)
                        tableList += $" and {tables.Count - 5} more";

                    TempData["SuccessMessage"] = $"Schema extracted successfully! Found {tables.Count} tables: {tableList}";
                    _logger.LogInformation("Schema extraction successful, found {TableCount} tables", tables.Count);
                }
                else
                {
                    TempData["ErrorMessage"] = "Schema extraction succeeded but failed to save configuration.";
                }
            }
            else
            {
                TempData["WarningMessage"] = "No tables found in the database. Check if the database contains tables and you have proper permissions.";
                _logger.LogWarning("No tables found during schema extraction");
            }

            await LoadAvailableTablesAsync();
            return Page();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting schema from database");
            TempData["ErrorMessage"] = $"Error extracting schema from database: {ex.Message}";
            await LoadAvailableTablesAsync();
            return Page();
        }
    }

    public async Task<IActionResult> OnPostResetConfigurationAsync()
    {
        try
        {
            var success = await _configurationService.ResetToDefaultsAsync();
            if (success)
            {
                TempData["SuccessMessage"] = "Configuration reset to defaults.";
            }
            else
            {
                TempData["ErrorMessage"] = "Failed to reset configuration.";
            }

            return RedirectToPage();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resetting configuration");
            TempData["ErrorMessage"] = "Error resetting configuration.";
            return RedirectToPage();
        }
    }

    private async Task LoadAvailableTablesAsync()
    {
        try
        {
            AvailableTables = new List<string>();

            // Try to load from database connection first
            if (!string.IsNullOrEmpty(Configuration.Database.ConnectionString))
            {
                try
                {
                    _logger.LogDebug("Loading tables from database connection");
                    var tables = await _schemaProcessor.ProcessConnectionStringAsync(Configuration.Database.ConnectionString);
                    AvailableTables = tables.Select(t => t.Name).ToList();
                    _logger.LogDebug("Loaded {TableCount} tables from database", AvailableTables.Count);
                    return;
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to load tables from database connection");
                }
            }

            // Try to load from XML schema if database connection failed or not available
            if (!string.IsNullOrEmpty(Configuration.Schema.XmlSchema))
            {
                try
                {
                    _logger.LogDebug("Loading tables from XML schema");
                    var tables = await _schemaProcessor.ProcessXmlSchemaAsync(Configuration.Schema.XmlSchema);
                    AvailableTables = tables.Select(t => t.Name).ToList();
                    _logger.LogDebug("Loaded {TableCount} tables from XML schema", AvailableTables.Count);
                    return;
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to load tables from XML schema");
                }
            }

            // Use configured included tables if available
            if (Configuration.Schema.IncludedTables?.Any() == true)
            {
                AvailableTables = Configuration.Schema.IncludedTables.ToList();
                _logger.LogDebug("Using configured included tables: {TableCount}", AvailableTables.Count);
                return;
            }

            // If nothing else works, show empty list with helpful message
            _logger.LogDebug("No tables available - no valid schema source found");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading available tables");
            AvailableTables = new List<string>();
        }
    }

    public class TestConnectionRequest
    {
        public string ConnectionString { get; set; } = string.Empty;
    }
}
