using Microsoft.AspNetCore.Mvc;
using Crudible.Services.Interfaces;
using Crudible.Models;

namespace Crudible.Controllers;

/// <summary>
/// API controller for data export functionality
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class ExportController : ControllerBase
{
    private readonly IExportService _exportService;
    private readonly ILogger<ExportController> _logger;

    public ExportController(IExportService exportService, ILogger<ExportController> logger)
    {
        _exportService = exportService;
        _logger = logger;
    }

    /// <summary>
    /// Export table data to Excel format
    /// </summary>
    /// <param name="tableName">Table name</param>
    /// <param name="searchTerm">Search term</param>
    /// <param name="sortField">Sort field</param>
    /// <param name="sortDirection">Sort direction</param>
    /// <returns>Excel file</returns>
    [HttpGet("{tableName}/excel")]
    public async Task<IActionResult> ExportToExcel(
        string tableName,
        [FromQuery] string? searchTerm = null,
        [FromQuery] string? sortField = null,
        [FromQuery] SortDirection sortDirection = SortDirection.Ascending)
    {
        try
        {
            var request = new ExportRequest
            {
                TableName = tableName,
                Format = ExportFormat.Excel,
                Filter = new ListFilter
                {
                    SearchTerm = searchTerm,
                    SortField = sortField,
                    SortDirection = sortDirection,
                    PageSize = 10000 // Large page size for export
                }
            };

            var data = await _exportService.ExportToExcelAsync(request);
            var fileName = await _exportService.GetExportFileNameAsync(tableName, ExportFormat.Excel);

            return File(data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to export table {TableName} to Excel", tableName);
            return BadRequest(new { error = "Export failed", message = ex.Message });
        }
    }

    /// <summary>
    /// Export table data to CSV format
    /// </summary>
    /// <param name="tableName">Table name</param>
    /// <param name="searchTerm">Search term</param>
    /// <param name="sortField">Sort field</param>
    /// <param name="sortDirection">Sort direction</param>
    /// <returns>CSV file</returns>
    [HttpGet("{tableName}/csv")]
    public async Task<IActionResult> ExportToCsv(
        string tableName,
        [FromQuery] string? searchTerm = null,
        [FromQuery] string? sortField = null,
        [FromQuery] SortDirection sortDirection = SortDirection.Ascending)
    {
        try
        {
            var request = new ExportRequest
            {
                TableName = tableName,
                Format = ExportFormat.Csv,
                Filter = new ListFilter
                {
                    SearchTerm = searchTerm,
                    SortField = sortField,
                    SortDirection = sortDirection,
                    PageSize = 10000 // Large page size for export
                }
            };

            var data = await _exportService.ExportToCsvAsync(request);
            var fileName = await _exportService.GetExportFileNameAsync(tableName, ExportFormat.Csv);

            return File(data, "text/csv", fileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to export table {TableName} to CSV", tableName);
            return BadRequest(new { error = "Export failed", message = ex.Message });
        }
    }

    /// <summary>
    /// Export table data to JSON format
    /// </summary>
    /// <param name="tableName">Table name</param>
    /// <param name="searchTerm">Search term</param>
    /// <param name="sortField">Sort field</param>
    /// <param name="sortDirection">Sort direction</param>
    /// <returns>JSON file</returns>
    [HttpGet("{tableName}/json")]
    public async Task<IActionResult> ExportToJson(
        string tableName,
        [FromQuery] string? searchTerm = null,
        [FromQuery] string? sortField = null,
        [FromQuery] SortDirection sortDirection = SortDirection.Ascending)
    {
        try
        {
            var request = new ExportRequest
            {
                TableName = tableName,
                Format = ExportFormat.Json,
                Filter = new ListFilter
                {
                    SearchTerm = searchTerm,
                    SortField = sortField,
                    SortDirection = sortDirection,
                    PageSize = 10000 // Large page size for export
                }
            };

            var data = await _exportService.ExportToJsonAsync(request);
            var fileName = await _exportService.GetExportFileNameAsync(tableName, ExportFormat.Json);

            return File(data, "application/json", fileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to export table {TableName} to JSON", tableName);
            return BadRequest(new { error = "Export failed", message = ex.Message });
        }
    }

    /// <summary>
    /// Get export status
    /// </summary>
    /// <param name="exportId">Export ID</param>
    /// <returns>Export status</returns>
    [HttpGet("status/{exportId}")]
    public async Task<IActionResult> GetExportStatus(string exportId)
    {
        // This would be implemented for long-running exports
        // For now, return a simple response
        return Ok(new { status = "completed", downloadUrl = $"/api/export/download/{exportId}" });
    }
}

/// <summary>
/// API controller for database operations
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class DatabaseController : ControllerBase
{
    private readonly ISchemaProcessor _schemaProcessor;
    private readonly ILogger<DatabaseController> _logger;

    public DatabaseController(ISchemaProcessor schemaProcessor, ILogger<DatabaseController> logger)
    {
        _schemaProcessor = schemaProcessor;
        _logger = logger;
    }

    /// <summary>
    /// Test database connection
    /// </summary>
    /// <param name="request">Connection test request</param>
    /// <returns>Connection test result</returns>
    [HttpPost("test-connection")]
    public async Task<IActionResult> TestConnection([FromBody] TestConnectionRequest request)
    {
        try
        {
            var result = await _schemaProcessor.ValidateSchemaAsync(
                request.ConnectionString, 
                Services.Interfaces.SchemaType.ConnectionString);

            return Ok(new 
            { 
                success = result.IsValid,
                message = result.IsValid ? "Connection successful" : string.Join(", ", result.Errors),
                tableCount = result.TableCount
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Database connection test failed");
            return Ok(new { success = false, message = "Connection test failed" });
        }
    }

    /// <summary>
    /// Get table list from database
    /// </summary>
    /// <param name="connectionString">Database connection string</param>
    /// <returns>List of tables</returns>
    [HttpPost("tables")]
    public async Task<IActionResult> GetTables([FromBody] GetTablesRequest request)
    {
        try
        {
            var tables = await _schemaProcessor.ProcessConnectionStringAsync(request.ConnectionString);
            
            return Ok(new 
            { 
                success = true,
                tables = tables.Select(t => new 
                { 
                    name = t.Name,
                    displayName = t.DisplayName,
                    description = t.Description,
                    fieldCount = t.Fields.Count
                })
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get tables from database");
            return BadRequest(new { success = false, message = "Failed to get tables" });
        }
    }

    public class TestConnectionRequest
    {
        public string ConnectionString { get; set; } = string.Empty;
    }

    public class GetTablesRequest
    {
        public string ConnectionString { get; set; } = string.Empty;
    }
}

/// <summary>
/// API controller for audit operations
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class AuditController : ControllerBase
{
    private readonly IAuditService _auditService;
    private readonly ILogger<AuditController> _logger;

    public AuditController(IAuditService auditService, ILogger<AuditController> logger)
    {
        _auditService = auditService;
        _logger = logger;
    }

    /// <summary>
    /// Get audit entries with filtering and pagination
    /// </summary>
    /// <param name="tableName">Table name filter</param>
    /// <param name="recordId">Record ID filter</param>
    /// <param name="userId">User ID filter</param>
    /// <param name="operation">Operation filter</param>
    /// <param name="startDate">Start date filter</param>
    /// <param name="endDate">End date filter</param>
    /// <param name="page">Page number</param>
    /// <param name="pageSize">Page size</param>
    /// <returns>Paged audit entries</returns>
    [HttpGet]
    public async Task<IActionResult> GetAuditEntries(
        [FromQuery] string? tableName = null,
        [FromQuery] string? recordId = null,
        [FromQuery] string? userId = null,
        [FromQuery] AuditOperation? operation = null,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 25)
    {
        try
        {
            var filter = new AuditFilter
            {
                TableName = tableName,
                RecordId = recordId,
                UserId = userId,
                Operation = operation,
                StartDate = startDate,
                EndDate = endDate,
                Page = page,
                PageSize = pageSize,
                SortField = "Timestamp",
                SortDirection = SortDirection.Descending
            };

            var result = await _auditService.GetAuditEntriesAsync(filter);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get audit entries");
            return BadRequest(new { error = "Failed to get audit entries", message = ex.Message });
        }
    }

    /// <summary>
    /// Get audit summary for reporting
    /// </summary>
    /// <param name="startDate">Start date</param>
    /// <param name="endDate">End date</param>
    /// <returns>Audit summary</returns>
    [HttpGet("summary")]
    public async Task<IActionResult> GetAuditSummary(
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var start = startDate ?? DateTime.UtcNow.AddDays(-30);
            var end = endDate ?? DateTime.UtcNow;

            var summary = await _auditService.GetAuditSummaryAsync(start, end);
            return Ok(summary);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get audit summary");
            return BadRequest(new { error = "Failed to get audit summary", message = ex.Message });
        }
    }
}
