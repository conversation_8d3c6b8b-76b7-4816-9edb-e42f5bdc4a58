using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;
using Crudible.Models;
using Crudible.Services.Interfaces;

namespace Crudible.Services;

/// <summary>
/// Service for validating entities based on table definitions and custom rules
/// </summary>
public class ValidationService : IValidationService
{
    private readonly ILogger<ValidationService> _logger;
    private readonly DatabaseService _databaseService;

    public ValidationService(ILogger<ValidationService> logger, DatabaseService databaseService)
    {
        _logger = logger;
        _databaseService = databaseService;
    }

    /// <summary>
    /// Validate entity based on table definition and custom rules
    /// </summary>
    /// <param name="entity">Entity to validate</param>
    /// <param name="tableDefinition">Table definition</param>
    /// <returns>Validation result</returns>
    public async Task<Models.ValidationResult> ValidateEntityAsync(object entity, TableDefinition tableDefinition)
    {
        var result = new Models.ValidationResult();

        try
        {
            _logger.LogDebug("Validating entity for table {TableName}", tableDefinition.Name);

            // Validate each field
            foreach (var field in tableDefinition.Fields)
            {
                var value = GetPropertyValue(entity, field.Name);
                await ValidateFieldAsync(field, value, result, tableDefinition);
            }

            // Validate unique constraints
            var uniqueResult = await ValidateUniqueConstraintsAsync(entity, tableDefinition);
            if (!uniqueResult.IsValid)
            {
                result.Errors.AddRange(uniqueResult.Errors);
                result.IsValid = false;
            }

            // Validate custom business rules
            await ValidateBusinessRulesAsync(entity, tableDefinition, result);

            _logger.LogDebug("Validation completed for table {TableName}. Valid: {IsValid}, Errors: {ErrorCount}",
                tableDefinition.Name, result.IsValid, result.Errors.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during validation for table {TableName}", tableDefinition.Name);
            result.AddError("General", "An error occurred during validation");
        }

        return result;
    }

    /// <summary>
    /// Get validation attributes for a field
    /// </summary>
    /// <param name="fieldDefinition">Field definition</param>
    /// <returns>List of validation attributes</returns>
    public List<ValidationAttribute> GetValidationAttributes(FieldDefinition fieldDefinition)
    {
        var attributes = new List<ValidationAttribute>();

        // Required validation
        if (!fieldDefinition.IsNullable && fieldDefinition.DataType == FieldDataType.String)
        {
            attributes.Add(new RequiredAttribute
            {
                ErrorMessage = $"{fieldDefinition.DisplayName} is required."
            });
        }

        // String length validation
        if (fieldDefinition.DataType == FieldDataType.String && fieldDefinition.MaxLength.HasValue)
        {
            attributes.Add(new StringLengthAttribute(fieldDefinition.MaxLength.Value)
            {
                ErrorMessage = $"{fieldDefinition.DisplayName} cannot exceed {fieldDefinition.MaxLength} characters."
            });
        }

        // Range validation for numeric types
        if (IsNumericType(fieldDefinition.DataType))
        {
            var minValue = GetMinValue(fieldDefinition);
            var maxValue = GetMaxValue(fieldDefinition);
            
            if (minValue.HasValue || maxValue.HasValue)
            {
                attributes.Add(new RangeAttribute(
                    minValue ?? double.MinValue,
                    maxValue ?? double.MaxValue)
                {
                    ErrorMessage = $"{fieldDefinition.DisplayName} must be between {minValue ?? double.MinValue} and {maxValue ?? double.MaxValue}."
                });
            }
        }

        // Email validation
        if (IsEmailField(fieldDefinition))
        {
            attributes.Add(new EmailAddressAttribute
            {
                ErrorMessage = $"{fieldDefinition.DisplayName} must be a valid email address."
            });
        }

        // URL validation
        if (IsUrlField(fieldDefinition))
        {
            attributes.Add(new UrlAttribute
            {
                ErrorMessage = $"{fieldDefinition.DisplayName} must be a valid URL."
            });
        }

        // Phone validation
        if (IsPhoneField(fieldDefinition))
        {
            attributes.Add(new PhoneAttribute
            {
                ErrorMessage = $"{fieldDefinition.DisplayName} must be a valid phone number."
            });
        }

        // Custom regex validation
        foreach (var rule in fieldDefinition.ValidationRules)
        {
            if (!string.IsNullOrEmpty(rule.RegexPattern))
            {
                attributes.Add(new RegularExpressionAttribute(rule.RegexPattern)
                {
                    ErrorMessage = rule.ValidationMessage ?? $"{fieldDefinition.DisplayName} format is invalid."
                });
            }
        }

        return attributes;
    }

    /// <summary>
    /// Validate unique constraints
    /// </summary>
    /// <param name="entity">Entity to validate</param>
    /// <param name="tableDefinition">Table definition</param>
    /// <returns>Validation result</returns>
    public async Task<Models.ValidationResult> ValidateUniqueConstraintsAsync(object entity, TableDefinition tableDefinition)
    {
        var result = new Models.ValidationResult();

        try
        {
            // Check unique fields
            var uniqueFields = tableDefinition.Fields.Where(f => f.IsUnique).ToList();
            
            foreach (var field in uniqueFields)
            {
                var value = GetPropertyValue(entity, field.Name);
                if (value != null)
                {
                    var isDuplicate = await CheckForDuplicateValueAsync(tableDefinition.Name, field.Name, value, entity);
                    if (isDuplicate)
                    {
                        result.AddError(field.Name, $"{field.DisplayName} '{value}' already exists.");
                    }
                }
            }

            // Check unique indexes
            foreach (var index in tableDefinition.Indexes.Where(i => i.IsUnique))
            {
                var values = new Dictionary<string, object?>();
                foreach (var fieldName in index.Fields)
                {
                    values[fieldName] = GetPropertyValue(entity, fieldName);
                }

                var isDuplicate = await CheckForDuplicateIndexAsync(tableDefinition.Name, index.Fields, values, entity);
                if (isDuplicate)
                {
                    var fieldNames = string.Join(", ", index.Fields);
                    result.AddError("Index", $"The combination of {fieldNames} already exists.");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating unique constraints for table {TableName}", tableDefinition.Name);
            result.AddError("Unique", "Error validating unique constraints");
        }

        return result;
    }

    private async Task ValidateFieldAsync(FieldDefinition field, object? value, Models.ValidationResult result, TableDefinition table)
    {
        // Required field validation
        if (!field.IsNullable && (value == null || (value is string str && string.IsNullOrWhiteSpace(str))))
        {
            result.AddError(field.Name, $"{field.DisplayName} is required.");
            return;
        }

        if (value == null)
            return;

        // Data type specific validation
        switch (field.DataType)
        {
            case FieldDataType.String:
                ValidateStringField(field, value.ToString()!, result);
                break;
            case FieldDataType.Integer:
                ValidateIntegerField(field, value, result);
                break;
            case FieldDataType.Decimal:
                ValidateDecimalField(field, value, result);
                break;
            case FieldDataType.DateTime:
            case FieldDataType.Date:
                ValidateDateTimeField(field, value, result);
                break;
            case FieldDataType.Boolean:
                ValidateBooleanField(field, value, result);
                break;
        }

        // Custom validation rules
        foreach (var rule in field.ValidationRules)
        {
            ValidateCustomRule(field, value, rule, result);
        }
    }

    private void ValidateStringField(FieldDefinition field, string value, Models.ValidationResult result)
    {
        // Length validation
        if (field.MaxLength.HasValue && value.Length > field.MaxLength.Value)
        {
            result.AddError(field.Name, $"{field.DisplayName} cannot exceed {field.MaxLength} characters.");
        }

        // Email validation
        if (IsEmailField(field) && !IsValidEmail(value))
        {
            result.AddError(field.Name, $"{field.DisplayName} must be a valid email address.");
        }

        // URL validation
        if (IsUrlField(field) && !IsValidUrl(value))
        {
            result.AddError(field.Name, $"{field.DisplayName} must be a valid URL.");
        }

        // Phone validation
        if (IsPhoneField(field) && !IsValidPhone(value))
        {
            result.AddError(field.Name, $"{field.DisplayName} must be a valid phone number.");
        }
    }

    private void ValidateIntegerField(FieldDefinition field, object value, Models.ValidationResult result)
    {
        if (!int.TryParse(value.ToString(), out var intValue))
        {
            result.AddError(field.Name, $"{field.DisplayName} must be a valid integer.");
            return;
        }

        var minValue = GetMinValue(field);
        var maxValue = GetMaxValue(field);

        if (minValue.HasValue && intValue < minValue.Value)
        {
            result.AddError(field.Name, $"{field.DisplayName} must be at least {minValue}.");
        }

        if (maxValue.HasValue && intValue > maxValue.Value)
        {
            result.AddError(field.Name, $"{field.DisplayName} cannot exceed {maxValue}.");
        }
    }

    private void ValidateDecimalField(FieldDefinition field, object value, Models.ValidationResult result)
    {
        if (!decimal.TryParse(value.ToString(), out var decimalValue))
        {
            result.AddError(field.Name, $"{field.DisplayName} must be a valid decimal number.");
            return;
        }

        var minValue = GetMinValue(field);
        var maxValue = GetMaxValue(field);

        if (minValue.HasValue && (double)decimalValue < minValue.Value)
        {
            result.AddError(field.Name, $"{field.DisplayName} must be at least {minValue}.");
        }

        if (maxValue.HasValue && (double)decimalValue > maxValue.Value)
        {
            result.AddError(field.Name, $"{field.DisplayName} cannot exceed {maxValue}.");
        }
    }

    private void ValidateDateTimeField(FieldDefinition field, object value, Models.ValidationResult result)
    {
        if (!DateTime.TryParse(value.ToString(), out var dateValue))
        {
            result.AddError(field.Name, $"{field.DisplayName} must be a valid date.");
            return;
        }

        // Add date range validation if needed
        if (field.Name.ToLower().Contains("birth") && dateValue > DateTime.Now.AddYears(-18))
        {
            result.AddError(field.Name, "Must be at least 18 years old.");
        }
    }

    private void ValidateBooleanField(FieldDefinition field, object value, Models.ValidationResult result)
    {
        if (!bool.TryParse(value.ToString(), out _))
        {
            result.AddError(field.Name, $"{field.DisplayName} must be true or false.");
        }
    }

    private void ValidateCustomRule(FieldDefinition field, object value, FieldValidationRule rule, Models.ValidationResult result)
    {
        var stringValue = value.ToString() ?? "";

        // Regex validation
        if (!string.IsNullOrEmpty(rule.RegexPattern))
        {
            if (!Regex.IsMatch(stringValue, rule.RegexPattern))
            {
                result.AddError(field.Name, rule.ValidationMessage ?? $"{field.DisplayName} format is invalid.");
            }
        }

        // Length validation
        if (rule.MinLength.HasValue && stringValue.Length < rule.MinLength.Value)
        {
            result.AddError(field.Name, $"{field.DisplayName} must be at least {rule.MinLength} characters.");
        }

        if (rule.MaxLength.HasValue && stringValue.Length > rule.MaxLength.Value)
        {
            result.AddError(field.Name, $"{field.DisplayName} cannot exceed {rule.MaxLength} characters.");
        }

        // Value range validation
        if (IsNumericType(field.DataType))
        {
            if (decimal.TryParse(stringValue, out var numericValue))
            {
                if (rule.MinValue.HasValue && numericValue < rule.MinValue.Value)
                {
                    result.AddError(field.Name, $"{field.DisplayName} must be at least {rule.MinValue}.");
                }

                if (rule.MaxValue.HasValue && numericValue > rule.MaxValue.Value)
                {
                    result.AddError(field.Name, $"{field.DisplayName} cannot exceed {rule.MaxValue}.");
                }
            }
        }
    }

    private async Task ValidateBusinessRulesAsync(object entity, TableDefinition table, Models.ValidationResult result)
    {
        // Implement custom business rules validation
        // This could be extended to support custom validation logic
        await Task.CompletedTask;
    }

    private async Task<bool> CheckForDuplicateValueAsync(string tableName, string fieldName, object value, object currentEntity)
    {
        // This would check the database for duplicate values
        // For now, return false (no duplicates found)
        await Task.CompletedTask;
        return false;
    }

    private async Task<bool> CheckForDuplicateIndexAsync(string tableName, List<string> fieldNames, Dictionary<string, object?> values, object currentEntity)
    {
        // This would check the database for duplicate index values
        // For now, return false (no duplicates found)
        await Task.CompletedTask;
        return false;
    }

    private object? GetPropertyValue(object obj, string propertyName)
    {
        return obj?.GetType()?.GetProperty(propertyName)?.GetValue(obj);
    }

    private bool IsNumericType(FieldDataType dataType)
    {
        return dataType == FieldDataType.Integer ||
               dataType == FieldDataType.Long ||
               dataType == FieldDataType.Decimal ||
               dataType == FieldDataType.Double;
    }

    private bool IsEmailField(FieldDefinition field)
    {
        return field.Name.ToLower().Contains("email") ||
               field.Name.ToLower().Contains("mail");
    }

    private bool IsUrlField(FieldDefinition field)
    {
        return field.Name.ToLower().Contains("url") ||
               field.Name.ToLower().Contains("website") ||
               field.Name.ToLower().Contains("link");
    }

    private bool IsPhoneField(FieldDefinition field)
    {
        return field.Name.ToLower().Contains("phone") ||
               field.Name.ToLower().Contains("mobile") ||
               field.Name.ToLower().Contains("tel");
    }

    private bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }

    private bool IsValidUrl(string url)
    {
        return Uri.TryCreate(url, UriKind.Absolute, out var result) &&
               (result.Scheme == Uri.UriSchemeHttp || result.Scheme == Uri.UriSchemeHttps);
    }

    private bool IsValidPhone(string phone)
    {
        // Basic phone validation - can be enhanced
        var phoneRegex = @"^[\+]?[1-9][\d]{0,15}$";
        return Regex.IsMatch(phone.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", ""), phoneRegex);
    }

    private double? GetMinValue(FieldDefinition field)
    {
        return field.ValidationRules.FirstOrDefault()?.MinValue != null
            ? (double)field.ValidationRules.First().MinValue!
            : null;
    }

    private double? GetMaxValue(FieldDefinition field)
    {
        return field.ValidationRules.FirstOrDefault()?.MaxValue != null
            ? (double)field.ValidationRules.First().MaxValue!
            : null;
    }
}
