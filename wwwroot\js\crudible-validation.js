/**
 * Crudible Client-Side Validation
 * Provides enhanced validation for dynamically generated forms
 */

class CrudibleValidator {
    constructor() {
        this.rules = new Map();
        this.messages = new Map();
        this.init();
    }

    init() {
        // Initialize validation on page load
        document.addEventListener('DOMContentLoaded', () => {
            this.setupFormValidation();
            this.setupRealTimeValidation();
            this.setupSmartInputs();
        });
    }

    setupFormValidation() {
        const forms = document.querySelectorAll('form[data-validate="true"], form.needs-validation');
        
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                if (!this.validateForm(form)) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                form.classList.add('was-validated');
            });
        });
    }

    setupRealTimeValidation() {
        const inputs = document.querySelectorAll('input, select, textarea');
        
        inputs.forEach(input => {
            // Validate on blur
            input.addEventListener('blur', () => {
                this.validateField(input);
            });

            // Clear validation on input (for better UX)
            input.addEventListener('input', () => {
                if (input.classList.contains('is-invalid')) {
                    this.clearFieldValidation(input);
                }
            });
        });
    }

    setupSmartInputs() {
        // Setup date inputs
        this.setupDateInputs();
        
        // Setup numeric inputs
        this.setupNumericInputs();
        
        // Setup foreign key selects
        this.setupForeignKeySelects();
        
        // Setup file inputs
        this.setupFileInputs();
    }

    setupDateInputs() {
        const dateInputs = document.querySelectorAll('input[type="date"], input[type="datetime-local"]');
        
        dateInputs.forEach(input => {
            // Add date validation
            input.addEventListener('change', () => {
                this.validateDateField(input);
            });
        });
    }

    setupNumericInputs() {
        const numericInputs = document.querySelectorAll('input[type="number"], input.numeric');
        
        numericInputs.forEach(input => {
            // Prevent non-numeric input
            input.addEventListener('keypress', (e) => {
                const char = String.fromCharCode(e.which);
                const isDecimal = input.step && input.step !== '1';
                const allowedChars = isDecimal ? /[0-9.]/ : /[0-9]/;
                
                if (!allowedChars.test(char) && !this.isControlKey(e)) {
                    e.preventDefault();
                }
            });

            // Validate range
            input.addEventListener('blur', () => {
                this.validateNumericField(input);
            });
        });
    }

    setupForeignKeySelects() {
        const fkSelects = document.querySelectorAll('select[data-foreign-key]');
        
        fkSelects.forEach(select => {
            select.addEventListener('change', () => {
                this.loadDependentOptions(select);
            });
        });
    }

    setupFileInputs() {
        const fileInputs = document.querySelectorAll('input[type="file"]');
        
        fileInputs.forEach(input => {
            input.addEventListener('change', () => {
                this.validateFileField(input);
                this.showFilePreview(input);
            });
        });
    }

    validateForm(form) {
        let isValid = true;
        const inputs = form.querySelectorAll('input, select, textarea');
        
        inputs.forEach(input => {
            if (!this.validateField(input)) {
                isValid = false;
            }
        });

        return isValid;
    }

    validateField(input) {
        const fieldType = input.dataset.fieldType;
        const fieldName = input.dataset.fieldName || input.name;
        let isValid = true;

        // Clear previous validation
        this.clearFieldValidation(input);

        // Required validation
        if (input.hasAttribute('required') && !input.value.trim()) {
            this.setFieldError(input, `${this.getFieldDisplayName(input)} is required.`);
            return false;
        }

        // Skip further validation if field is empty and not required
        if (!input.value.trim()) {
            return true;
        }

        // Type-specific validation
        switch (fieldType) {
            case 'String':
                isValid = this.validateStringField(input);
                break;
            case 'Integer':
            case 'Long':
            case 'Decimal':
            case 'Double':
                isValid = this.validateNumericField(input);
                break;
            case 'DateTime':
            case 'Date':
                isValid = this.validateDateField(input);
                break;
            case 'Boolean':
                isValid = this.validateBooleanField(input);
                break;
        }

        // Pattern validation
        if (isValid && input.pattern) {
            const regex = new RegExp(input.pattern);
            if (!regex.test(input.value)) {
                this.setFieldError(input, input.title || `${this.getFieldDisplayName(input)} format is invalid.`);
                isValid = false;
            }
        }

        // Custom validation rules
        if (isValid) {
            isValid = this.validateCustomRules(input);
        }

        return isValid;
    }

    validateStringField(input) {
        const value = input.value;
        const maxLength = input.maxLength;

        // Length validation
        if (maxLength && value.length > maxLength) {
            this.setFieldError(input, `${this.getFieldDisplayName(input)} cannot exceed ${maxLength} characters.`);
            return false;
        }

        // Email validation
        if (input.type === 'email' && !this.isValidEmail(value)) {
            this.setFieldError(input, `${this.getFieldDisplayName(input)} must be a valid email address.`);
            return false;
        }

        // URL validation
        if (input.type === 'url' && !this.isValidUrl(value)) {
            this.setFieldError(input, `${this.getFieldDisplayName(input)} must be a valid URL.`);
            return false;
        }

        // Phone validation
        if (input.type === 'tel' && !this.isValidPhone(value)) {
            this.setFieldError(input, `${this.getFieldDisplayName(input)} must be a valid phone number.`);
            return false;
        }

        return true;
    }

    validateNumericField(input) {
        const value = parseFloat(input.value);
        const min = input.min ? parseFloat(input.min) : null;
        const max = input.max ? parseFloat(input.max) : null;

        if (isNaN(value)) {
            this.setFieldError(input, `${this.getFieldDisplayName(input)} must be a valid number.`);
            return false;
        }

        if (min !== null && value < min) {
            this.setFieldError(input, `${this.getFieldDisplayName(input)} must be at least ${min}.`);
            return false;
        }

        if (max !== null && value > max) {
            this.setFieldError(input, `${this.getFieldDisplayName(input)} cannot exceed ${max}.`);
            return false;
        }

        return true;
    }

    validateDateField(input) {
        const value = new Date(input.value);
        
        if (isNaN(value.getTime())) {
            this.setFieldError(input, `${this.getFieldDisplayName(input)} must be a valid date.`);
            return false;
        }

        // Age validation for birth date fields
        if (input.name.toLowerCase().includes('birth')) {
            const age = this.calculateAge(value);
            if (age < 0) {
                this.setFieldError(input, 'Birth date cannot be in the future.');
                return false;
            }
            if (age > 150) {
                this.setFieldError(input, 'Please enter a valid birth date.');
                return false;
            }
        }

        return true;
    }

    validateBooleanField(input) {
        // Boolean fields are generally always valid
        return true;
    }

    validateFileField(input) {
        const files = input.files;
        
        if (!files || files.length === 0) {
            return true;
        }

        const file = files[0];
        const maxSize = input.dataset.maxSize ? parseInt(input.dataset.maxSize) : 10 * 1024 * 1024; // 10MB default
        const allowedTypes = input.dataset.allowedTypes ? input.dataset.allowedTypes.split(',') : [];

        // Size validation
        if (file.size > maxSize) {
            this.setFieldError(input, `File size cannot exceed ${this.formatFileSize(maxSize)}.`);
            return false;
        }

        // Type validation
        if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
            this.setFieldError(input, `File type not allowed. Allowed types: ${allowedTypes.join(', ')}`);
            return false;
        }

        return true;
    }

    validateCustomRules(input) {
        const customRules = input.dataset.customRules;
        
        if (!customRules) {
            return true;
        }

        try {
            const rules = JSON.parse(customRules);
            
            for (const rule of rules) {
                if (!this.validateCustomRule(input, rule)) {
                    return false;
                }
            }
        } catch (e) {
            console.warn('Invalid custom rules format:', customRules);
        }

        return true;
    }

    validateCustomRule(input, rule) {
        const value = input.value;

        switch (rule.type) {
            case 'minLength':
                if (value.length < rule.value) {
                    this.setFieldError(input, rule.message || `Minimum length is ${rule.value} characters.`);
                    return false;
                }
                break;
            case 'maxLength':
                if (value.length > rule.value) {
                    this.setFieldError(input, rule.message || `Maximum length is ${rule.value} characters.`);
                    return false;
                }
                break;
            case 'regex':
                const regex = new RegExp(rule.pattern);
                if (!regex.test(value)) {
                    this.setFieldError(input, rule.message || 'Invalid format.');
                    return false;
                }
                break;
        }

        return true;
    }

    setFieldError(input, message) {
        input.classList.add('is-invalid');
        input.classList.remove('is-valid');

        const feedback = input.parentElement.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.textContent = message;
        }
    }

    clearFieldValidation(input) {
        input.classList.remove('is-invalid', 'is-valid');
        
        const feedback = input.parentElement.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.textContent = '';
        }
    }

    setFieldValid(input) {
        input.classList.add('is-valid');
        input.classList.remove('is-invalid');
    }

    getFieldDisplayName(input) {
        const label = input.parentElement.querySelector('label');
        return label ? label.textContent.replace('*', '').trim() : input.name;
    }

    // Utility methods
    isValidEmail(email) {
        const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return regex.test(email);
    }

    isValidUrl(url) {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    }

    isValidPhone(phone) {
        const regex = /^[\+]?[1-9][\d]{0,15}$/;
        return regex.test(phone.replace(/[\s\-\(\)]/g, ''));
    }

    isControlKey(e) {
        return e.ctrlKey || e.altKey || e.metaKey || 
               [8, 9, 13, 27, 35, 36, 37, 38, 39, 40, 46].includes(e.keyCode);
    }

    calculateAge(birthDate) {
        const today = new Date();
        const birth = new Date(birthDate);
        let age = today.getFullYear() - birth.getFullYear();
        const monthDiff = today.getMonth() - birth.getMonth();
        
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
            age--;
        }
        
        return age;
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    showFilePreview(input) {
        const files = input.files;
        if (!files || files.length === 0) return;

        const file = files[0];
        const preview = input.parentElement.querySelector('.file-preview');
        
        if (preview) {
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    preview.innerHTML = `<img src="${e.target.result}" class="img-thumbnail" style="max-width: 200px; max-height: 200px;">`;
                };
                reader.readAsDataURL(file);
            } else {
                preview.innerHTML = `<div class="alert alert-info">File selected: ${file.name}</div>`;
            }
        }
    }

    async loadDependentOptions(select) {
        const dependentSelect = document.querySelector(`[data-depends-on="${select.name}"]`);
        
        if (!dependentSelect) return;

        const selectedValue = select.value;
        if (!selectedValue) {
            dependentSelect.innerHTML = '<option value="">-- Select --</option>';
            dependentSelect.disabled = true;
            return;
        }

        try {
            dependentSelect.disabled = true;
            dependentSelect.innerHTML = '<option value="">Loading...</option>';

            const response = await fetch(`/api/options/${dependentSelect.dataset.table}?filter=${selectedValue}`);
            const options = await response.json();

            dependentSelect.innerHTML = '<option value="">-- Select --</option>';
            options.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option.value;
                optionElement.textContent = option.text;
                dependentSelect.appendChild(optionElement);
            });

            dependentSelect.disabled = false;
        } catch (error) {
            console.error('Error loading dependent options:', error);
            dependentSelect.innerHTML = '<option value="">Error loading options</option>';
        }
    }
}

// Initialize validator
const crudibleValidator = new CrudibleValidator();
