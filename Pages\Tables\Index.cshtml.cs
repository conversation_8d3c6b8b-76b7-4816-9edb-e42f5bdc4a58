using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Crudible.Services;
using Crudible.Models;
using Crudible.Services.Interfaces;

namespace Crudible.Pages.Tables;

public class TablesIndexModel : PageModel
{
    private readonly ConfigurationService _configurationService;
    private readonly ISchemaProcessor _schemaProcessor;
    private readonly ICrudPageGenerator _crudPageGenerator;
    private readonly TableDataService _tableDataService;
    private readonly ILogger<TablesIndexModel> _logger;

    public TablesIndexModel(
        ConfigurationService configurationService,
        ISchemaProcessor schemaProcessor,
        ICrudPageGenerator crudPageGenerator,
        TableDataService tableDataService,
        ILogger<TablesIndexModel> logger)
    {
        _configurationService = configurationService;
        _schemaProcessor = schemaProcessor;
        _crudPageGenerator = crudPageGenerator;
        _tableDataService = tableDataService;
        _logger = logger;
    }

    public List<TableInfo> AvailableTables { get; set; } = new();

    public async Task OnGetAsync()
    {
        try
        {
            await LoadAvailableTablesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading tables");
            TempData["ErrorMessage"] = "Error loading table information.";
        }
    }

    public async Task<IActionResult> OnPostGenerateAsync([FromBody] GenerateTableRequest request)
    {
        try
        {
            if (string.IsNullOrEmpty(request.TableName))
            {
                return new JsonResult(new { success = false, message = "Table name is required." });
            }

            _logger.LogInformation("Generating CRUD pages for table: {TableName}", request.TableName);

            // Get table definition
            var tableDefinition = await GetTableDefinitionAsync(request.TableName);
            if (tableDefinition == null)
            {
                return new JsonResult(new { success = false, message = "Table definition not found." });
            }

            // Generate CRUD pages
            var pageSet = await _crudPageGenerator.GenerateCrudPagesAsync(tableDefinition);
            
            if (pageSet != null)
            {
                _logger.LogInformation("Successfully generated CRUD pages for table: {TableName}", request.TableName);
                return new JsonResult(new { 
                    success = true, 
                    message = $"CRUD pages generated successfully for {request.TableName}!" 
                });
            }
            else
            {
                return new JsonResult(new { success = false, message = "Failed to generate CRUD pages." });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating CRUD pages for table: {TableName}", request.TableName);
            return new JsonResult(new { 
                success = false, 
                message = $"Error generating CRUD pages: {ex.Message}" 
            });
        }
    }

    public async Task<IActionResult> OnPostGenerateAllAsync()
    {
        try
        {
            _logger.LogInformation("Generating CRUD pages for all tables");

            var successCount = 0;
            var errorCount = 0;
            var errors = new List<string>();

            foreach (var tableInfo in AvailableTables)
            {
                try
                {
                    var tableDefinition = await GetTableDefinitionAsync(tableInfo.Name);
                    if (tableDefinition != null)
                    {
                        var pageSet = await _crudPageGenerator.GenerateCrudPagesAsync(tableDefinition);
                        if (pageSet != null)
                        {
                            successCount++;
                        }
                        else
                        {
                            errorCount++;
                            errors.Add($"Failed to generate pages for {tableInfo.Name}");
                        }
                    }
                    else
                    {
                        errorCount++;
                        errors.Add($"Table definition not found for {tableInfo.Name}");
                    }
                }
                catch (Exception ex)
                {
                    errorCount++;
                    errors.Add($"Error generating {tableInfo.Name}: {ex.Message}");
                    _logger.LogError(ex, "Error generating CRUD pages for table: {TableName}", tableInfo.Name);
                }
            }

            var message = $"Generated CRUD pages for {successCount} tables.";
            if (errorCount > 0)
            {
                message += $" {errorCount} errors occurred.";
            }

            _logger.LogInformation("Bulk CRUD generation completed: {SuccessCount} success, {ErrorCount} errors", 
                successCount, errorCount);

            return new JsonResult(new { 
                success = errorCount == 0, 
                message = message,
                details = errors
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during bulk CRUD generation");
            return new JsonResult(new { 
                success = false, 
                message = $"Error generating CRUD pages: {ex.Message}" 
            });
        }
    }

    public async Task<IActionResult> OnGetSchemaAsync(string tableName)
    {
        try
        {
            if (string.IsNullOrEmpty(tableName))
            {
                return new JsonResult(new { success = false, message = "Table name is required." });
            }

            var columns = await _tableDataService.GetTableSchemaAsync(tableName);
            
            return new JsonResult(new { 
                success = true, 
                tableName = tableName,
                columns = columns.Select(c => new {
                    name = c.Name,
                    dataType = c.DataType,
                    isNullable = c.IsNullable,
                    maxLength = c.MaxLength,
                    defaultValue = c.DefaultValue
                })
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting schema for table: {TableName}", tableName);
            return new JsonResult(new { 
                success = false, 
                message = $"Error getting schema: {ex.Message}" 
            });
        }
    }

    private async Task LoadAvailableTablesAsync()
    {
        try
        {
            var config = await _configurationService.LoadConfigurationAsync();
            var tables = new List<TableDefinition>();

            // Try to load from database connection first
            if (!string.IsNullOrEmpty(config.Database.ConnectionString))
            {
                try
                {
                    tables = await _schemaProcessor.ProcessConnectionStringAsync(config.Database.ConnectionString);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to load tables from database connection");
                }
            }

            // Try to load from XML schema if database connection failed or not available
            if (!tables.Any() && !string.IsNullOrEmpty(config.Schema.XmlSchema))
            {
                try
                {
                    tables = await _schemaProcessor.ProcessXmlSchemaAsync(config.Schema.XmlSchema);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to load tables from XML schema");
                }
            }

            // Convert to TableInfo objects and check for existing CRUD pages
            AvailableTables = tables.Select(t => new TableInfo
            {
                Name = t.Name,
                DisplayName = t.DisplayName ?? t.Name,
                Description = t.Description,
                FieldCount = t.Fields.Count,
                HasCrudPages = CheckIfCrudPagesExist(t.Name)
            }).ToList();

            _logger.LogInformation("Loaded {TableCount} tables", AvailableTables.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading available tables");
            AvailableTables = new List<TableInfo>();
        }
    }

    private bool CheckIfCrudPagesExist(string tableName)
    {
        try
        {
            var pagesPath = Path.Combine(Directory.GetCurrentDirectory(), "Pages", "Tables", tableName);
            return Directory.Exists(pagesPath) &&
                   System.IO.File.Exists(Path.Combine(pagesPath, "Index.cshtml"));
        }
        catch
        {
            return false;
        }
    }

    private async Task<TableDefinition?> GetTableDefinitionAsync(string tableName)
    {
        try
        {
            var config = await _configurationService.LoadConfigurationAsync();
            var tables = new List<TableDefinition>();

            // Try database first
            if (!string.IsNullOrEmpty(config.Database.ConnectionString))
            {
                try
                {
                    tables = await _schemaProcessor.ProcessConnectionStringAsync(config.Database.ConnectionString);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to load table definition from database");
                }
            }

            // Try XML schema
            if (!tables.Any() && !string.IsNullOrEmpty(config.Schema.XmlSchema))
            {
                try
                {
                    tables = await _schemaProcessor.ProcessXmlSchemaAsync(config.Schema.XmlSchema);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to load table definition from XML schema");
                }
            }

            return tables.FirstOrDefault(t => t.Name.Equals(tableName, StringComparison.OrdinalIgnoreCase));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting table definition for {TableName}", tableName);
            return null;
        }
    }
}

public class TableInfo
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int FieldCount { get; set; }
    public bool HasCrudPages { get; set; }
}

public class GenerateTableRequest
{
    public string TableName { get; set; } = string.Empty;
}
