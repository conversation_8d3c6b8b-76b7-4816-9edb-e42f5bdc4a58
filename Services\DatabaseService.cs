using Microsoft.EntityFrameworkCore;
using Crudible.Models;
using Crudible.Data;
using System.Reflection;

namespace Crudible.Services;

/// <summary>
/// Service for managing database operations with dynamic schemas
/// </summary>
public class DatabaseService
{
    private readonly DynamicDbContextService _dynamicDbContextService;
    private readonly CrudibleDbContext _crudibleContext;
    private readonly ILogger<DatabaseService> _logger;
    private readonly Dictionary<string, DbContext> _contextCache = new();

    public DatabaseService(
        DynamicDbContextService dynamicDbContextService,
        CrudibleDbContext crudibleContext,
        ILogger<DatabaseService> logger)
    {
        _dynamicDbContextService = dynamicDbContextService;
        _crudibleContext = crudibleContext;
        _logger = logger;
    }

    /// <summary>
    /// Get or create a dynamic DbContext for the given tables
    /// </summary>
    /// <param name="tables">Table definitions</param>
    /// <param name="connectionString">Database connection string</param>
    /// <returns>DbContext instance</returns>
    public async Task<DbContext> GetDbContextAsync(List<TableDefinition> tables, string connectionString)
    {
        var contextKey = GenerateContextKey(tables, connectionString);
        
        if (!_contextCache.ContainsKey(contextKey))
        {
            var context = await _dynamicDbContextService.CreateDbContextAsync(tables, connectionString);
            _contextCache[contextKey] = context;
        }

        return _contextCache[contextKey];
    }

    /// <summary>
    /// Get all records from a table
    /// </summary>
    /// <param name="tableName">Table name</param>
    /// <param name="tables">Table definitions</param>
    /// <param name="connectionString">Database connection string</param>
    /// <param name="filter">Optional filter criteria</param>
    /// <returns>Paged result of records</returns>
    public async Task<PagedResult<object>> GetTableDataAsync(
        string tableName, 
        List<TableDefinition> tables, 
        string connectionString,
        ListFilter? filter = null)
    {
        try
        {
            var context = await GetDbContextAsync(tables, connectionString);
            var tableDefinition = tables.FirstOrDefault(t => t.Name == tableName);
            
            if (tableDefinition == null)
            {
                throw new ArgumentException($"Table '{tableName}' not found in schema");
            }

            var dbSetProperty = _dynamicDbContextService.GetDbSetProperty(context, tableName);
            if (dbSetProperty == null)
            {
                throw new InvalidOperationException($"DbSet for table '{tableName}' not found");
            }

            var dbSet = dbSetProperty.GetValue(context);
            if (dbSet == null)
            {
                return new PagedResult<object>();
            }

            // Apply filtering and pagination
            var queryable = (IQueryable<object>)dbSet;
            
            // Apply search filter if provided
            if (filter != null && !string.IsNullOrEmpty(filter.SearchTerm))
            {
                queryable = ApplySearchFilter(queryable, tableDefinition, filter.SearchTerm);
            }

            // Apply field filters
            if (filter?.FieldFilters?.Any() == true)
            {
                queryable = ApplyFieldFilters(queryable, tableDefinition, filter.FieldFilters);
            }

            // Get total count
            var totalCount = await CountAsync(queryable);

            // Apply sorting
            if (filter != null && !string.IsNullOrEmpty(filter.SortField))
            {
                queryable = ApplySorting(queryable, filter.SortField, filter.SortDirection);
            }
            else if (!string.IsNullOrEmpty(tableDefinition.UIConfiguration.DefaultSortField))
            {
                queryable = ApplySorting(queryable, tableDefinition.UIConfiguration.DefaultSortField, 
                    tableDefinition.UIConfiguration.DefaultSortDirection);
            }

            // Apply pagination
            var pageSize = filter?.PageSize ?? tableDefinition.UIConfiguration.ItemsPerPage;
            var page = filter?.Page ?? 1;
            var skip = (page - 1) * pageSize;

            var items = await TakeAsync(SkipAsync(queryable, skip), pageSize);

            return new PagedResult<object>
            {
                Items = items,
                TotalItems = totalCount,
                CurrentPage = page,
                PageSize = pageSize
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get table data for {TableName}", tableName);
            throw;
        }
    }

    /// <summary>
    /// Get a single record by ID
    /// </summary>
    /// <param name="tableName">Table name</param>
    /// <param name="id">Record ID</param>
    /// <param name="tables">Table definitions</param>
    /// <param name="connectionString">Database connection string</param>
    /// <returns>Record or null if not found</returns>
    public async Task<object?> GetRecordByIdAsync(
        string tableName, 
        object id, 
        List<TableDefinition> tables, 
        string connectionString)
    {
        try
        {
            var context = await GetDbContextAsync(tables, connectionString);
            var tableDefinition = tables.FirstOrDefault(t => t.Name == tableName);
            
            if (tableDefinition == null)
            {
                throw new ArgumentException($"Table '{tableName}' not found in schema");
            }

            var entityType = _dynamicDbContextService.GetEntityType(context, tableName);
            if (entityType == null)
            {
                return null;
            }

            // Use Entity Framework's Find method
            var findMethod = context.GetType().GetMethod("Find", new[] { typeof(Type), typeof(object[]) });
            if (findMethod != null)
            {
                return await Task.FromResult(findMethod.Invoke(context, new object[] { entityType, new[] { id } }));
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get record by ID for {TableName}", tableName);
            throw;
        }
    }

    /// <summary>
    /// Create a new record
    /// </summary>
    /// <param name="tableName">Table name</param>
    /// <param name="values">Field values</param>
    /// <param name="tables">Table definitions</param>
    /// <param name="connectionString">Database connection string</param>
    /// <returns>Created record</returns>
    public async Task<object> CreateRecordAsync(
        string tableName, 
        Dictionary<string, object?> values, 
        List<TableDefinition> tables, 
        string connectionString)
    {
        try
        {
            var context = await GetDbContextAsync(tables, connectionString);
            return await _dynamicDbContextService.CreateEntityAsync(context, tableName, values);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create record for {TableName}", tableName);
            throw;
        }
    }

    /// <summary>
    /// Update an existing record
    /// </summary>
    /// <param name="tableName">Table name</param>
    /// <param name="id">Record ID</param>
    /// <param name="values">Updated field values</param>
    /// <param name="tables">Table definitions</param>
    /// <param name="connectionString">Database connection string</param>
    /// <returns>Updated record</returns>
    public async Task<object?> UpdateRecordAsync(
        string tableName, 
        object id, 
        Dictionary<string, object?> values, 
        List<TableDefinition> tables, 
        string connectionString)
    {
        try
        {
            var context = await GetDbContextAsync(tables, connectionString);
            var record = await GetRecordByIdAsync(tableName, id, tables, connectionString);
            
            if (record == null)
            {
                return null;
            }

            // Update properties
            var entityType = record.GetType();
            foreach (var kvp in values)
            {
                var property = entityType.GetProperty(kvp.Key);
                if (property != null && property.CanWrite)
                {
                    var convertedValue = ConvertValue(kvp.Value, property.PropertyType);
                    property.SetValue(record, convertedValue);
                }
            }

            context.Entry(record).State = EntityState.Modified;
            await context.SaveChangesAsync();
            
            return record;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update record for {TableName}", tableName);
            throw;
        }
    }

    /// <summary>
    /// Delete a record
    /// </summary>
    /// <param name="tableName">Table name</param>
    /// <param name="id">Record ID</param>
    /// <param name="tables">Table definitions</param>
    /// <param name="connectionString">Database connection string</param>
    /// <returns>True if deleted, false if not found</returns>
    public async Task<bool> DeleteRecordAsync(
        string tableName, 
        object id, 
        List<TableDefinition> tables, 
        string connectionString)
    {
        try
        {
            var context = await GetDbContextAsync(tables, connectionString);
            var record = await GetRecordByIdAsync(tableName, id, tables, connectionString);
            
            if (record == null)
            {
                return false;
            }

            context.Entry(record).State = EntityState.Deleted;
            await context.SaveChangesAsync();
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete record for {TableName}", tableName);
            throw;
        }
    }

    /// <summary>
    /// Get related records for a foreign key relationship
    /// </summary>
    /// <param name="parentTableName">Parent table name</param>
    /// <param name="parentId">Parent record ID</param>
    /// <param name="childTableName">Child table name</param>
    /// <param name="foreignKeyField">Foreign key field name</param>
    /// <param name="tables">Table definitions</param>
    /// <param name="connectionString">Database connection string</param>
    /// <returns>List of related records</returns>
    public async Task<List<object>> GetRelatedRecordsAsync(
        string parentTableName,
        object parentId,
        string childTableName,
        string foreignKeyField,
        List<TableDefinition> tables,
        string connectionString)
    {
        try
        {
            var filter = new ListFilter
            {
                FieldFilters = { [foreignKeyField] = parentId }
            };

            var result = await GetTableDataAsync(childTableName, tables, connectionString, filter);
            return result.Items;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get related records for {ParentTable}->{ChildTable}", 
                parentTableName, childTableName);
            throw;
        }
    }

    private string GenerateContextKey(List<TableDefinition> tables, string connectionString)
    {
        var tableSignature = string.Join("|", tables.OrderBy(t => t.Name).Select(t => t.Name));
        var connectionHash = connectionString.GetHashCode();
        return $"{tableSignature}_{connectionHash}";
    }

    private IQueryable<object> ApplySearchFilter(IQueryable<object> queryable, TableDefinition table, string searchTerm)
    {
        // This is a simplified implementation
        // In a real scenario, you'd use dynamic LINQ or build expression trees
        return queryable;
    }

    private IQueryable<object> ApplyFieldFilters(IQueryable<object> queryable, TableDefinition table, Dictionary<string, object> filters)
    {
        // This is a simplified implementation
        // In a real scenario, you'd use dynamic LINQ or build expression trees
        return queryable;
    }

    private IQueryable<object> ApplySorting(IQueryable<object> queryable, string sortField, SortDirection direction)
    {
        // This is a simplified implementation
        // In a real scenario, you'd use dynamic LINQ or build expression trees
        return queryable;
    }

    private IQueryable<object> SkipAsync(IQueryable<object> queryable, int count)
    {
        return queryable.Skip(count);
    }

    private async Task<List<object>> TakeAsync(IQueryable<object> queryable, int count)
    {
        // This is a simplified implementation
        // In a real scenario, you'd use the proper async methods
        return await Task.FromResult(queryable.Take(count).ToList());
    }

    private async Task<int> CountAsync(IQueryable<object> queryable)
    {
        // This is a simplified implementation
        // In a real scenario, you'd use the proper async methods
        return await Task.FromResult(queryable.Count());
    }

    private object? ConvertValue(object? value, Type targetType)
    {
        if (value == null)
            return null;

        if (targetType.IsAssignableFrom(value.GetType()))
            return value;

        // Handle nullable types
        if (targetType.IsGenericType && targetType.GetGenericTypeDefinition() == typeof(Nullable<>))
        {
            targetType = Nullable.GetUnderlyingType(targetType)!;
        }

        try
        {
            return Convert.ChangeType(value, targetType);
        }
        catch
        {
            return null;
        }
    }
}
