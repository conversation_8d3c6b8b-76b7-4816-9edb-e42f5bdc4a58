/* Crudible Custom Styles */

/* Smart Input Enhancements */
.smart-input-container {
    position: relative;
}

.smart-input-container .form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.smart-input-container .form-label .text-danger {
    margin-left: 0.25rem;
}

/* Enhanced Form Controls */
.form-control:focus,
.form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-control.is-valid,
.form-select.is-valid {
    border-color: #198754;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='m2.3 6.73.94-.94 1.38 1.38L7.7 4.08 6.76 3.14 4.62 5.28z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-control.is-invalid,
.form-select.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 2.4 2.4m0-2.4L5.8 7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* Date and Time Inputs */
.date-input {
    position: relative;
}

.date-input::-webkit-calendar-picker-indicator {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='%23212529' d='M3.5 0a.5.5 0 0 1 .5.5V1h8V.5a.5.5 0 0 1 1 0V1h1a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h1V.5a.5.5 0 0 1 .5-.5zM2 2a1 1 0 0 0-1 1v1h14V3a1 1 0 0 0-1-1H2zm13 3H1v9a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V5z'/%3e%3c/svg%3e");
    background-size: 1rem 1rem;
    background-repeat: no-repeat;
    background-position: center;
}

/* Numeric Inputs */
.numeric {
    text-align: right;
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type="number"] {
    -moz-appearance: textfield;
}

/* File Input Enhancements */
.file-input-container {
    position: relative;
}

.file-preview {
    margin-top: 0.5rem;
    padding: 0.5rem;
    border: 1px dashed #dee2e6;
    border-radius: 0.375rem;
    text-align: center;
    background-color: #f8f9fa;
}

.file-preview img {
    border-radius: 0.375rem;
}

.file-preview.drag-over {
    border-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.1);
}

/* Color Input */
.form-control-color {
    width: 3rem;
    height: calc(1.5em + 0.75rem + 2px);
    padding: 0.375rem;
}

/* Range Input */
.form-range {
    margin-bottom: 1rem;
}

.form-range:focus {
    outline: 0;
}

.form-range:focus::-webkit-slider-thumb {
    box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-range:focus::-moz-range-thumb {
    box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Smart Select Enhancements */
.smart-select {
    position: relative;
}

.smart-select .loading {
    position: absolute;
    right: 2rem;
    top: 50%;
    transform: translateY(-50%);
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0d6efd;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
}

/* Validation Feedback */
.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #dc3545;
}

.valid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #198754;
}

/* Form Layout Enhancements */
.form-row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -0.5rem;
    margin-left: -0.5rem;
}

.form-row > .col,
.form-row > [class*="col-"] {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
}

/* Required Field Indicator */
.required-indicator {
    color: #dc3545;
    font-weight: bold;
}

/* Help Text */
.form-text {
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #6c757d;
}

/* Field Groups */
.field-group {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: #f8f9fa;
}

.field-group .field-group-title {
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: #495057;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 0.5rem;
}

/* Responsive Form Adjustments */
@media (max-width: 576px) {
    .form-row {
        margin-right: 0;
        margin-left: 0;
    }
    
    .form-row > .col,
    .form-row > [class*="col-"] {
        padding-right: 0;
        padding-left: 0;
        margin-bottom: 1rem;
    }
    
    .btn-group {
        display: flex;
        flex-direction: column;
        width: 100%;
    }
    
    .btn-group .btn {
        margin-bottom: 0.5rem;
        border-radius: 0.375rem !important;
    }
}

/* Table Enhancements */
.table-responsive {
    border-radius: 0.375rem;
    overflow: hidden;
}

.table th {
    border-top: none;
    font-weight: 600;
    background-color: #f8f9fa;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.075);
}

/* Action Buttons */
.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 0.2rem;
}

.btn-outline-info:hover {
    color: #fff;
    background-color: #0dcaf0;
    border-color: #0dcaf0;
}

.btn-outline-warning:hover {
    color: #000;
    background-color: #ffc107;
    border-color: #ffc107;
}

.btn-outline-danger:hover {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545;
}

/* Loading States */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    width: 3rem;
    height: 3rem;
    border: 0.3rem solid #f3f3f3;
    border-top: 0.3rem solid #0d6efd;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Card Enhancements */
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: 600;
}

/* Pagination */
.pagination {
    margin-bottom: 0;
}

.page-link {
    color: #0d6efd;
}

.page-link:hover {
    color: #0a58ca;
}

.page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* Alerts */
.alert {
    border: none;
    border-radius: 0.5rem;
}

.alert-success {
    background-color: #d1e7dd;
    color: #0f5132;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    color: #055160;
}

/* Badges */
.badge {
    font-size: 0.75em;
    font-weight: 500;
}

/* Utility Classes */
.text-truncate-50 {
    max-width: 50ch;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.cursor-pointer {
    cursor: pointer;
}

.border-dashed {
    border-style: dashed !important;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .card {
        background-color: #212529;
        border-color: #495057;
    }
    
    .card-header {
        background-color: #343a40;
        border-color: #495057;
    }
    
    .table th {
        background-color: #343a40;
    }
    
    .field-group {
        background-color: #343a40;
        border-color: #495057;
    }
}
