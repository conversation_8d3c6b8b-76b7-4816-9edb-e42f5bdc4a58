@model SmartInputViewModel

<div class="mb-3">
    <label for="@Model.PropertyName" class="form-label">
        @Model.Field.DisplayName
        @if (!Model.Field.IsNullable)
        {
            <span class="text-danger">*</span>
        }
    </label>

    @switch (Model.InputType)
    {
        case SmartInputType.Text:
            <input type="text" 
                   id="@Model.PropertyName" 
                   name="@Model.PropertyName" 
                   value="@Model.Value" 
                   class="@Model.CssClasses"
                   readonly="@Model.IsReadOnly" />
            break;

        case SmartInputType.Password:
            <input type="password" 
                   id="@Model.PropertyName" 
                   name="@Model.PropertyName" 
                   value="@Model.Value" 
                   class="@Model.CssClasses"
                   readonly="@Model.IsReadOnly" />
            break;

        case SmartInputType.Email:
            <input type="email" 
                   id="@Model.PropertyName" 
                   name="@Model.PropertyName" 
                   value="@Model.Value" 
                   class="@Model.CssClasses"
                   readonly="@Model.IsReadOnly" />
            break;

        case SmartInputType.Number:
            <input type="number" 
                   id="@Model.PropertyName" 
                   name="@Model.PropertyName" 
                   value="@Model.Value" 
                   class="@Model.CssClasses"
                   readonly="@Model.IsReadOnly" />
            break;

        case SmartInputType.Date:
            <input type="date" 
                   id="@Model.PropertyName" 
                   name="@Model.PropertyName" 
                   value="@(Model.Value?.ToString())" 
                   class="@Model.CssClasses"
                   readonly="@Model.IsReadOnly" />
            break;

        case SmartInputType.DateTime:
            <input type="datetime-local" 
                   id="@Model.PropertyName" 
                   name="@Model.PropertyName" 
                   value="@(Model.Value?.ToString())" 
                   class="@Model.CssClasses"
                   readonly="@Model.IsReadOnly" />
            break;

        case SmartInputType.Checkbox:
            <div class="form-check">
                <input type="checkbox" 
                       id="@Model.PropertyName" 
                       name="@Model.PropertyName" 
                       value="true"
                       class="form-check-input"
                       checked="@(Model.Value?.ToString() == "True")"
                       disabled="@Model.IsReadOnly" />
                <label class="form-check-label" for="@Model.PropertyName">
                    @Model.Field.DisplayName
                </label>
            </div>
            break;

        case SmartInputType.Textarea:
            <textarea id="@Model.PropertyName" 
                      name="@Model.PropertyName" 
                      class="@Model.CssClasses"
                      rows="3"
                      readonly="@Model.IsReadOnly">@Model.Value</textarea>
            break;

        case SmartInputType.Select:
            <select id="@Model.PropertyName" 
                    name="@Model.PropertyName" 
                    class="@Model.CssClasses"
                    disabled="@Model.IsReadOnly">
                @if (Model.Field.IsNullable)
                {
                    <option value="">-- Select @Model.Field.DisplayName --</option>
                }
                @foreach (var option in Model.SelectOptions)
                {
                    <option value="@option.Value" 
                            selected="@(option.Value == Model.Value?.ToString() || option.Selected)"
                            disabled="@option.Disabled">
                        @option.Text
                    </option>
                }
            </select>
            break;

        case SmartInputType.File:
            <input type="file" 
                   id="@Model.PropertyName" 
                   name="@Model.PropertyName" 
                   class="@Model.CssClasses"
                   disabled="@Model.IsReadOnly" />
            break;

        default:
            <input type="text" 
                   id="@Model.PropertyName" 
                   name="@Model.PropertyName" 
                   value="@Model.Value" 
                   class="@Model.CssClasses"
                   readonly="@Model.IsReadOnly" />
            break;
    }

    @if (!string.IsNullOrEmpty(Model.Field.Description))
    {
        <div class="form-text">@Model.Field.Description</div>
    }

    @if (Model.HasError && !string.IsNullOrEmpty(Model.ErrorMessage))
    {
        <div class="invalid-feedback d-block">@Model.ErrorMessage</div>
    }
</div>
