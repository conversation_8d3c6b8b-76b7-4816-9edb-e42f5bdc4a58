﻿@page
@model IndexModel
@{
    ViewData["Title"] = "Dashboard";
    ViewData["ShowBreadcrumb"] = false;
}

<!-- Hero Section -->
<div class="row mb-5">
    <div class="col-12">
        <div class="card bg-primary text-white">
            <div class="card-body py-5">
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <h1 class="display-4 fw-bold mb-3">
                            <i class="fas fa-database me-3"></i>
                            Welcome to Crudible
                        </h1>
                        <p class="lead mb-4">
                            The enterprise-grade CRUD generator that automatically creates complete web applications
                            from your database schemas. Build powerful data management interfaces in minutes, not hours.
                        </p>
                        <div class="d-flex flex-wrap gap-3">
                            <a href="/configuration" class="btn btn-light btn-lg">
                                <i class="fas fa-cog me-2"></i>
                                Get Started
                            </a>
                            <a href="/schema/upload" class="btn btn-outline-light btn-lg">
                                <i class="fas fa-upload me-2"></i>
                                Upload Schema
                            </a>
                        </div>
                    </div>
                    <div class="col-lg-4 text-center">
                        <i class="fas fa-magic fa-8x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mb-5">
    <div class="col-md-3 mb-3">
        <div class="card text-center h-100">
            <div class="card-body">
                <div class="text-primary mb-3">
                    <i class="fas fa-table fa-3x"></i>
                </div>
                <h3 class="card-title">@Model.TableCount</h3>
                <p class="card-text text-muted">Tables Configured</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card text-center h-100">
            <div class="card-body">
                <div class="text-success mb-3">
                    <i class="fas fa-database fa-3x"></i>
                </div>
                <h3 class="card-title">@Model.RecordCount</h3>
                <p class="card-text text-muted">Total Records</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card text-center h-100">
            <div class="card-body">
                <div class="text-warning mb-3">
                    <i class="fas fa-history fa-3x"></i>
                </div>
                <h3 class="card-title">@Model.AuditCount</h3>
                <p class="card-text text-muted">Audit Entries</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card text-center h-100">
            <div class="card-body">
                <div class="text-info mb-3">
                    <i class="fas fa-download fa-3x"></i>
                </div>
                <h3 class="card-title">@Model.ExportCount</h3>
                <p class="card-text text-muted">Exports Generated</p>
            </div>
        </div>
    </div>
</div>

<!-- Features Section -->
<div class="row mb-5">
    <div class="col-12">
        <h2 class="text-center mb-4">
            <i class="fas fa-star me-2"></i>
            Key Features
        </h2>
    </div>
    <div class="col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="text-primary mb-3">
                    <i class="fas fa-magic fa-3x"></i>
                </div>
                <h5 class="card-title">Auto-Generated CRUD</h5>
                <p class="card-text">
                    Automatically generate complete Create, Read, Update, Delete interfaces
                    for all your database tables with smart validation and relationships.
                </p>
            </div>
        </div>
    </div>
    <div class="col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="text-success mb-3">
                    <i class="fas fa-mobile-alt fa-3x"></i>
                </div>
                <h5 class="card-title">Responsive Design</h5>
                <p class="card-text">
                    Built with Bootstrap 5 for a modern, responsive interface that works
                    perfectly on desktop, tablet, and mobile devices.
                </p>
            </div>
        </div>
    </div>
    <div class="col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="text-warning mb-3">
                    <i class="fas fa-shield-alt fa-3x"></i>
                </div>
                <h5 class="card-title">Enterprise Security</h5>
                <p class="card-text">
                    Comprehensive audit trails, validation rules, and security features
                    ready for enterprise deployment and compliance requirements.
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
@if (Model.RecentActivity?.Any() == true)
{
    <div class="row mb-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        Recent Activity
                    </h5>
                    <a href="/audit" class="btn btn-outline-primary btn-sm">
                        View All
                    </a>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        @foreach (var activity in Model.RecentActivity.Take(5))
                        {
                            <div class="list-group-item d-flex justify-content-between align-items-start">
                                <div class="ms-2 me-auto">
                                    <div class="fw-bold">@activity.TableName</div>
                                    <small class="text-muted">@activity.Operation - @activity.UserName</small>
                                </div>
                                <small class="text-muted">@activity.Timestamp.ToString("MMM dd, HH:mm")</small>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
}

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 col-lg-3 mb-3">
                        <a href="/configuration" class="btn btn-outline-primary w-100 h-100 d-flex flex-column justify-content-center">
                            <i class="fas fa-cog fa-2x mb-2"></i>
                            <span>Configure Database</span>
                        </a>
                    </div>
                    <div class="col-md-6 col-lg-3 mb-3">
                        <a href="/schema/upload" class="btn btn-outline-success w-100 h-100 d-flex flex-column justify-content-center">
                            <i class="fas fa-upload fa-2x mb-2"></i>
                            <span>Upload Schema</span>
                        </a>
                    </div>
                    <div class="col-md-6 col-lg-3 mb-3">
                        <a href="/tables" class="btn btn-outline-info w-100 h-100 d-flex flex-column justify-content-center">
                            <i class="fas fa-table fa-2x mb-2"></i>
                            <span>Manage Tables</span>
                        </a>
                    </div>
                    <div class="col-md-6 col-lg-3 mb-3">
                        <a href="/export" class="btn btn-outline-warning w-100 h-100 d-flex flex-column justify-content-center">
                            <i class="fas fa-download fa-2x mb-2"></i>
                            <span>Export Data</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
