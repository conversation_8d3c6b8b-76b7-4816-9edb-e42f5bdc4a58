using Crudible.Models;

namespace Crudible.Services.Interfaces;

/// <summary>
/// Interface for processing database schemas from various sources
/// </summary>
public interface ISchemaProcessor
{
    /// <summary>
    /// Process XML schema and extract table definitions
    /// </summary>
    /// <param name="xmlSchema">XML schema content</param>
    /// <returns>List of table definitions</returns>
    Task<List<TableDefinition>> ProcessXmlSchemaAsync(string xmlSchema);

    /// <summary>
    /// Process database connection string and extract table definitions
    /// </summary>
    /// <param name="connectionString">Database connection string</param>
    /// <returns>List of table definitions</returns>
    Task<List<TableDefinition>> ProcessConnectionStringAsync(string connectionString);

    /// <summary>
    /// Validate schema format
    /// </summary>
    /// <param name="schema">Schema content</param>
    /// <param name="schemaType">Type of schema (XML or ConnectionString)</param>
    /// <returns>Validation result</returns>
    Task<SchemaValidationResult> ValidateSchemaAsync(string schema, SchemaType schemaType);

    /// <summary>
    /// Extract relationships between tables
    /// </summary>
    /// <param name="tables">List of table definitions</param>
    /// <returns>List of relationships</returns>
    Task<List<TableRelationship>> ExtractRelationshipsAsync(List<TableDefinition> tables);
}

/// <summary>
/// Interface for generating CRUD pages dynamically
/// </summary>
public interface ICrudPageGenerator
{
    /// <summary>
    /// Generate all CRUD pages for a table
    /// </summary>
    /// <param name="tableDefinition">Table definition</param>
    /// <returns>Generated page information</returns>
    Task<CrudPageSet> GenerateCrudPagesAsync(TableDefinition tableDefinition);

    /// <summary>
    /// Generate list page for a table
    /// </summary>
    /// <param name="tableDefinition">Table definition</param>
    /// <returns>Generated page content</returns>
    Task<string> GenerateListPageAsync(TableDefinition tableDefinition);

    /// <summary>
    /// Generate create page for a table
    /// </summary>
    /// <param name="tableDefinition">Table definition</param>
    /// <returns>Generated page content</returns>
    Task<string> GenerateCreatePageAsync(TableDefinition tableDefinition);

    /// <summary>
    /// Generate edit page for a table
    /// </summary>
    /// <param name="tableDefinition">Table definition</param>
    /// <returns>Generated page content</returns>
    Task<string> GenerateEditPageAsync(TableDefinition tableDefinition);

    /// <summary>
    /// Generate details page for a table
    /// </summary>
    /// <param name="tableDefinition">Table definition</param>
    /// <returns>Generated page content</returns>
    Task<string> GenerateDetailsPageAsync(TableDefinition tableDefinition);

    /// <summary>
    /// Generate delete page for a table
    /// </summary>
    /// <param name="tableDefinition">Table definition</param>
    /// <returns>Generated page content</returns>
    Task<string> GenerateDeletePageAsync(TableDefinition tableDefinition);
}

/// <summary>
/// Interface for audit trail functionality
/// </summary>
public interface IAuditService
{
    /// <summary>
    /// Log an audit entry
    /// </summary>
    /// <param name="entry">Audit entry to log</param>
    Task LogAuditEntryAsync(AuditEntry entry);

    /// <summary>
    /// Get audit entries for a specific table and record
    /// </summary>
    /// <param name="tableName">Table name</param>
    /// <param name="recordId">Record ID</param>
    /// <returns>List of audit entries</returns>
    Task<List<AuditEntry>> GetAuditEntriesAsync(string tableName, string recordId);

    /// <summary>
    /// Get audit entries for a date range
    /// </summary>
    /// <param name="startDate">Start date</param>
    /// <param name="endDate">End date</param>
    /// <returns>List of audit entries</returns>
    Task<List<AuditEntry>> GetAuditEntriesAsync(DateTime startDate, DateTime endDate);

    /// <summary>
    /// Get paged audit entries with filtering
    /// </summary>
    /// <param name="filter">Audit filter criteria</param>
    /// <returns>Paged result of audit entries</returns>
    Task<PagedResult<AuditEntry>> GetAuditEntriesAsync(AuditFilter filter);

    /// <summary>
    /// Get audit summary for reporting
    /// </summary>
    /// <param name="startDate">Start date</param>
    /// <param name="endDate">End date</param>
    /// <returns>Audit summary</returns>
    Task<AuditSummary> GetAuditSummaryAsync(DateTime startDate, DateTime endDate);

    /// <summary>
    /// Log CRUD operation
    /// </summary>
    /// <param name="tableName">Table name</param>
    /// <param name="recordId">Record ID</param>
    /// <param name="operation">CRUD operation</param>
    /// <param name="oldValues">Old values (for updates/deletes)</param>
    /// <param name="newValues">New values (for creates/updates)</param>
    /// <param name="success">Whether operation was successful</param>
    /// <param name="errorMessage">Error message if operation failed</param>
    Task LogCrudOperationAsync(string tableName, string recordId, AuditOperation operation, object? oldValues = null, object? newValues = null, bool success = true, string? errorMessage = null);

    /// <summary>
    /// Clean up old audit entries based on retention policy
    /// </summary>
    Task CleanupOldEntriesAsync();
}

/// <summary>
/// Interface for data export functionality
/// </summary>
public interface IExportService
{
    /// <summary>
    /// Export table data to Excel format
    /// </summary>
    /// <param name="request">Export request</param>
    /// <returns>Excel file as byte array</returns>
    Task<byte[]> ExportToExcelAsync(ExportRequest request);

    /// <summary>
    /// Export table data to CSV format
    /// </summary>
    /// <param name="request">Export request</param>
    /// <returns>CSV file as byte array</returns>
    Task<byte[]> ExportToCsvAsync(ExportRequest request);

    /// <summary>
    /// Export table data to JSON format
    /// </summary>
    /// <param name="request">Export request</param>
    /// <returns>JSON file as byte array</returns>
    Task<byte[]> ExportToJsonAsync(ExportRequest request);

    /// <summary>
    /// Get export file name based on template
    /// </summary>
    /// <param name="tableName">Table name</param>
    /// <param name="format">Export format</param>
    /// <returns>Generated file name</returns>
    Task<string> GetExportFileNameAsync(string tableName, Models.ExportFormat format);
}

/// <summary>
/// Interface for validation service
/// </summary>
public interface IValidationService
{
    /// <summary>
    /// Validate entity based on table definition and custom rules
    /// </summary>
    /// <param name="entity">Entity to validate</param>
    /// <param name="tableDefinition">Table definition</param>
    /// <returns>Validation result</returns>
    Task<Models.ValidationResult> ValidateEntityAsync(object entity, TableDefinition tableDefinition);

    /// <summary>
    /// Get validation attributes for a field
    /// </summary>
    /// <param name="fieldDefinition">Field definition</param>
    /// <returns>List of validation attributes</returns>
    List<System.ComponentModel.DataAnnotations.ValidationAttribute> GetValidationAttributes(FieldDefinition fieldDefinition);

    /// <summary>
    /// Validate unique constraints
    /// </summary>
    /// <param name="entity">Entity to validate</param>
    /// <param name="tableDefinition">Table definition</param>
    /// <returns>Validation result</returns>
    Task<Models.ValidationResult> ValidateUniqueConstraintsAsync(object entity, TableDefinition tableDefinition);
}

/// <summary>
/// Schema type enumeration
/// </summary>
public enum SchemaType
{
    Xml,
    ConnectionString
}


