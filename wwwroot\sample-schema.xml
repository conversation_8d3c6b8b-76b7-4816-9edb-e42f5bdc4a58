<?xml version="1.0" encoding="utf-8"?>
<CrudibleSchema version="1.0" type="crudible">
  <Description>Sample e-commerce database schema for Crudible demonstration</Description>
  
  <Tables>
    <!-- Categories Table -->
    <Table name="Categories" schema="dbo" displayName="Product Categories">
      <Description>Product categories for organizing products</Description>
      
      <Fields>
        <Field name="CategoryId" type="int" primaryKey="true" identity="true" nullable="false" displayName="Category ID">
          <Description>Unique identifier for the category</Description>
        </Field>
        <Field name="CategoryName" type="nvarchar" maxLength="100" nullable="false" unique="true" displayName="Category Name">
          <Description>Name of the product category</Description>
        </Field>
        <Field name="Description" type="nvarchar" maxLength="500" nullable="true" displayName="Description">
          <Description>Detailed description of the category</Description>
        </Field>
        <Field name="ParentCategoryId" type="int" nullable="true" displayName="Parent Category" 
               referencedTable="Categories" referencedField="CategoryId" displayField="CategoryName">
          <Description>Reference to parent category for hierarchical structure</Description>
        </Field>
        <Field name="IsActive" type="bit" nullable="false" defaultValue="1" displayName="Is Active">
          <Description>Whether the category is currently active</Description>
        </Field>
        <Field name="CreatedDate" type="datetime2" nullable="false" defaultValue="GETUTCDATE()" displayName="Created Date">
          <Description>Date and time when the category was created</Description>
        </Field>
        <Field name="ModifiedDate" type="datetime2" nullable="true" displayName="Modified Date">
          <Description>Date and time when the category was last modified</Description>
        </Field>
      </Fields>
      
      <PrimaryKeys>
        <Key field="CategoryId" />
      </PrimaryKeys>
      
      <ForeignKeys>
        <ForeignKey name="FK_Categories_ParentCategory" referencedTable="Categories" onDelete="SetNull" onUpdate="Cascade">
          <LocalFields>
            <Field name="ParentCategoryId" />
          </LocalFields>
          <ReferencedFields>
            <Field name="CategoryId" />
          </ReferencedFields>
        </ForeignKey>
      </ForeignKeys>
    </Table>

    <!-- Products Table -->
    <Table name="Products" schema="dbo" displayName="Products">
      <Description>Product catalog with detailed information</Description>
      
      <Fields>
        <Field name="ProductId" type="int" primaryKey="true" identity="true" nullable="false" displayName="Product ID">
          <Description>Unique identifier for the product</Description>
        </Field>
        <Field name="ProductName" type="nvarchar" maxLength="200" nullable="false" displayName="Product Name">
          <Description>Name of the product</Description>
        </Field>
        <Field name="ProductCode" type="nvarchar" maxLength="50" nullable="false" unique="true" displayName="Product Code">
          <Description>Unique product code for identification</Description>
        </Field>
        <Field name="CategoryId" type="int" nullable="false" displayName="Category" 
               referencedTable="Categories" referencedField="CategoryId" displayField="CategoryName">
          <Description>Category this product belongs to</Description>
        </Field>
        <Field name="Description" type="nvarchar" maxLength="1000" nullable="true" displayName="Description">
          <Description>Detailed product description</Description>
        </Field>
        <Field name="Price" type="decimal" precision="18" scale="2" nullable="false" displayName="Price">
          <Description>Current price of the product</Description>
        </Field>
        <Field name="CostPrice" type="decimal" precision="18" scale="2" nullable="true" displayName="Cost Price">
          <Description>Cost price for calculating profit margins</Description>
        </Field>
        <Field name="StockQuantity" type="int" nullable="false" defaultValue="0" displayName="Stock Quantity">
          <Description>Current stock quantity available</Description>
        </Field>
        <Field name="MinStockLevel" type="int" nullable="false" defaultValue="0" displayName="Minimum Stock Level">
          <Description>Minimum stock level before reordering</Description>
        </Field>
        <Field name="IsActive" type="bit" nullable="false" defaultValue="1" displayName="Is Active">
          <Description>Whether the product is currently active for sale</Description>
        </Field>
        <Field name="Weight" type="decimal" precision="10" scale="3" nullable="true" displayName="Weight (kg)">
          <Description>Product weight in kilograms</Description>
        </Field>
        <Field name="Dimensions" type="nvarchar" maxLength="100" nullable="true" displayName="Dimensions">
          <Description>Product dimensions (L x W x H)</Description>
        </Field>
        <Field name="ImageUrl" type="nvarchar" maxLength="500" nullable="true" displayName="Image URL">
          <Description>URL to the product image</Description>
        </Field>
        <Field name="CreatedDate" type="datetime2" nullable="false" defaultValue="GETUTCDATE()" displayName="Created Date">
          <Description>Date and time when the product was created</Description>
        </Field>
        <Field name="ModifiedDate" type="datetime2" nullable="true" displayName="Modified Date">
          <Description>Date and time when the product was last modified</Description>
        </Field>
      </Fields>
      
      <PrimaryKeys>
        <Key field="ProductId" />
      </PrimaryKeys>
      
      <ForeignKeys>
        <ForeignKey name="FK_Products_Category" referencedTable="Categories" onDelete="Restrict" onUpdate="Cascade">
          <LocalFields>
            <Field name="CategoryId" />
          </LocalFields>
          <ReferencedFields>
            <Field name="CategoryId" />
          </ReferencedFields>
        </ForeignKey>
      </ForeignKeys>
    </Table>

    <!-- Customers Table -->
    <Table name="Customers" schema="dbo" displayName="Customers">
      <Description>Customer information and contact details</Description>
      
      <Fields>
        <Field name="CustomerId" type="int" primaryKey="true" identity="true" nullable="false" displayName="Customer ID">
          <Description>Unique identifier for the customer</Description>
        </Field>
        <Field name="FirstName" type="nvarchar" maxLength="100" nullable="false" displayName="First Name">
          <Description>Customer's first name</Description>
        </Field>
        <Field name="LastName" type="nvarchar" maxLength="100" nullable="false" displayName="Last Name">
          <Description>Customer's last name</Description>
        </Field>
        <Field name="Email" type="nvarchar" maxLength="255" nullable="false" unique="true" displayName="Email Address">
          <Description>Customer's email address</Description>
        </Field>
        <Field name="Phone" type="nvarchar" maxLength="20" nullable="true" displayName="Phone Number">
          <Description>Customer's phone number</Description>
        </Field>
        <Field name="DateOfBirth" type="date" nullable="true" displayName="Date of Birth">
          <Description>Customer's date of birth</Description>
        </Field>
        <Field name="Address" type="nvarchar" maxLength="500" nullable="true" displayName="Address">
          <Description>Customer's mailing address</Description>
        </Field>
        <Field name="City" type="nvarchar" maxLength="100" nullable="true" displayName="City">
          <Description>Customer's city</Description>
        </Field>
        <Field name="State" type="nvarchar" maxLength="100" nullable="true" displayName="State/Province">
          <Description>Customer's state or province</Description>
        </Field>
        <Field name="PostalCode" type="nvarchar" maxLength="20" nullable="true" displayName="Postal Code">
          <Description>Customer's postal or ZIP code</Description>
        </Field>
        <Field name="Country" type="nvarchar" maxLength="100" nullable="true" displayName="Country">
          <Description>Customer's country</Description>
        </Field>
        <Field name="IsActive" type="bit" nullable="false" defaultValue="1" displayName="Is Active">
          <Description>Whether the customer account is active</Description>
        </Field>
        <Field name="CreatedDate" type="datetime2" nullable="false" defaultValue="GETUTCDATE()" displayName="Created Date">
          <Description>Date and time when the customer was created</Description>
        </Field>
        <Field name="ModifiedDate" type="datetime2" nullable="true" displayName="Modified Date">
          <Description>Date and time when the customer was last modified</Description>
        </Field>
      </Fields>
      
      <PrimaryKeys>
        <Key field="CustomerId" />
      </PrimaryKeys>
    </Table>

    <!-- Orders Table -->
    <Table name="Orders" schema="dbo" displayName="Orders">
      <Description>Customer orders and order information</Description>
      
      <Fields>
        <Field name="OrderId" type="int" primaryKey="true" identity="true" nullable="false" displayName="Order ID">
          <Description>Unique identifier for the order</Description>
        </Field>
        <Field name="CustomerId" type="int" nullable="false" displayName="Customer" 
               referencedTable="Customers" referencedField="CustomerId" displayField="FirstName">
          <Description>Customer who placed the order</Description>
        </Field>
        <Field name="OrderDate" type="datetime2" nullable="false" defaultValue="GETUTCDATE()" displayName="Order Date">
          <Description>Date and time when the order was placed</Description>
        </Field>
        <Field name="OrderStatus" type="nvarchar" maxLength="50" nullable="false" defaultValue="Pending" displayName="Order Status">
          <Description>Current status of the order</Description>
        </Field>
        <Field name="TotalAmount" type="decimal" precision="18" scale="2" nullable="false" displayName="Total Amount">
          <Description>Total amount of the order</Description>
        </Field>
        <Field name="TaxAmount" type="decimal" precision="18" scale="2" nullable="false" defaultValue="0" displayName="Tax Amount">
          <Description>Tax amount for the order</Description>
        </Field>
        <Field name="ShippingAmount" type="decimal" precision="18" scale="2" nullable="false" defaultValue="0" displayName="Shipping Amount">
          <Description>Shipping cost for the order</Description>
        </Field>
        <Field name="ShippingAddress" type="nvarchar" maxLength="500" nullable="true" displayName="Shipping Address">
          <Description>Address where the order should be shipped</Description>
        </Field>
        <Field name="Notes" type="nvarchar" maxLength="1000" nullable="true" displayName="Notes">
          <Description>Additional notes for the order</Description>
        </Field>
        <Field name="CreatedDate" type="datetime2" nullable="false" defaultValue="GETUTCDATE()" displayName="Created Date">
          <Description>Date and time when the order was created</Description>
        </Field>
        <Field name="ModifiedDate" type="datetime2" nullable="true" displayName="Modified Date">
          <Description>Date and time when the order was last modified</Description>
        </Field>
      </Fields>
      
      <PrimaryKeys>
        <Key field="OrderId" />
      </PrimaryKeys>
      
      <ForeignKeys>
        <ForeignKey name="FK_Orders_Customer" referencedTable="Customers" onDelete="Restrict" onUpdate="Cascade">
          <LocalFields>
            <Field name="CustomerId" />
          </LocalFields>
          <ReferencedFields>
            <Field name="CustomerId" />
          </ReferencedFields>
        </ForeignKey>
      </ForeignKeys>
    </Table>

    <!-- OrderItems Table -->
    <Table name="OrderItems" schema="dbo" displayName="Order Items">
      <Description>Individual items within customer orders</Description>
      
      <Fields>
        <Field name="OrderItemId" type="int" primaryKey="true" identity="true" nullable="false" displayName="Order Item ID">
          <Description>Unique identifier for the order item</Description>
        </Field>
        <Field name="OrderId" type="int" nullable="false" displayName="Order" 
               referencedTable="Orders" referencedField="OrderId" displayField="OrderId">
          <Description>Order this item belongs to</Description>
        </Field>
        <Field name="ProductId" type="int" nullable="false" displayName="Product" 
               referencedTable="Products" referencedField="ProductId" displayField="ProductName">
          <Description>Product being ordered</Description>
        </Field>
        <Field name="Quantity" type="int" nullable="false" displayName="Quantity">
          <Description>Quantity of the product ordered</Description>
        </Field>
        <Field name="UnitPrice" type="decimal" precision="18" scale="2" nullable="false" displayName="Unit Price">
          <Description>Price per unit at the time of order</Description>
        </Field>
        <Field name="TotalPrice" type="decimal" precision="18" scale="2" nullable="false" displayName="Total Price">
          <Description>Total price for this line item (Quantity × Unit Price)</Description>
        </Field>
        <Field name="CreatedDate" type="datetime2" nullable="false" defaultValue="GETUTCDATE()" displayName="Created Date">
          <Description>Date and time when the order item was created</Description>
        </Field>
      </Fields>
      
      <PrimaryKeys>
        <Key field="OrderItemId" />
      </PrimaryKeys>
      
      <ForeignKeys>
        <ForeignKey name="FK_OrderItems_Order" referencedTable="Orders" onDelete="Cascade" onUpdate="Cascade">
          <LocalFields>
            <Field name="OrderId" />
          </LocalFields>
          <ReferencedFields>
            <Field name="OrderId" />
          </ReferencedFields>
        </ForeignKey>
        <ForeignKey name="FK_OrderItems_Product" referencedTable="Products" onDelete="Restrict" onUpdate="Cascade">
          <LocalFields>
            <Field name="ProductId" />
          </LocalFields>
          <ReferencedFields>
            <Field name="ProductId" />
          </ReferencedFields>
        </ForeignKey>
      </ForeignKeys>
    </Table>
  </Tables>
</CrudibleSchema>
