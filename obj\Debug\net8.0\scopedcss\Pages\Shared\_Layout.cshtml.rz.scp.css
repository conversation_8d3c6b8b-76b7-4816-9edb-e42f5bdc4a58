/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-ld0zrl05lh] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-ld0zrl05lh] {
  color: #0077cc;
}

.btn-primary[b-ld0zrl05lh] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-ld0zrl05lh], .nav-pills .show > .nav-link[b-ld0zrl05lh] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-ld0zrl05lh] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-ld0zrl05lh] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-ld0zrl05lh] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-ld0zrl05lh] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-ld0zrl05lh] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
