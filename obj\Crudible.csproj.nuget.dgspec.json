{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\augment-projects\\Crudible\\Crudible.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\augment-projects\\Crudible\\Crudible.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\Crudible\\Crudible.csproj", "projectName": "Crudible", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\Crudible\\Crudible.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\Crudible\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"EPPlus": {"target": "Package", "version": "[8.0.7, )"}, "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.CodeAnalysis.Analyzers": {"target": "Package", "version": "[3.3.4, )"}, "Microsoft.CodeAnalysis.CSharp": {"target": "Package", "version": "[4.8.0, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[5.2.2, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.7, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "QuestPDF": {"target": "Package", "version": "[2025.7.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.15, 8.0.15]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[8.0.15, 8.0.15]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.15, 8.0.15]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[8.0.15, 8.0.15]"}], "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}