@page
@model TablesIndexModel
@using Crudible.Models
@{
    ViewData["Title"] = "Table Management";
    ViewData["Breadcrumbs"] = new List<BreadcrumbItem>
    {
        new BreadcrumbItem("Tables", "/tables", true, "fas fa-table")
    };
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0"><i class="fas fa-table me-2"></i>Table Management</h4>
                    <div>
                        <a href="/config" class="btn btn-outline-primary">
                            <i class="fas fa-cog me-1"></i>Configuration
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if (!string.IsNullOrEmpty(TempData["SuccessMessage"]?.ToString()))
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>@TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(TempData["ErrorMessage"]?.ToString()))
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>@TempData["ErrorMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    @if (Model.AvailableTables.Any())
                    {
                        <div class="row">
                            @foreach (var table in Model.AvailableTables)
                            {
                                <div class="col-md-6 col-lg-4 mb-4">
                                    <div class="card h-100 table-card">
                                        <div class="card-body">
                                            <h5 class="card-title">
                                                <i class="fas fa-table text-primary me-2"></i>
                                                @table.DisplayName
                                            </h5>
                                            <p class="card-text text-muted">
                                                @if (!string.IsNullOrEmpty(table.Description))
                                                {
                                                    @table.Description
                                                }
                                                else
                                                {
                                                    <span>Table: @table.Name</span>
                                                }
                                            </p>
                                            <div class="mb-3">
                                                <small class="text-muted">
                                                    <i class="fas fa-columns me-1"></i>@table.FieldCount fields
                                                    @if (table.HasCrudPages)
                                                    {
                                                        <span class="badge bg-success ms-2">CRUD Generated</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-secondary ms-2">Not Generated</span>
                                                    }
                                                </small>
                                            </div>
                                        </div>
                                        <div class="card-footer bg-transparent">
                                            <div class="btn-group w-100" role="group">
                                                @if (table.HasCrudPages)
                                                {
                                                    <a href="/@table.Name.ToLower()" class="btn btn-primary btn-sm">
                                                        <i class="fas fa-eye me-1"></i>View
                                                    </a>
                                                    <button type="button" class="btn btn-warning btn-sm" onclick="regenerateTable('@table.Name')">
                                                        <i class="fas fa-sync me-1"></i>Regenerate
                                                    </button>
                                                }
                                                else
                                                {
                                                    <button type="button" class="btn btn-success btn-sm" onclick="generateTable('@table.Name')">
                                                        <i class="fas fa-magic me-1"></i>Generate CRUD
                                                    </button>
                                                }
                                                <button type="button" class="btn btn-info btn-sm" onclick="viewSchema('@table.Name')">
                                                    <i class="fas fa-info-circle me-1"></i>Schema
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>

                        <div class="mt-4">
                            <div class="row">
                                <div class="col-md-6">
                                    <button type="button" class="btn btn-success" onclick="generateAllTables()">
                                        <i class="fas fa-magic me-1"></i>Generate All CRUD Pages
                                    </button>
                                </div>
                                <div class="col-md-6 text-end">
                                    <span class="text-muted">
                                        Total: @Model.AvailableTables.Count tables
                                        (@Model.AvailableTables.Count(t => t.HasCrudPages) with CRUD pages)
                                    </span>
                                </div>
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-table fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Tables Available</h5>
                            <p class="text-muted">
                                Please configure your database connection or upload a schema file to get started.
                            </p>
                            <a href="/config" class="btn btn-primary">
                                <i class="fas fa-cog me-1"></i>Go to Configuration
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Schema Modal -->
<div class="modal fade" id="schemaModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Table Schema</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="schemaContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.table-card {
    transition: transform 0.2s;
}

.table-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
</style>

<script>
function generateTable(tableName) {
    if (confirm(`Generate CRUD pages for table "${tableName}"?`)) {
        showLoading();
        fetch('/tables/generate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            },
            body: JSON.stringify({ tableName: tableName })
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showSuccess(data.message || `CRUD pages generated successfully for ${tableName}!`);
                setTimeout(() => location.reload(), 1500);
            } else {
                showError(data.message || 'Failed to generate CRUD pages.');
            }
        })
        .catch(error => {
            hideLoading();
            showError('An error occurred while generating CRUD pages.');
            console.error('Error:', error);
        });
    }
}

function regenerateTable(tableName) {
    if (confirm(`Regenerate CRUD pages for table "${tableName}"? This will overwrite existing pages.`)) {
        generateTable(tableName);
    }
}

function generateAllTables() {
    if (confirm('Generate CRUD pages for all tables? This may take a few moments.')) {
        showLoading();
        fetch('/tables/generate-all', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            }
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showSuccess(data.message || 'All CRUD pages generated successfully!');
                setTimeout(() => location.reload(), 2000);
            } else {
                showError(data.message || 'Failed to generate some CRUD pages.');
            }
        })
        .catch(error => {
            hideLoading();
            showError('An error occurred while generating CRUD pages.');
            console.error('Error:', error);
        });
    }
}

function viewSchema(tableName) {
    $('#schemaModal').modal('show');
    $('#schemaContent').html('<div class="text-center"><div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div></div>');
    
    fetch(`/tables/schema/${tableName}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = `<h6>${data.tableName}</h6>`;
                html += '<div class="table-responsive"><table class="table table-sm">';
                html += '<thead><tr><th>Column</th><th>Type</th><th>Nullable</th><th>Default</th></tr></thead><tbody>';
                
                data.columns.forEach(col => {
                    html += `<tr>
                        <td><strong>${col.name}</strong></td>
                        <td><code>${col.dataType}</code></td>
                        <td>${col.isNullable ? '<span class="badge bg-warning">Yes</span>' : '<span class="badge bg-success">No</span>'}</td>
                        <td>${col.defaultValue || '-'}</td>
                    </tr>`;
                });
                
                html += '</tbody></table></div>';
                $('#schemaContent').html(html);
            } else {
                $('#schemaContent').html('<div class="alert alert-danger">Failed to load schema information.</div>');
            }
        })
        .catch(error => {
            $('#schemaContent').html('<div class="alert alert-danger">Error loading schema information.</div>');
            console.error('Error:', error);
        });
}

function showLoading() {
    // You can implement a loading overlay here
}

function hideLoading() {
    // Hide loading overlay
}

function showSuccess(message) {
    // Show success toast/alert
    alert(message);
}

function showError(message) {
    // Show error toast/alert
    alert(message);
}
</script>
