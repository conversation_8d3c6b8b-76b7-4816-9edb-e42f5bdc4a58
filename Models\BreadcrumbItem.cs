namespace Crudible.Models;

/// <summary>
/// Represents a breadcrumb navigation item
/// </summary>
public class BreadcrumbItem
{
    /// <summary>
    /// Display title of the breadcrumb
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// URL for the breadcrumb link
    /// </summary>
    public string? Url { get; set; }

    /// <summary>
    /// Whether this is the active (current) breadcrumb
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Icon class for the breadcrumb (optional)
    /// </summary>
    public string? Icon { get; set; }

    /// <summary>
    /// Create a new breadcrumb item
    /// </summary>
    /// <param name="title">Display title</param>
    /// <param name="url">URL (null for active item)</param>
    /// <param name="isActive">Whether this is the active item</param>
    /// <param name="icon">Optional icon class</param>
    public BreadcrumbItem(string title, string? url = null, bool isActive = false, string? icon = null)
    {
        Title = title;
        Url = url;
        IsActive = isActive;
        Icon = icon;
    }

    /// <summary>
    /// Create a new breadcrumb item (parameterless constructor for model binding)
    /// </summary>
    public BreadcrumbItem()
    {
    }
}
