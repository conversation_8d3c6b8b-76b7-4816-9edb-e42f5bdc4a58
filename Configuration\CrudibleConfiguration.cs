using System.ComponentModel.DataAnnotations;

namespace Crudible.Configuration;

/// <summary>
/// Main configuration class for the Crudible application
/// </summary>
public class CrudibleConfiguration
{
    /// <summary>
    /// Database connection configuration
    /// </summary>
    public DatabaseConfiguration Database { get; set; } = new();

    /// <summary>
    /// Schema configuration settings
    /// </summary>
    public SchemaConfiguration Schema { get; set; } = new();

    /// <summary>
    /// UI and display configuration
    /// </summary>
    public UIConfiguration UI { get; set; } = new();

    /// <summary>
    /// Export functionality configuration
    /// </summary>
    public ExportConfiguration Export { get; set; } = new();

    /// <summary>
    /// Audit trail configuration
    /// </summary>
    public AuditConfiguration Audit { get; set; } = new();
}

/// <summary>
/// Database connection configuration
/// </summary>
public class DatabaseConfiguration
{
    /// <summary>
    /// SQL Server connection string
    /// </summary>
    public string? ConnectionString { get; set; }

    /// <summary>
    /// Database provider type
    /// </summary>
    public DatabaseProvider Provider { get; set; } = DatabaseProvider.SqlServer;

    /// <summary>
    /// Connection timeout in seconds
    /// </summary>
    public int CommandTimeout { get; set; } = 30;

    /// <summary>
    /// Enable connection pooling
    /// </summary>
    public bool EnablePooling { get; set; } = true;
}

/// <summary>
/// Schema configuration settings
/// </summary>
public class SchemaConfiguration
{
    /// <summary>
    /// XML schema file path or content
    /// </summary>
    public string? XmlSchema { get; set; }

    /// <summary>
    /// List of tables to include in CRUD generation
    /// </summary>
    public List<string> IncludedTables { get; set; } = new();

    /// <summary>
    /// List of tables to exclude from CRUD generation
    /// </summary>
    public List<string> ExcludedTables { get; set; } = new();

    /// <summary>
    /// Custom validation rules per table/field
    /// </summary>
    public Dictionary<string, TableValidationRules> ValidationRules { get; set; } = new();

    /// <summary>
    /// Auto-detect relationships
    /// </summary>
    public bool AutoDetectRelationships { get; set; } = true;
}

/// <summary>
/// UI configuration settings
/// </summary>
public class UIConfiguration
{
    /// <summary>
    /// Application title
    /// </summary>
    public string ApplicationTitle { get; set; } = "Crudible - Enterprise CRUD Generator";

    /// <summary>
    /// Items per page for list views
    /// </summary>
    public int ItemsPerPage { get; set; } = 25;

    /// <summary>
    /// Enable responsive design
    /// </summary>
    public bool EnableResponsiveDesign { get; set; } = true;

    /// <summary>
    /// Bootstrap theme
    /// </summary>
    public string BootstrapTheme { get; set; } = "default";

    /// <summary>
    /// Show audit information in UI
    /// </summary>
    public bool ShowAuditInfo { get; set; } = true;
}

/// <summary>
/// Export configuration settings
/// </summary>
public class ExportConfiguration
{
    /// <summary>
    /// Enable PDF export
    /// </summary>
    public bool EnablePdfExport { get; set; } = true;

    /// <summary>
    /// Enable Excel export
    /// </summary>
    public bool EnableExcelExport { get; set; } = true;

    /// <summary>
    /// Maximum records per export
    /// </summary>
    public int MaxExportRecords { get; set; } = 10000;

    /// <summary>
    /// Export file name template
    /// </summary>
    public string FileNameTemplate { get; set; } = "{TableName}_{DateTime:yyyyMMdd_HHmmss}";
}

/// <summary>
/// Audit configuration settings
/// </summary>
public class AuditConfiguration
{
    /// <summary>
    /// Enable audit trail
    /// </summary>
    public bool EnableAuditTrail { get; set; } = true;

    /// <summary>
    /// Audit table name
    /// </summary>
    public string AuditTableName { get; set; } = "AuditLog";

    /// <summary>
    /// Retain audit records for days (0 = forever)
    /// </summary>
    public int RetentionDays { get; set; } = 365;

    /// <summary>
    /// Log read operations
    /// </summary>
    public bool LogReadOperations { get; set; } = false;

    /// <summary>
    /// Log create operations
    /// </summary>
    public bool LogCreateOperations { get; set; } = true;

    /// <summary>
    /// Log update operations
    /// </summary>
    public bool LogUpdateOperations { get; set; } = true;

    /// <summary>
    /// Log delete operations
    /// </summary>
    public bool LogDeleteOperations { get; set; } = true;
}

/// <summary>
/// Table-specific validation rules
/// </summary>
public class TableValidationRules
{
    /// <summary>
    /// Field-specific validation rules
    /// </summary>
    public Dictionary<string, FieldValidationRule> FieldRules { get; set; } = new();

    /// <summary>
    /// Custom business rules
    /// </summary>
    public List<string> BusinessRules { get; set; } = new();
}

/// <summary>
/// Field-specific validation rule
/// </summary>
public class FieldValidationRule
{
    /// <summary>
    /// Regular expression pattern
    /// </summary>
    public string? RegexPattern { get; set; }

    /// <summary>
    /// Minimum length
    /// </summary>
    public int? MinLength { get; set; }

    /// <summary>
    /// Maximum length
    /// </summary>
    public int? MaxLength { get; set; }

    /// <summary>
    /// Minimum value (for numeric fields)
    /// </summary>
    public decimal? MinValue { get; set; }

    /// <summary>
    /// Maximum value (for numeric fields)
    /// </summary>
    public decimal? MaxValue { get; set; }

    /// <summary>
    /// Custom validation message
    /// </summary>
    public string? ValidationMessage { get; set; }

    /// <summary>
    /// Field is required
    /// </summary>
    public bool IsRequired { get; set; }

    /// <summary>
    /// Field must be unique
    /// </summary>
    public bool IsUnique { get; set; }
}

/// <summary>
/// Database provider types
/// </summary>
public enum DatabaseProvider
{
    SqlServer,
    MySQL,
    PostgreSQL,
    SQLite,
    Oracle
}
