using Crudible.Models;
using Microsoft.EntityFrameworkCore;

namespace Crudible.Data;

/// <summary>
/// Main database context for Crudible application
/// </summary>
public class CrudibleDbContext : DbContext
{
    public CrudibleDbContext(DbContextOptions<CrudibleDbContext> options) : base(options)
    {
    }

    /// <summary>
    /// Audit log entries
    /// </summary>
    public DbSet<AuditEntry> AuditEntries { get; set; }

    /// <summary>
    /// Table definitions
    /// </summary>
    public DbSet<TableDefinition> TableDefinitions { get; set; }

    /// <summary>
    /// Field definitions
    /// </summary>
    public DbSet<FieldDefinition> FieldDefinitions { get; set; }

    /// <summary>
    /// Table relationships
    /// </summary>
    public DbSet<TableRelationship> TableRelationships { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure AuditEntry
        modelBuilder.Entity<AuditEntry>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.TableName).IsRequired().HasMaxLength(255);
            entity.Property(e => e.RecordId).IsRequired().HasMaxLength(255);
            entity.Property(e => e.UserId).HasMaxLength(255);
            entity.Property(e => e.UserName).HasMaxLength(255);
            entity.Property(e => e.IpAddress).HasMaxLength(45);
            entity.Property(e => e.UserAgent).HasMaxLength(500);
            entity.Property(e => e.Timestamp).IsRequired();
            entity.Property(e => e.Operation).IsRequired();

            // Indexes for performance
            entity.HasIndex(e => e.TableName);
            entity.HasIndex(e => e.RecordId);
            entity.HasIndex(e => e.UserId);
            entity.HasIndex(e => e.Timestamp);
            entity.HasIndex(e => new { e.TableName, e.RecordId });
        });

        // Configure TableDefinition
        modelBuilder.Entity<TableDefinition>(entity =>
        {
            entity.HasKey(e => e.Name);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(255);
            entity.Property(e => e.Schema).HasMaxLength(255);
            entity.Property(e => e.DisplayName).IsRequired().HasMaxLength(255);
            entity.Property(e => e.Description).HasMaxLength(1000);

            // Convert complex properties to JSON
            entity.Property(e => e.PrimaryKeys)
                .HasConversion(
                    v => string.Join(',', v),
                    v => v.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList());

            entity.Property(e => e.CustomValidationRules)
                .HasConversion(
                    v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                    v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new());

            // Configure relationships
            entity.HasMany<FieldDefinition>()
                .WithOne()
                .HasForeignKey("TableName")
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasMany<ForeignKeyDefinition>()
                .WithOne()
                .HasForeignKey("TableName")
                .OnDelete(DeleteBehavior.Cascade);
        });

        // Configure FieldDefinition
        modelBuilder.Entity<FieldDefinition>(entity =>
        {
            entity.HasKey(e => e.Name); // Simple key for now
            entity.Property(e => e.Name).IsRequired().HasMaxLength(255);
            entity.Property(e => e.DisplayName).IsRequired().HasMaxLength(255);
            entity.Property(e => e.Description).HasMaxLength(1000);
            entity.Property(e => e.DotNetType).IsRequired().HasMaxLength(255);
            entity.Property(e => e.SqlDataType).IsRequired().HasMaxLength(255);

            // Add shadow property for table name
            entity.Property<string>("TableName").IsRequired().HasMaxLength(255);

            // Convert complex properties to JSON
            entity.Property(e => e.DefaultValue)
                .HasConversion(
                    v => v != null ? System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null) : null,
                    v => v != null ? System.Text.Json.JsonSerializer.Deserialize<object>(v, (System.Text.Json.JsonSerializerOptions?)null) : null);

            entity.OwnsOne(e => e.ForeignKeyReference, fk =>
            {
                fk.Property(f => f.ReferencedTable).HasMaxLength(255);
                fk.Property(f => f.ReferencedField).HasMaxLength(255);
                fk.Property(f => f.DisplayField).HasMaxLength(255);
            });

            entity.OwnsOne(e => e.UIConfiguration, ui =>
            {
                ui.Property(u => u.CssClasses).HasMaxLength(500);
                ui.Property(u => u.Placeholder).HasMaxLength(255);
                ui.Property(u => u.HelpText).HasMaxLength(1000);
            });
        });

        // Configure ForeignKeyDefinition
        modelBuilder.Entity<ForeignKeyDefinition>(entity =>
        {
            entity.HasKey(e => e.Name);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(255);
            entity.Property(e => e.ReferencedTable).IsRequired().HasMaxLength(255);

            // Add shadow property for table name
            entity.Property<string>("TableName").IsRequired().HasMaxLength(255);

            // Convert arrays to JSON
            entity.Property(e => e.LocalFields)
                .HasConversion(
                    v => string.Join(',', v),
                    v => v.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList());

            entity.Property(e => e.ReferencedFields)
                .HasConversion(
                    v => string.Join(',', v),
                    v => v.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList());
        });

        // Configure TableRelationship
        modelBuilder.Entity<TableRelationship>(entity =>
        {
            entity.HasKey(e => e.Name);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(255);
            entity.Property(e => e.ParentTable).IsRequired().HasMaxLength(255);
            entity.Property(e => e.ChildTable).IsRequired().HasMaxLength(255);
            entity.Property(e => e.DisplayName).IsRequired().HasMaxLength(255);

            // Convert arrays to JSON
            entity.Property(e => e.ParentFields)
                .HasConversion(
                    v => string.Join(',', v),
                    v => v.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList());

            entity.Property(e => e.ChildFields)
                .HasConversion(
                    v => string.Join(',', v),
                    v => v.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList());

            // Indexes
            entity.HasIndex(e => e.ParentTable);
            entity.HasIndex(e => e.ChildTable);
        });
    }

    /// <summary>
    /// Save changes with audit logging
    /// </summary>
    /// <returns>Number of affected records</returns>
    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // Add audit entries for changes
        var auditEntries = new List<AuditEntry>();
        
        foreach (var entry in ChangeTracker.Entries())
        {
            if (entry.Entity is AuditEntry)
                continue; // Don't audit the audit entries themselves

            var auditEntry = CreateAuditEntry(entry);
            if (auditEntry != null)
            {
                auditEntries.Add(auditEntry);
            }
        }

        var result = await base.SaveChangesAsync(cancellationToken);

        // Add audit entries after successful save
        if (auditEntries.Any())
        {
            AuditEntries.AddRange(auditEntries);
            await base.SaveChangesAsync(cancellationToken);
        }

        return result;
    }

    private AuditEntry? CreateAuditEntry(Microsoft.EntityFrameworkCore.ChangeTracking.EntityEntry entry)
    {
        var entityName = entry.Entity.GetType().Name;
        var primaryKey = GetPrimaryKeyValue(entry);

        if (string.IsNullOrEmpty(primaryKey))
            return null;

        var operation = entry.State switch
        {
            EntityState.Added => AuditOperation.Create,
            EntityState.Modified => AuditOperation.Update,
            EntityState.Deleted => AuditOperation.Delete,
            _ => (AuditOperation?)null
        };

        if (operation == null)
            return null;

        return new AuditEntry
        {
            TableName = entityName,
            RecordId = primaryKey,
            Operation = operation.Value,
            Timestamp = DateTime.UtcNow,
            OldValues = entry.State == EntityState.Modified || entry.State == EntityState.Deleted 
                ? GetEntityValues(entry, true) : null,
            NewValues = entry.State == EntityState.Added || entry.State == EntityState.Modified 
                ? GetEntityValues(entry, false) : null
        };
    }

    private string GetPrimaryKeyValue(Microsoft.EntityFrameworkCore.ChangeTracking.EntityEntry entry)
    {
        var keyValues = new List<string>();
        
        foreach (var property in entry.Properties.Where(p => p.Metadata.IsPrimaryKey()))
        {
            var value = property.CurrentValue?.ToString() ?? "";
            keyValues.Add(value);
        }

        return string.Join(",", keyValues);
    }

    private string? GetEntityValues(Microsoft.EntityFrameworkCore.ChangeTracking.EntityEntry entry, bool original)
    {
        var values = new Dictionary<string, object?>();

        foreach (var property in entry.Properties)
        {
            var value = original ? property.OriginalValue : property.CurrentValue;
            values[property.Metadata.Name] = value;
        }

        return System.Text.Json.JsonSerializer.Serialize(values);
    }
}
