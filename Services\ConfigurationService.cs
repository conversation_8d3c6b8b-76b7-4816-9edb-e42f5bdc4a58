using Crudible.Configuration;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace Crudible.Services;

/// <summary>
/// Service for managing application configuration
/// </summary>
public class ConfigurationService
{
    private readonly IOptionsMonitor<CrudibleConfiguration> _configuration;
    private readonly ILogger<ConfigurationService> _logger;
    private readonly string _configurationPath;

    public ConfigurationService(
        IOptionsMonitor<CrudibleConfiguration> configuration,
        ILogger<ConfigurationService> logger,
        IWebHostEnvironment environment)
    {
        _configuration = configuration;
        _logger = logger;
        _configurationPath = Path.Combine(environment.ContentRootPath, "crudible-config.json");
    }

    /// <summary>
    /// Get current configuration
    /// </summary>
    /// <returns>Current configuration</returns>
    public CrudibleConfiguration GetConfiguration()
    {
        return _configuration.CurrentValue;
    }

    /// <summary>
    /// Update configuration
    /// </summary>
    /// <param name="configuration">New configuration</param>
    /// <returns>Success status</returns>
    public async Task<bool> UpdateConfigurationAsync(CrudibleConfiguration configuration)
    {
        try
        {
            var json = JsonConvert.SerializeObject(configuration, Formatting.Indented);
            await File.WriteAllTextAsync(_configurationPath, json);
            
            _logger.LogInformation("Configuration updated successfully");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update configuration");
            return false;
        }
    }

    /// <summary>
    /// Load configuration from file
    /// </summary>
    /// <returns>Configuration or default if file doesn't exist</returns>
    public async Task<CrudibleConfiguration> LoadConfigurationAsync()
    {
        try
        {
            if (!File.Exists(_configurationPath))
            {
                var defaultConfig = new CrudibleConfiguration();
                await UpdateConfigurationAsync(defaultConfig);
                return defaultConfig;
            }

            var json = await File.ReadAllTextAsync(_configurationPath);
            var configuration = JsonConvert.DeserializeObject<CrudibleConfiguration>(json);
            return configuration ?? new CrudibleConfiguration();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load configuration, using defaults");
            return new CrudibleConfiguration();
        }
    }

    /// <summary>
    /// Update database configuration
    /// </summary>
    /// <param name="databaseConfig">Database configuration</param>
    /// <returns>Success status</returns>
    public async Task<bool> UpdateDatabaseConfigurationAsync(DatabaseConfiguration databaseConfig)
    {
        try
        {
            var config = await LoadConfigurationAsync();
            config.Database = databaseConfig;
            return await UpdateConfigurationAsync(config);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update database configuration");
            return false;
        }
    }

    /// <summary>
    /// Update schema configuration
    /// </summary>
    /// <param name="schemaConfig">Schema configuration</param>
    /// <returns>Success status</returns>
    public async Task<bool> UpdateSchemaConfigurationAsync(SchemaConfiguration schemaConfig)
    {
        try
        {
            var config = await LoadConfigurationAsync();
            config.Schema = schemaConfig;
            return await UpdateConfigurationAsync(config);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update schema configuration");
            return false;
        }
    }

    /// <summary>
    /// Update UI configuration
    /// </summary>
    /// <param name="uiConfig">UI configuration</param>
    /// <returns>Success status</returns>
    public async Task<bool> UpdateUIConfigurationAsync(UIConfiguration uiConfig)
    {
        try
        {
            var config = await LoadConfigurationAsync();
            config.UI = uiConfig;
            return await UpdateConfigurationAsync(config);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update UI configuration");
            return false;
        }
    }

    /// <summary>
    /// Update export configuration
    /// </summary>
    /// <param name="exportConfig">Export configuration</param>
    /// <returns>Success status</returns>
    public async Task<bool> UpdateExportConfigurationAsync(ExportConfiguration exportConfig)
    {
        try
        {
            var config = await LoadConfigurationAsync();
            config.Export = exportConfig;
            return await UpdateConfigurationAsync(config);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update export configuration");
            return false;
        }
    }

    /// <summary>
    /// Update audit configuration
    /// </summary>
    /// <param name="auditConfig">Audit configuration</param>
    /// <returns>Success status</returns>
    public async Task<bool> UpdateAuditConfigurationAsync(AuditConfiguration auditConfig)
    {
        try
        {
            var config = await LoadConfigurationAsync();
            config.Audit = auditConfig;
            return await UpdateConfigurationAsync(config);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update audit configuration");
            return false;
        }
    }

    /// <summary>
    /// Validate configuration
    /// </summary>
    /// <param name="configuration">Configuration to validate</param>
    /// <returns>Validation result</returns>
    public Models.ValidationResult ValidateConfiguration(CrudibleConfiguration configuration)
    {
        var result = new Models.ValidationResult();

        // Validate database configuration
        if (string.IsNullOrWhiteSpace(configuration.Database.ConnectionString))
        {
            result.AddError("Database.ConnectionString", "Connection string is required");
        }

        // Validate schema configuration
        if (string.IsNullOrWhiteSpace(configuration.Schema.XmlSchema) && 
            string.IsNullOrWhiteSpace(configuration.Database.ConnectionString))
        {
            result.AddError("Schema", "Either XML schema or database connection string must be provided");
        }

        // Validate UI configuration
        if (configuration.UI.ItemsPerPage <= 0)
        {
            result.AddError("UI.ItemsPerPage", "Items per page must be greater than 0");
        }

        // Validate export configuration
        if (configuration.Export.MaxExportRecords <= 0)
        {
            result.AddError("Export.MaxExportRecords", "Max export records must be greater than 0");
        }

        // Validate audit configuration
        if (configuration.Audit.RetentionDays < 0)
        {
            result.AddError("Audit.RetentionDays", "Retention days cannot be negative");
        }

        return result;
    }

    /// <summary>
    /// Reset configuration to defaults
    /// </summary>
    /// <returns>Success status</returns>
    public async Task<bool> ResetToDefaultsAsync()
    {
        try
        {
            var defaultConfig = new CrudibleConfiguration();
            return await UpdateConfigurationAsync(defaultConfig);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to reset configuration to defaults");
            return false;
        }
    }

    /// <summary>
    /// Export configuration to JSON
    /// </summary>
    /// <returns>Configuration as JSON string</returns>
    public async Task<string> ExportConfigurationAsync()
    {
        try
        {
            var config = await LoadConfigurationAsync();
            return JsonConvert.SerializeObject(config, Formatting.Indented);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to export configuration");
            return string.Empty;
        }
    }

    /// <summary>
    /// Import configuration from JSON
    /// </summary>
    /// <param name="json">Configuration JSON</param>
    /// <returns>Success status</returns>
    public async Task<bool> ImportConfigurationAsync(string json)
    {
        try
        {
            var configuration = JsonConvert.DeserializeObject<CrudibleConfiguration>(json);
            if (configuration == null)
            {
                _logger.LogError("Failed to deserialize configuration JSON");
                return false;
            }

            var validationResult = ValidateConfiguration(configuration);
            if (!validationResult.IsValid)
            {
                _logger.LogError("Configuration validation failed: {Errors}", 
                    string.Join(", ", validationResult.Errors.Select(e => e.ErrorMessage)));
                return false;
            }

            return await UpdateConfigurationAsync(configuration);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to import configuration");
            return false;
        }
    }
}
