using System.ComponentModel.DataAnnotations;

namespace Crudible.Models;

/// <summary>
/// Represents an audit log entry
/// </summary>
public class AuditEntry
{
    /// <summary>
    /// Unique identifier for the audit entry
    /// </summary>
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// Table name that was affected
    /// </summary>
    [Required]
    [MaxLength(255)]
    public string TableName { get; set; } = string.Empty;

    /// <summary>
    /// Record ID that was affected
    /// </summary>
    [Required]
    [MaxLength(255)]
    public string RecordId { get; set; } = string.Empty;

    /// <summary>
    /// Type of operation performed
    /// </summary>
    public AuditOperation Operation { get; set; }

    /// <summary>
    /// User who performed the operation
    /// </summary>
    [MaxLength(255)]
    public string? UserId { get; set; }

    /// <summary>
    /// Username who performed the operation
    /// </summary>
    [MaxLength(255)]
    public string? UserName { get; set; }

    /// <summary>
    /// Timestamp when the operation occurred
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// IP address of the user
    /// </summary>
    [MaxLength(45)]
    public string? IpAddress { get; set; }

    /// <summary>
    /// User agent string
    /// </summary>
    [MaxLength(500)]
    public string? UserAgent { get; set; }

    /// <summary>
    /// Old values (JSON format)
    /// </summary>
    public string? OldValues { get; set; }

    /// <summary>
    /// New values (JSON format)
    /// </summary>
    public string? NewValues { get; set; }

    /// <summary>
    /// Changed fields
    /// </summary>
    public string? ChangedFields { get; set; }

    /// <summary>
    /// Additional metadata
    /// </summary>
    public string? Metadata { get; set; }

    /// <summary>
    /// Success status of the operation
    /// </summary>
    public bool Success { get; set; } = true;

    /// <summary>
    /// Error message if operation failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Duration of the operation in milliseconds
    /// </summary>
    public long? DurationMs { get; set; }
}

/// <summary>
/// Audit operation types
/// </summary>
public enum AuditOperation
{
    Create,
    Read,
    Update,
    Delete,
    Export,
    Import,
    Login,
    Logout,
    ConfigurationChange
}

/// <summary>
/// Audit summary for reporting
/// </summary>
public class AuditSummary
{
    /// <summary>
    /// Table name
    /// </summary>
    public string TableName { get; set; } = string.Empty;

    /// <summary>
    /// Total number of operations
    /// </summary>
    public int TotalOperations { get; set; }

    /// <summary>
    /// Number of create operations
    /// </summary>
    public int CreateOperations { get; set; }

    /// <summary>
    /// Number of read operations
    /// </summary>
    public int ReadOperations { get; set; }

    /// <summary>
    /// Number of update operations
    /// </summary>
    public int UpdateOperations { get; set; }

    /// <summary>
    /// Number of delete operations
    /// </summary>
    public int DeleteOperations { get; set; }

    /// <summary>
    /// Number of failed operations
    /// </summary>
    public int FailedOperations { get; set; }

    /// <summary>
    /// Date range start
    /// </summary>
    public DateTime StartDate { get; set; }

    /// <summary>
    /// Date range end
    /// </summary>
    public DateTime EndDate { get; set; }

    /// <summary>
    /// Most active users
    /// </summary>
    public List<UserActivity> TopUsers { get; set; } = new();
}

/// <summary>
/// User activity summary
/// </summary>
public class UserActivity
{
    /// <summary>
    /// User ID
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// Username
    /// </summary>
    public string? UserName { get; set; }

    /// <summary>
    /// Number of operations
    /// </summary>
    public int OperationCount { get; set; }

    /// <summary>
    /// Last activity timestamp
    /// </summary>
    public DateTime LastActivity { get; set; }
}

/// <summary>
/// Audit filter for querying audit entries
/// </summary>
public class AuditFilter
{
    /// <summary>
    /// Table name filter
    /// </summary>
    public string? TableName { get; set; }

    /// <summary>
    /// Record ID filter
    /// </summary>
    public string? RecordId { get; set; }

    /// <summary>
    /// User ID filter
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// Operation type filter
    /// </summary>
    public AuditOperation? Operation { get; set; }

    /// <summary>
    /// Start date filter
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// End date filter
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// Success status filter
    /// </summary>
    public bool? Success { get; set; }

    /// <summary>
    /// Page number for pagination
    /// </summary>
    public int Page { get; set; } = 1;

    /// <summary>
    /// Page size for pagination
    /// </summary>
    public int PageSize { get; set; } = 25;

    /// <summary>
    /// Sort field
    /// </summary>
    public string SortField { get; set; } = nameof(AuditEntry.Timestamp);

    /// <summary>
    /// Sort direction
    /// </summary>
    public SortDirection SortDirection { get; set; } = SortDirection.Descending;
}
