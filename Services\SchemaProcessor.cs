using Crudible.Models;
using Crudible.Services.Interfaces;

namespace Crudible.Services;

/// <summary>
/// Main schema processor service that coordinates XML and database schema processing
/// </summary>
public class SchemaProcessor : ISchemaProcessor
{
    private readonly XmlSchemaParser _xmlParser;
    private readonly DatabaseSchemaExtractor _dbExtractor;
    private readonly ILogger<SchemaProcessor> _logger;

    public SchemaProcessor(
        XmlSchemaParser xmlParser,
        DatabaseSchemaExtractor dbExtractor,
        ILogger<SchemaProcessor> logger)
    {
        _xmlParser = xmlParser;
        _dbExtractor = dbExtractor;
        _logger = logger;
    }

    /// <summary>
    /// Process XML schema and extract table definitions
    /// </summary>
    /// <param name="xmlSchema">XML schema content</param>
    /// <returns>List of table definitions</returns>
    public async Task<List<TableDefinition>> ProcessXmlSchemaAsync(string xmlSchema)
    {
        try
        {
            _logger.LogInformation("Processing XML schema");
            
            var tables = await _xmlParser.ParseSchemaAsync(xmlSchema);
            
            // Post-process tables
            foreach (var table in tables)
            {
                PostProcessTable(table);
            }

            _logger.LogInformation("Successfully processed {TableCount} tables from XML schema", tables.Count);
            return tables;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process XML schema");
            throw;
        }
    }

    /// <summary>
    /// Process database connection string and extract table definitions
    /// </summary>
    /// <param name="connectionString">Database connection string</param>
    /// <returns>List of table definitions</returns>
    public async Task<List<TableDefinition>> ProcessConnectionStringAsync(string connectionString)
    {
        try
        {
            _logger.LogInformation("Processing database connection string");
            
            // Test connection first
            var (success, message) = await _dbExtractor.TestConnectionAsync(connectionString);
            if (!success)
            {
                throw new InvalidOperationException($"Database connection failed: {message}");
            }

            var tables = await _dbExtractor.ExtractSchemaAsync(connectionString);
            
            // Post-process tables
            foreach (var table in tables)
            {
                PostProcessTable(table);
            }

            _logger.LogInformation("Successfully processed {TableCount} tables from database", tables.Count);
            return tables;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process database connection string");
            throw;
        }
    }

    /// <summary>
    /// Validate schema format
    /// </summary>
    /// <param name="schema">Schema content</param>
    /// <param name="schemaType">Type of schema (XML or ConnectionString)</param>
    /// <returns>Validation result</returns>
    public async Task<SchemaValidationResult> ValidateSchemaAsync(string schema, SchemaType schemaType)
    {
        try
        {
            _logger.LogInformation("Validating {SchemaType} schema", schemaType);

            var result = schemaType switch
            {
                SchemaType.Xml => _xmlParser.ValidateSchema(schema),
                SchemaType.ConnectionString => await ValidateConnectionStringAsync(schema),
                _ => throw new ArgumentException($"Unsupported schema type: {schemaType}")
            };

            _logger.LogInformation("Schema validation completed. Valid: {IsValid}, Errors: {ErrorCount}, Warnings: {WarningCount}",
                result.IsValid, result.Errors.Count, result.Warnings.Count);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Schema validation failed");
            
            return new SchemaValidationResult
            {
                IsValid = false,
                Errors = { $"Validation error: {ex.Message}" }
            };
        }
    }

    /// <summary>
    /// Extract relationships between tables
    /// </summary>
    /// <param name="tables">List of table definitions</param>
    /// <returns>List of relationships</returns>
    public async Task<List<TableRelationship>> ExtractRelationshipsAsync(List<TableDefinition> tables)
    {
        try
        {
            _logger.LogInformation("Extracting relationships from {TableCount} tables", tables.Count);

            var relationships = new List<TableRelationship>();

            // Extract relationships from foreign keys
            foreach (var table in tables)
            {
                foreach (var foreignKey in table.ForeignKeys)
                {
                    var relationship = CreateRelationshipFromForeignKey(table, foreignKey, tables);
                    if (relationship != null)
                    {
                        relationships.Add(relationship);
                    }
                }

                // Extract relationships from field-level foreign key references
                foreach (var field in table.Fields.Where(f => f.ForeignKeyReference != null))
                {
                    var relationship = CreateRelationshipFromFieldReference(table, field, tables);
                    if (relationship != null)
                    {
                        relationships.Add(relationship);
                    }
                }
            }

            // Remove duplicates
            relationships = relationships
                .GroupBy(r => new { r.ParentTable, r.ChildTable, ParentFields = string.Join(",", r.ParentFields), ChildFields = string.Join(",", r.ChildFields) })
                .Select(g => g.First())
                .ToList();

            _logger.LogInformation("Extracted {RelationshipCount} relationships", relationships.Count);
            return relationships;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to extract relationships");
            throw;
        }
    }

    private async Task<SchemaValidationResult> ValidateConnectionStringAsync(string connectionString)
    {
        var result = new SchemaValidationResult();

        try
        {
            var (success, message) = await _dbExtractor.TestConnectionAsync(connectionString);

            if (!success)
            {
                result.Errors.Add($"Database connection test failed: {message}");
                return result;
            }

            // Try to extract a sample of tables to validate the connection works
            var tables = await _dbExtractor.ExtractSchemaAsync(connectionString);
            result.TableCount = tables.Count;

            if (tables.Count == 0)
            {
                result.Warnings.Add("No tables found in the database");
            }

            result.IsValid = !result.Errors.Any();
        }
        catch (Exception ex)
        {
            result.Errors.Add($"Connection validation error: {ex.Message}");
        }

        return result;
    }

    private void PostProcessTable(TableDefinition table)
    {
        // Set primary key flags on fields
        foreach (var pkField in table.PrimaryKeys)
        {
            var field = table.Fields.FirstOrDefault(f => f.Name == pkField);
            if (field != null)
            {
                field.IsPrimaryKey = true;
            }
        }

        // Configure UI settings
        ConfigureTableUI(table);
        
        // Configure field UI settings
        foreach (var field in table.Fields)
        {
            ConfigureFieldUI(field);
        }

        // Set display names if not already set
        if (string.IsNullOrEmpty(table.DisplayName))
        {
            table.DisplayName = FormatDisplayName(table.Name);
        }

        foreach (var field in table.Fields)
        {
            if (string.IsNullOrEmpty(field.DisplayName))
            {
                field.DisplayName = FormatDisplayName(field.Name);
            }
        }
    }

    private void ConfigureTableUI(TableDefinition table)
    {
        // Set default list view fields (first 5 non-binary fields)
        table.UIConfiguration.ListViewFields = table.Fields
            .Where(f => f.DataType != FieldDataType.Binary && f.DataType != FieldDataType.Json)
            .Take(5)
            .Select(f => f.Name)
            .ToList();

        // Set default sort field (first primary key or first field)
        table.UIConfiguration.DefaultSortField = table.PrimaryKeys.FirstOrDefault() 
            ?? table.Fields.FirstOrDefault()?.Name;

        // Set icon based on table name
        table.UIConfiguration.Icon = GetTableIcon(table.Name);
    }

    private void ConfigureFieldUI(FieldDefinition field)
    {
        // Set input type based on data type
        field.UIConfiguration.InputType = field.DataType switch
        {
            FieldDataType.Boolean => InputType.Checkbox,
            FieldDataType.Date => InputType.Date,
            FieldDataType.DateTime => InputType.DateTime,
            FieldDataType.Time => InputType.Time,
            FieldDataType.Integer or FieldDataType.Long or FieldDataType.Decimal or FieldDataType.Double => InputType.Number,
            FieldDataType.Binary => InputType.File,
            _ => field.MaxLength > 255 ? InputType.Textarea : InputType.Text
        };

        // Configure visibility
        field.UIConfiguration.ShowInList = field.DataType != FieldDataType.Binary && 
                                          field.DataType != FieldDataType.Json &&
                                          (field.MaxLength ?? 0) <= 100;

        field.UIConfiguration.ShowInCreate = !field.IsIdentity;
        field.UIConfiguration.ShowInEdit = !field.IsIdentity && !field.IsPrimaryKey;

        // Set order based on field importance
        field.UIConfiguration.Order = GetFieldOrder(field);
    }

    private TableRelationship? CreateRelationshipFromForeignKey(TableDefinition table, ForeignKeyDefinition foreignKey, List<TableDefinition> allTables)
    {
        var referencedTable = allTables.FirstOrDefault(t => t.Name == foreignKey.ReferencedTable);
        if (referencedTable == null)
            return null;

        return new TableRelationship
        {
            Name = foreignKey.Name,
            ParentTable = foreignKey.ReferencedTable,
            ChildTable = table.Name,
            ParentFields = foreignKey.ReferencedFields,
            ChildFields = foreignKey.LocalFields,
            RelationshipType = RelationshipType.OneToMany,
            DisplayName = $"{referencedTable.DisplayName} → {table.DisplayName}"
        };
    }

    private TableRelationship? CreateRelationshipFromFieldReference(TableDefinition table, FieldDefinition field, List<TableDefinition> allTables)
    {
        if (field.ForeignKeyReference == null)
            return null;

        var referencedTable = allTables.FirstOrDefault(t => t.Name == field.ForeignKeyReference.ReferencedTable);
        if (referencedTable == null)
            return null;

        return new TableRelationship
        {
            Name = $"FK_{table.Name}_{field.Name}",
            ParentTable = field.ForeignKeyReference.ReferencedTable,
            ChildTable = table.Name,
            ParentFields = { field.ForeignKeyReference.ReferencedField },
            ChildFields = { field.Name },
            RelationshipType = RelationshipType.ManyToOne,
            DisplayName = $"{referencedTable.DisplayName} → {table.DisplayName}"
        };
    }

    private string FormatDisplayName(string name)
    {
        // Convert PascalCase or snake_case to display name
        if (name.Contains('_'))
        {
            return string.Join(" ", name.Split('_').Select(word => 
                char.ToUpper(word[0]) + word.Substring(1).ToLower()));
        }

        // Insert spaces before capital letters in PascalCase
        var result = "";
        for (int i = 0; i < name.Length; i++)
        {
            if (i > 0 && char.IsUpper(name[i]))
                result += " ";
            result += name[i];
        }

        return result;
    }

    private string GetTableIcon(string tableName)
    {
        var lowerName = tableName.ToLower();
        
        return lowerName switch
        {
            var name when name.Contains("user") || name.Contains("person") || name.Contains("customer") => "fas fa-users",
            var name when name.Contains("order") || name.Contains("sale") => "fas fa-shopping-cart",
            var name when name.Contains("product") || name.Contains("item") => "fas fa-box",
            var name when name.Contains("category") => "fas fa-tags",
            var name when name.Contains("log") || name.Contains("audit") => "fas fa-history",
            var name when name.Contains("config") || name.Contains("setting") => "fas fa-cog",
            var name when name.Contains("report") => "fas fa-chart-bar",
            _ => "fas fa-table"
        };
    }

    private int GetFieldOrder(FieldDefinition field)
    {
        if (field.IsPrimaryKey) return 1;
        if (field.Name.ToLower().Contains("name")) return 2;
        if (field.Name.ToLower().Contains("title")) return 3;
        if (field.Name.ToLower().Contains("description")) return 100;
        if (field.DataType == FieldDataType.DateTime) return 90;
        if (field.ForeignKeyReference != null) return 10;
        
        return 50;
    }
}
