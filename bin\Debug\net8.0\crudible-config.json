{"Database": {"ConnectionString": null, "Provider": 0, "CommandTimeout": 30, "EnablePooling": true}, "Schema": {"XmlSchema": null, "IncludedTables": [], "ExcludedTables": [], "ValidationRules": {}, "AutoDetectRelationships": true}, "UI": {"ApplicationTitle": "Crudible - Enterprise CRUD Generator", "ItemsPerPage": 25, "EnableResponsiveDesign": true, "BootstrapTheme": "default", "ShowAuditInfo": true}, "Export": {"EnablePdfExport": true, "EnableExcelExport": true, "MaxExportRecords": 10000, "FileNameTemplate": "{TableName}_{DateTime:yyyyMMdd_HHmmss}"}, "Audit": {"EnableAuditTrail": true, "AuditTableName": "AuditLog", "RetentionDays": 365, "LogReadOperations": false, "LogCreateOperations": true, "LogUpdateOperations": true, "LogDeleteOperations": true}}