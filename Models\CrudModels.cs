using System.ComponentModel.DataAnnotations;

namespace Crudible.Models;

/// <summary>
/// Represents a complete set of CRUD pages for a table
/// </summary>
public class CrudPageSet
{
    /// <summary>
    /// Table definition
    /// </summary>
    public TableDefinition TableDefinition { get; set; } = new();

    /// <summary>
    /// List page content
    /// </summary>
    public string ListPageContent { get; set; } = string.Empty;

    /// <summary>
    /// Create page content
    /// </summary>
    public string CreatePageContent { get; set; } = string.Empty;

    /// <summary>
    /// Edit page content
    /// </summary>
    public string EditPageContent { get; set; } = string.Empty;

    /// <summary>
    /// Details page content
    /// </summary>
    public string DetailsPageContent { get; set; } = string.Empty;

    /// <summary>
    /// Delete page content
    /// </summary>
    public string DeletePageContent { get; set; } = string.Empty;

    /// <summary>
    /// Page model class content
    /// </summary>
    public string PageModelContent { get; set; } = string.Empty;

    /// <summary>
    /// Entity model class content
    /// </summary>
    public string EntityModelContent { get; set; } = string.Empty;

    /// <summary>
    /// Generation timestamp
    /// </summary>
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Schema validation result
/// </summary>
public class SchemaValidationResult
{
    /// <summary>
    /// Whether the schema is valid
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// Validation errors
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// Validation warnings
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// Number of tables found
    /// </summary>
    public int TableCount { get; set; }

    /// <summary>
    /// Number of relationships found
    /// </summary>
    public int RelationshipCount { get; set; }
}

/// <summary>
/// Table relationship definition
/// </summary>
public class TableRelationship
{
    /// <summary>
    /// Relationship name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Parent table name
    /// </summary>
    public string ParentTable { get; set; } = string.Empty;

    /// <summary>
    /// Child table name
    /// </summary>
    public string ChildTable { get; set; } = string.Empty;

    /// <summary>
    /// Parent field names
    /// </summary>
    public List<string> ParentFields { get; set; } = new();

    /// <summary>
    /// Child field names
    /// </summary>
    public List<string> ChildFields { get; set; } = new();

    /// <summary>
    /// Relationship type
    /// </summary>
    public RelationshipType RelationshipType { get; set; }

    /// <summary>
    /// Whether to show child records in parent details
    /// </summary>
    public bool ShowInParentDetails { get; set; } = true;

    /// <summary>
    /// Display name for the relationship
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;
}

/// <summary>
/// Validation result for entities
/// </summary>
public class ValidationResult
{
    /// <summary>
    /// Whether validation passed
    /// </summary>
    public bool IsValid { get; set; } = true;

    /// <summary>
    /// Validation errors
    /// </summary>
    public List<ValidationError> Errors { get; set; } = new();

    /// <summary>
    /// Add an error
    /// </summary>
    /// <param name="fieldName">Field name</param>
    /// <param name="errorMessage">Error message</param>
    public void AddError(string fieldName, string errorMessage)
    {
        Errors.Add(new ValidationError { FieldName = fieldName, ErrorMessage = errorMessage });
        IsValid = false;
    }
}

/// <summary>
/// Validation error
/// </summary>
public class ValidationError
{
    /// <summary>
    /// Field name that has the error
    /// </summary>
    public string FieldName { get; set; } = string.Empty;

    /// <summary>
    /// Error message
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// Error code
    /// </summary>
    public string? ErrorCode { get; set; }
}

/// <summary>
/// Paged result for list views
/// </summary>
/// <typeparam name="T">Type of items</typeparam>
public class PagedResult<T>
{
    /// <summary>
    /// Items in the current page
    /// </summary>
    public List<T> Items { get; set; } = new();

    /// <summary>
    /// Total number of items
    /// </summary>
    public int TotalItems { get; set; }

    /// <summary>
    /// Current page number
    /// </summary>
    public int CurrentPage { get; set; }

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Total number of pages
    /// </summary>
    public int TotalPages => (int)Math.Ceiling((double)TotalItems / PageSize);

    /// <summary>
    /// Whether there is a previous page
    /// </summary>
    public bool HasPreviousPage => CurrentPage > 1;

    /// <summary>
    /// Whether there is a next page
    /// </summary>
    public bool HasNextPage => CurrentPage < TotalPages;
}

/// <summary>
/// Filter criteria for list views
/// </summary>
public class ListFilter
{
    /// <summary>
    /// Search term
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Field-specific filters
    /// </summary>
    public Dictionary<string, object> FieldFilters { get; set; } = new();

    /// <summary>
    /// Sort field
    /// </summary>
    public string? SortField { get; set; }

    /// <summary>
    /// Sort direction
    /// </summary>
    public SortDirection SortDirection { get; set; } = SortDirection.Ascending;

    /// <summary>
    /// Page number
    /// </summary>
    public int Page { get; set; } = 1;

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; } = 25;
}

/// <summary>
/// Export format enumeration
/// </summary>
public enum ExportFormat
{
    Excel,
    Csv,
    Json,
    Pdf,
    Xml
}

/// <summary>
/// Export request
/// </summary>
public class ExportRequest
{
    /// <summary>
    /// Table name to export
    /// </summary>
    public string TableName { get; set; } = string.Empty;

    /// <summary>
    /// Export format
    /// </summary>
    public ExportFormat Format { get; set; }

    /// <summary>
    /// Filter criteria
    /// </summary>
    public ListFilter? Filter { get; set; }

    /// <summary>
    /// Fields to include in export
    /// </summary>
    public List<string>? IncludeFields { get; set; }

    /// <summary>
    /// File name
    /// </summary>
    public string? FileName { get; set; }
}

/// <summary>
/// Configuration update request
/// </summary>
public class ConfigurationUpdateRequest
{
    /// <summary>
    /// Configuration section to update
    /// </summary>
    public string Section { get; set; } = string.Empty;

    /// <summary>
    /// Configuration values
    /// </summary>
    public Dictionary<string, object> Values { get; set; } = new();

    /// <summary>
    /// User making the change
    /// </summary>
    public string? UserId { get; set; }
}

/// <summary>
/// Relationship types
/// </summary>
public enum RelationshipType
{
    OneToOne,
    OneToMany,
    ManyToOne,
    ManyToMany
}
