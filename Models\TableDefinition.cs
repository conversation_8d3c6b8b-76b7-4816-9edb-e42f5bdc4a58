using System.ComponentModel.DataAnnotations;

namespace Crudible.Models;

/// <summary>
/// Represents a database table definition
/// </summary>
public class TableDefinition
{
    /// <summary>
    /// Table name
    /// </summary>
    [Required]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Schema name (if applicable)
    /// </summary>
    public string? Schema { get; set; }

    /// <summary>
    /// Display name for UI
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// Table description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// List of field definitions
    /// </summary>
    public List<FieldDefinition> Fields { get; set; } = new();

    /// <summary>
    /// Primary key field names
    /// </summary>
    public List<string> PrimaryKeys { get; set; } = new();

    /// <summary>
    /// Foreign key relationships
    /// </summary>
    public List<ForeignKeyDefinition> ForeignKeys { get; set; } = new();

    /// <summary>
    /// Indexes defined on the table
    /// </summary>
    public List<IndexDefinition> Indexes { get; set; } = new();

    /// <summary>
    /// Whether this table should be included in CRUD generation
    /// </summary>
    public bool IncludeInCrud { get; set; } = true;

    /// <summary>
    /// Custom validation rules for this table
    /// </summary>
    public Dictionary<string, object> CustomValidationRules { get; set; } = new();

    /// <summary>
    /// UI configuration for this table
    /// </summary>
    public TableUIConfiguration UIConfiguration { get; set; } = new();
}

/// <summary>
/// Represents a database field/column definition
/// </summary>
public class FieldDefinition
{
    /// <summary>
    /// Field name
    /// </summary>
    [Required]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Display name for UI
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// Field description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Data type
    /// </summary>
    public FieldDataType DataType { get; set; }

    /// <summary>
    /// .NET type name
    /// </summary>
    public string DotNetType { get; set; } = string.Empty;

    /// <summary>
    /// SQL data type
    /// </summary>
    public string SqlDataType { get; set; } = string.Empty;

    /// <summary>
    /// Maximum length (for string types)
    /// </summary>
    public int? MaxLength { get; set; }

    /// <summary>
    /// Precision (for decimal types)
    /// </summary>
    public int? Precision { get; set; }

    /// <summary>
    /// Scale (for decimal types)
    /// </summary>
    public int? Scale { get; set; }

    /// <summary>
    /// Whether the field allows null values
    /// </summary>
    public bool IsNullable { get; set; }

    /// <summary>
    /// Whether this is a primary key field
    /// </summary>
    public bool IsPrimaryKey { get; set; }

    /// <summary>
    /// Whether this is an identity/auto-increment field
    /// </summary>
    public bool IsIdentity { get; set; }

    /// <summary>
    /// Whether this field is unique
    /// </summary>
    public bool IsUnique { get; set; }

    /// <summary>
    /// Default value
    /// </summary>
    public object? DefaultValue { get; set; }

    /// <summary>
    /// Foreign key reference (if applicable)
    /// </summary>
    public ForeignKeyReference? ForeignKeyReference { get; set; }

    /// <summary>
    /// UI configuration for this field
    /// </summary>
    public FieldUIConfiguration UIConfiguration { get; set; } = new();

    /// <summary>
    /// Validation rules for this field
    /// </summary>
    public List<FieldValidationRule> ValidationRules { get; set; } = new();
}

/// <summary>
/// Validation rule for a field
/// </summary>
public class FieldValidationRule
{
    public string? RegexPattern { get; set; }
    public string? ValidationMessage { get; set; }
    public int? MinLength { get; set; }
    public int? MaxLength { get; set; }
    public decimal? MinValue { get; set; }
    public decimal? MaxValue { get; set; }
    public bool IsRequired { get; set; }
    public string? CustomValidationMethod { get; set; }
}

/// <summary>
/// Foreign key definition
/// </summary>
public class ForeignKeyDefinition
{
    /// <summary>
    /// Foreign key name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Local field names
    /// </summary>
    public List<string> LocalFields { get; set; } = new();

    /// <summary>
    /// Referenced table name
    /// </summary>
    public string ReferencedTable { get; set; } = string.Empty;

    /// <summary>
    /// Referenced field names
    /// </summary>
    public List<string> ReferencedFields { get; set; } = new();

    /// <summary>
    /// Delete action
    /// </summary>
    public ForeignKeyAction OnDelete { get; set; } = ForeignKeyAction.NoAction;

    /// <summary>
    /// Update action
    /// </summary>
    public ForeignKeyAction OnUpdate { get; set; } = ForeignKeyAction.NoAction;
}

/// <summary>
/// Foreign key reference for a field
/// </summary>
public class ForeignKeyReference
{
    /// <summary>
    /// Referenced table name
    /// </summary>
    public string ReferencedTable { get; set; } = string.Empty;

    /// <summary>
    /// Referenced field name
    /// </summary>
    public string ReferencedField { get; set; } = string.Empty;

    /// <summary>
    /// Display field name (for dropdowns)
    /// </summary>
    public string? DisplayField { get; set; }
}

/// <summary>
/// Index definition
/// </summary>
public class IndexDefinition
{
    /// <summary>
    /// Index name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Field names in the index
    /// </summary>
    public List<string> Fields { get; set; } = new();

    /// <summary>
    /// Whether this is a unique index
    /// </summary>
    public bool IsUnique { get; set; }

    /// <summary>
    /// Whether this is a clustered index
    /// </summary>
    public bool IsClustered { get; set; }
}

/// <summary>
/// Table UI configuration
/// </summary>
public class TableUIConfiguration
{
    /// <summary>
    /// Icon for the table
    /// </summary>
    public string? Icon { get; set; }

    /// <summary>
    /// Color theme
    /// </summary>
    public string? ColorTheme { get; set; }

    /// <summary>
    /// Fields to display in list view
    /// </summary>
    public List<string> ListViewFields { get; set; } = new();

    /// <summary>
    /// Default sort field
    /// </summary>
    public string? DefaultSortField { get; set; }

    /// <summary>
    /// Default sort direction
    /// </summary>
    public SortDirection DefaultSortDirection { get; set; } = SortDirection.Ascending;

    /// <summary>
    /// Items per page
    /// </summary>
    public int ItemsPerPage { get; set; } = 25;
}

/// <summary>
/// Field UI configuration
/// </summary>
public class FieldUIConfiguration
{
    /// <summary>
    /// Input type for forms
    /// </summary>
    public InputType InputType { get; set; } = InputType.Text;

    /// <summary>
    /// Whether to show in list view
    /// </summary>
    public bool ShowInList { get; set; } = true;

    /// <summary>
    /// Whether to show in create form
    /// </summary>
    public bool ShowInCreate { get; set; } = true;

    /// <summary>
    /// Whether to show in edit form
    /// </summary>
    public bool ShowInEdit { get; set; } = true;

    /// <summary>
    /// Whether to show in details view
    /// </summary>
    public bool ShowInDetails { get; set; } = true;

    /// <summary>
    /// Field order in forms
    /// </summary>
    public int Order { get; set; }

    /// <summary>
    /// CSS classes for styling
    /// </summary>
    public string? CssClasses { get; set; }

    /// <summary>
    /// Placeholder text
    /// </summary>
    public string? Placeholder { get; set; }

    /// <summary>
    /// Help text
    /// </summary>
    public string? HelpText { get; set; }
}

/// <summary>
/// Field data types
/// </summary>
public enum FieldDataType
{
    String,
    Integer,
    Long,
    Decimal,
    Double,
    Boolean,
    DateTime,
    Date,
    Time,
    Guid,
    Binary,
    Json,
    Xml
}

/// <summary>
/// Foreign key actions
/// </summary>
public enum ForeignKeyAction
{
    NoAction,
    Cascade,
    SetNull,
    SetDefault,
    Restrict
}

/// <summary>
/// Sort directions
/// </summary>
public enum SortDirection
{
    Ascending,
    Descending
}

/// <summary>
/// Input types for UI
/// </summary>
public enum InputType
{
    Text,
    Password,
    Email,
    Number,
    Date,
    DateTime,
    Time,
    Checkbox,
    Radio,
    Select,
    Textarea,
    File,
    Hidden,
    Color,
    Range,
    Url,
    Tel
}
