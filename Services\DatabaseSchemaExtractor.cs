using System.Data;
using Microsoft.Data.SqlClient;
using Crudible.Models;
using Microsoft.EntityFrameworkCore;

namespace Crudible.Services;

/// <summary>
/// Service for extracting schema information from database connections
/// </summary>
public class DatabaseSchemaExtractor
{
    private readonly ILogger<DatabaseSchemaExtractor> _logger;

    public DatabaseSchemaExtractor(ILogger<DatabaseSchemaExtractor> logger)
    {
        _logger = logger;
    }



    /// <summary>
    /// Extract table definitions from database connection string
    /// </summary>
    /// <param name="connectionString">Database connection string</param>
    /// <returns>List of table definitions</returns>
    public async Task<List<TableDefinition>> ExtractSchemaAsync(string connectionString)
    {
        try
        {
            using var connection = new SqlConnection(connectionString);
            await connection.OpenAsync();

            var tables = await GetTablesAsync(connection);
            
            foreach (var table in tables)
            {
                table.Fields = await GetTableFieldsAsync(connection, table.Name, table.Schema);
                table.PrimaryKeys = await GetPrimaryKeysAsync(connection, table.Name, table.Schema);
                table.ForeignKeys = await GetForeignKeysAsync(connection, table.Name, table.Schema);
                table.Indexes = await GetIndexesAsync(connection, table.Name, table.Schema);
            }

            _logger.LogInformation("Successfully extracted {TableCount} tables from database", tables.Count);
            return tables;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to extract schema from database");
            throw;
        }
    }

    /// <summary>
    /// Test database connection
    /// </summary>
    /// <param name="connectionString">Connection string to test</param>
    /// <returns>Connection test result</returns>
    public async Task<(bool Success, string Message)> TestConnectionAsync(string connectionString)
    {
        try
        {
            using var connection = new SqlConnection(connectionString);
            await connection.OpenAsync();
            
            var version = connection.ServerVersion;
            return (true, $"Connection successful. Server version: {version}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Database connection test failed");
            return (false, ex.Message);
        }
    }

    private async Task<List<TableDefinition>> GetTablesAsync(SqlConnection connection)
    {
        const string sql = @"
            SELECT 
                t.TABLE_SCHEMA as SchemaName,
                t.TABLE_NAME as TableName,
                ISNULL(ep.value, '') as Description
            FROM INFORMATION_SCHEMA.TABLES t
            LEFT JOIN sys.tables st ON st.name = t.TABLE_NAME
            LEFT JOIN sys.extended_properties ep ON ep.major_id = st.object_id 
                AND ep.minor_id = 0 AND ep.name = 'MS_Description'
            WHERE t.TABLE_TYPE = 'BASE TABLE'
            ORDER BY t.TABLE_SCHEMA, t.TABLE_NAME";

        var tables = new List<TableDefinition>();

        using var command = new SqlCommand(sql, connection);
        using var reader = await command.ExecuteReaderAsync();

        while (await reader.ReadAsync())
        {
            var table = new TableDefinition
            {
                Schema = reader.GetString("SchemaName"),
                Name = reader.GetString("TableName"),
                Description = reader.IsDBNull("Description") ? null : reader.GetString("Description")
            };

            table.DisplayName = FormatDisplayName(table.Name);
            tables.Add(table);
        }

        return tables;
    }

    private async Task<List<FieldDefinition>> GetTableFieldsAsync(SqlConnection connection, string tableName, string? schemaName)
    {
        const string sql = @"
            SELECT 
                c.COLUMN_NAME as ColumnName,
                c.DATA_TYPE as DataType,
                c.IS_NULLABLE as IsNullable,
                c.CHARACTER_MAXIMUM_LENGTH as MaxLength,
                c.NUMERIC_PRECISION as Precision,
                c.NUMERIC_SCALE as Scale,
                c.COLUMN_DEFAULT as DefaultValue,
                COLUMNPROPERTY(OBJECT_ID(c.TABLE_SCHEMA + '.' + c.TABLE_NAME), c.COLUMN_NAME, 'IsIdentity') as IsIdentity,
                ISNULL(ep.value, '') as Description
            FROM INFORMATION_SCHEMA.COLUMNS c
            LEFT JOIN sys.columns sc ON sc.object_id = OBJECT_ID(c.TABLE_SCHEMA + '.' + c.TABLE_NAME) 
                AND sc.name = c.COLUMN_NAME
            LEFT JOIN sys.extended_properties ep ON ep.major_id = sc.object_id 
                AND ep.minor_id = sc.column_id AND ep.name = 'MS_Description'
            WHERE c.TABLE_NAME = @tableName 
                AND c.TABLE_SCHEMA = @schemaName
            ORDER BY c.ORDINAL_POSITION";

        var fields = new List<FieldDefinition>();

        using var command = new SqlCommand(sql, connection);
        command.Parameters.AddWithValue("@tableName", tableName);
        command.Parameters.AddWithValue("@schemaName", schemaName ?? "dbo");

        using var reader = await command.ExecuteReaderAsync();

        while (await reader.ReadAsync())
        {
            var field = new FieldDefinition
            {
                Name = reader.GetString("ColumnName"),
                SqlDataType = reader.GetString("DataType"),
                IsNullable = reader.GetString("IsNullable") == "YES",
                IsIdentity = reader.GetInt32("IsIdentity") == 1,
                Description = reader.IsDBNull("Description") ? null : reader.GetString("Description")
            };

            field.DisplayName = FormatDisplayName(field.Name);
            field.DataType = MapSqlTypeToFieldDataType(field.SqlDataType);
            field.DotNetType = MapSqlTypeToDotNetType(field.SqlDataType, field.IsNullable);

            // Set length, precision, scale
            if (!reader.IsDBNull("MaxLength"))
                field.MaxLength = reader.GetInt32("MaxLength");

            if (!reader.IsDBNull("Precision"))
                field.Precision = reader.GetByte("Precision");

            if (!reader.IsDBNull("Scale"))
                field.Scale = reader.GetByte("Scale");

            // Parse default value
            if (!reader.IsDBNull("DefaultValue"))
            {
                var defaultValue = reader.GetString("DefaultValue");
                field.DefaultValue = ParseDefaultValue(defaultValue, field.DataType);
            }

            fields.Add(field);
        }

        return fields;
    }

    private async Task<List<string>> GetPrimaryKeysAsync(SqlConnection connection, string tableName, string? schemaName)
    {
        const string sql = @"
            SELECT c.COLUMN_NAME
            FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
            JOIN INFORMATION_SCHEMA.CONSTRAINT_COLUMN_USAGE ccu ON tc.CONSTRAINT_NAME = ccu.CONSTRAINT_NAME
            JOIN INFORMATION_SCHEMA.COLUMNS c ON ccu.COLUMN_NAME = c.COLUMN_NAME 
                AND ccu.TABLE_NAME = c.TABLE_NAME
            WHERE tc.CONSTRAINT_TYPE = 'PRIMARY KEY'
                AND tc.TABLE_NAME = @tableName
                AND tc.TABLE_SCHEMA = @schemaName
            ORDER BY c.ORDINAL_POSITION";

        var primaryKeys = new List<string>();

        using var command = new SqlCommand(sql, connection);
        command.Parameters.AddWithValue("@tableName", tableName);
        command.Parameters.AddWithValue("@schemaName", schemaName ?? "dbo");

        using var reader = await command.ExecuteReaderAsync();

        while (await reader.ReadAsync())
        {
            primaryKeys.Add(reader.GetString("COLUMN_NAME"));
        }

        return primaryKeys;
    }

    private async Task<List<ForeignKeyDefinition>> GetForeignKeysAsync(SqlConnection connection, string tableName, string? schemaName)
    {
        const string sql = @"
            SELECT 
                fk.name as ForeignKeyName,
                OBJECT_SCHEMA_NAME(fk.referenced_object_id) as ReferencedSchema,
                OBJECT_NAME(fk.referenced_object_id) as ReferencedTable,
                COL_NAME(fkc.parent_object_id, fkc.parent_column_id) as LocalColumn,
                COL_NAME(fkc.referenced_object_id, fkc.referenced_column_id) as ReferencedColumn,
                fk.delete_referential_action_desc as DeleteAction,
                fk.update_referential_action_desc as UpdateAction
            FROM sys.foreign_keys fk
            JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
            WHERE OBJECT_SCHEMA_NAME(fk.parent_object_id) = @schemaName
                AND OBJECT_NAME(fk.parent_object_id) = @tableName
            ORDER BY fk.name, fkc.constraint_column_id";

        var foreignKeys = new Dictionary<string, ForeignKeyDefinition>();

        using var command = new SqlCommand(sql, connection);
        command.Parameters.AddWithValue("@tableName", tableName);
        command.Parameters.AddWithValue("@schemaName", schemaName ?? "dbo");

        using var reader = await command.ExecuteReaderAsync();

        while (await reader.ReadAsync())
        {
            var fkName = reader.GetString("ForeignKeyName");
            
            if (!foreignKeys.ContainsKey(fkName))
            {
                foreignKeys[fkName] = new ForeignKeyDefinition
                {
                    Name = fkName,
                    ReferencedTable = reader.GetString("ReferencedTable"),
                    OnDelete = MapForeignKeyAction(reader.GetString("DeleteAction")),
                    OnUpdate = MapForeignKeyAction(reader.GetString("UpdateAction"))
                };
            }

            var fk = foreignKeys[fkName];
            fk.LocalFields.Add(reader.GetString("LocalColumn"));
            fk.ReferencedFields.Add(reader.GetString("ReferencedColumn"));
        }

        return foreignKeys.Values.ToList();
    }

    private async Task<List<IndexDefinition>> GetIndexesAsync(SqlConnection connection, string tableName, string? schemaName)
    {
        const string sql = @"
            SELECT 
                i.name as IndexName,
                i.is_unique as IsUnique,
                i.type_desc as IndexType,
                c.name as ColumnName
            FROM sys.indexes i
            JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
            JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
            WHERE OBJECT_SCHEMA_NAME(i.object_id) = @schemaName
                AND OBJECT_NAME(i.object_id) = @tableName
                AND i.type > 0  -- Exclude heaps
            ORDER BY i.name, ic.key_ordinal";

        var indexes = new Dictionary<string, IndexDefinition>();

        using var command = new SqlCommand(sql, connection);
        command.Parameters.AddWithValue("@tableName", tableName);
        command.Parameters.AddWithValue("@schemaName", schemaName ?? "dbo");

        using var reader = await command.ExecuteReaderAsync();

        while (await reader.ReadAsync())
        {
            var indexName = reader.GetString("IndexName");
            
            if (!indexes.ContainsKey(indexName))
            {
                indexes[indexName] = new IndexDefinition
                {
                    Name = indexName,
                    IsUnique = reader.GetBoolean("IsUnique"),
                    IsClustered = reader.GetString("IndexType") == "CLUSTERED"
                };
            }

            var index = indexes[indexName];
            index.Fields.Add(reader.GetString("ColumnName"));
        }

        return indexes.Values.ToList();
    }

    private FieldDataType MapSqlTypeToFieldDataType(string sqlType)
    {
        return sqlType.ToLower() switch
        {
            "varchar" or "nvarchar" or "char" or "nchar" or "text" or "ntext" => FieldDataType.String,
            "int" or "smallint" or "tinyint" => FieldDataType.Integer,
            "bigint" => FieldDataType.Long,
            "decimal" or "numeric" or "money" or "smallmoney" => FieldDataType.Decimal,
            "float" or "real" => FieldDataType.Double,
            "bit" => FieldDataType.Boolean,
            "datetime" or "datetime2" or "smalldatetime" => FieldDataType.DateTime,
            "date" => FieldDataType.Date,
            "time" => FieldDataType.Time,
            "uniqueidentifier" => FieldDataType.Guid,
            "varbinary" or "binary" or "image" => FieldDataType.Binary,
            _ => FieldDataType.String
        };
    }

    private string MapSqlTypeToDotNetType(string sqlType, bool isNullable)
    {
        var baseType = sqlType.ToLower() switch
        {
            "varchar" or "nvarchar" or "char" or "nchar" or "text" or "ntext" => "string",
            "int" => "int",
            "smallint" => "short",
            "tinyint" => "byte",
            "bigint" => "long",
            "decimal" or "numeric" or "money" or "smallmoney" => "decimal",
            "float" => "double",
            "real" => "float",
            "bit" => "bool",
            "datetime" or "datetime2" or "smalldatetime" or "date" or "time" => "DateTime",
            "uniqueidentifier" => "Guid",
            "varbinary" or "binary" or "image" => "byte[]",
            _ => "string"
        };

        // Add nullable suffix for value types
        if (isNullable && baseType != "string" && baseType != "byte[]")
        {
            baseType += "?";
        }

        return baseType;
    }

    private ForeignKeyAction MapForeignKeyAction(string action)
    {
        return action switch
        {
            "CASCADE" => ForeignKeyAction.Cascade,
            "SET_NULL" => ForeignKeyAction.SetNull,
            "SET_DEFAULT" => ForeignKeyAction.SetDefault,
            "RESTRICT" => ForeignKeyAction.Restrict,
            _ => ForeignKeyAction.NoAction
        };
    }

    private object? ParseDefaultValue(string defaultValue, FieldDataType dataType)
    {
        // Remove SQL Server default value syntax
        defaultValue = defaultValue.Trim('(', ')', '\'');

        if (string.IsNullOrEmpty(defaultValue) || defaultValue.ToUpper() == "NULL")
            return null;

        try
        {
            return dataType switch
            {
                FieldDataType.String => defaultValue,
                FieldDataType.Integer => int.Parse(defaultValue),
                FieldDataType.Long => long.Parse(defaultValue),
                FieldDataType.Decimal => decimal.Parse(defaultValue),
                FieldDataType.Double => double.Parse(defaultValue),
                FieldDataType.Boolean => defaultValue == "1" || bool.Parse(defaultValue),
                FieldDataType.DateTime => DateTime.Parse(defaultValue),
                FieldDataType.Date => DateTime.Parse(defaultValue).Date,
                FieldDataType.Guid => Guid.Parse(defaultValue),
                _ => defaultValue
            };
        }
        catch
        {
            return null;
        }
    }

    private string FormatDisplayName(string name)
    {
        // Convert PascalCase or snake_case to display name
        if (name.Contains('_'))
        {
            return string.Join(" ", name.Split('_').Select(word => 
                char.ToUpper(word[0]) + word.Substring(1).ToLower()));
        }

        // Insert spaces before capital letters in PascalCase
        var result = "";
        for (int i = 0; i < name.Length; i++)
        {
            if (i > 0 && char.IsUpper(name[i]))
                result += " ";
            result += name[i];
        }

        return result;
    }
}
