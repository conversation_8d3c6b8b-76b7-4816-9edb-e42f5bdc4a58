@page
@model ConfigurationIndexModel
@using Crudible.Models
@{
    ViewData["Title"] = "Configuration";
    ViewData["Breadcrumbs"] = new List<BreadcrumbItem>
    {
        new BreadcrumbItem("Configuration", "/config", true, "fas fa-cog")
    };
}

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-cog me-2"></i>
                Configuration
            </h1>
            <div>
                <button type="button" class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#helpModal">
                    <i class="fas fa-question-circle me-1"></i>
                    Help
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Configuration Tabs -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <ul class="nav nav-tabs card-header-tabs" id="configTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="database-tab" data-bs-toggle="tab" data-bs-target="#database" 
                                type="button" role="tab" aria-controls="database" aria-selected="true">
                            <i class="fas fa-database me-1"></i>
                            Database
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="schema-tab" data-bs-toggle="tab" data-bs-target="#schema" 
                                type="button" role="tab" aria-controls="schema" aria-selected="false">
                            <i class="fas fa-file-code me-1"></i>
                            Schema
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="ui-tab" data-bs-toggle="tab" data-bs-target="#ui" 
                                type="button" role="tab" aria-controls="ui" aria-selected="false">
                            <i class="fas fa-palette me-1"></i>
                            UI Settings
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="audit-tab" data-bs-toggle="tab" data-bs-target="#audit" 
                                type="button" role="tab" aria-controls="audit" aria-selected="false">
                            <i class="fas fa-history me-1"></i>
                            Audit & Export
                        </button>
                    </li>
                </ul>
            </div>
            
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    <div class="tab-content" id="configTabContent">
                        <!-- Database Configuration -->
                        <div class="tab-pane fade show active" id="database" role="tabpanel" aria-labelledby="database-tab">
                            <h5 class="mb-3">Database Connection</h5>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="Configuration.Database.Provider" class="form-label">Database Provider</label>
                                        <select asp-for="Configuration.Database.Provider" class="form-select">
                                            <option value="SqlServer">SQL Server</option>
                                            <option value="MySQL">MySQL</option>
                                            <option value="PostgreSQL">PostgreSQL</option>
                                            <option value="SQLite">SQLite</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="Configuration.Database.CommandTimeout" class="form-label">Command Timeout (seconds)</label>
                                        <input asp-for="Configuration.Database.CommandTimeout" type="number" class="form-control" min="1" max="300">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label asp-for="Configuration.Database.ConnectionString" class="form-label">Connection String</label>
                                <textarea asp-for="Configuration.Database.ConnectionString" class="form-control" rows="3" 
                                          placeholder="Server=localhost;Database=MyDatabase;Trusted_Connection=true;"></textarea>
                                <div class="form-text">
                                    Enter your database connection string. 
                                    <a href="#" data-bs-toggle="modal" data-bs-target="#connectionStringModal">Need help?</a>
                                </div>
                            </div>
                            
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-outline-primary" id="testConnectionBtn">
                                    <i class="fas fa-plug me-1"></i>
                                    Test Connection
                                </button>
                                <div id="connectionStatus" class="align-self-center"></div>
                            </div>
                        </div>

                        <!-- Schema Configuration -->
                        <div class="tab-pane fade" id="schema" role="tabpanel" aria-labelledby="schema-tab">
                            <h5 class="mb-3">Schema Configuration</h5>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">Upload XML Schema</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <input type="file" name="schemaFile" class="form-control" accept=".xml,.xsd">
                                                <div class="form-text">Upload an XML schema file describing your database structure.</div>
                                            </div>
                                            <button type="button" class="btn btn-primary" id="uploadSchemaBtn">
                                                <i class="fas fa-upload me-1"></i>
                                                Upload Schema
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">Extract from Database</h6>
                                        </div>
                                        <div class="card-body">
                                            <p class="text-muted">Extract schema directly from your database connection.</p>
                                            <button type="button" class="btn btn-success" id="extractSchemaBtn">
                                                <i class="fas fa-database me-1"></i>
                                                Extract Schema
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <h6>Table Selection</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <label class="form-label">Included Tables</label>
                                        <select multiple class="form-select" size="8" id="includedTables">
                                            @if (Model.AvailableTables?.Any() == true)
                                            {
                                                @foreach (var table in Model.AvailableTables)
                                                {
                                                    <option value="@table">@table</option>
                                                }
                                            }
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Excluded Tables</label>
                                        <select multiple class="form-select" size="8" id="excludedTables">
                                            <!-- Excluded tables will be populated here -->
                                        </select>
                                    </div>
                                </div>
                                <div class="text-center mt-3">
                                    <button type="button" class="btn btn-outline-primary" id="includeAllBtn">Include All</button>
                                    <button type="button" class="btn btn-outline-secondary" id="excludeAllBtn">Exclude All</button>
                                </div>
                            </div>
                        </div>

                        <!-- UI Configuration -->
                        <div class="tab-pane fade" id="ui" role="tabpanel" aria-labelledby="ui-tab">
                            <h5 class="mb-3">UI Settings</h5>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="Configuration.UI.ApplicationTitle" class="form-label">Application Title</label>
                                        <input asp-for="Configuration.UI.ApplicationTitle" type="text" class="form-control">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label asp-for="Configuration.UI.ItemsPerPage" class="form-label">Items Per Page</label>
                                        <select asp-for="Configuration.UI.ItemsPerPage" class="form-select">
                                            <option value="10">10</option>
                                            <option value="25">25</option>
                                            <option value="50">50</option>
                                            <option value="100">100</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label asp-for="Configuration.UI.BootstrapTheme" class="form-label">Bootstrap Theme</label>
                                        <select asp-for="Configuration.UI.BootstrapTheme" class="form-select">
                                            <option value="default">Default</option>
                                            <option value="dark">Dark</option>
                                            <option value="light">Light</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check mb-3">
                                        <input asp-for="Configuration.UI.EnableResponsiveDesign" type="checkbox" class="form-check-input">
                                        <label asp-for="Configuration.UI.EnableResponsiveDesign" class="form-check-label">
                                            Enable Responsive Design
                                        </label>
                                    </div>
                                    
                                    <div class="form-check mb-3">
                                        <input asp-for="Configuration.UI.ShowAuditInfo" type="checkbox" class="form-check-input">
                                        <label asp-for="Configuration.UI.ShowAuditInfo" class="form-check-label">
                                            Show Audit Information
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Audit & Export Configuration -->
                        <div class="tab-pane fade" id="audit" role="tabpanel" aria-labelledby="audit-tab">
                            <h5 class="mb-3">Audit Trail Settings</h5>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check mb-3">
                                        <input asp-for="Configuration.Audit.EnableAuditTrail" type="checkbox" class="form-check-input">
                                        <label asp-for="Configuration.Audit.EnableAuditTrail" class="form-check-label">
                                            Enable Audit Trail
                                        </label>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label asp-for="Configuration.Audit.RetentionDays" class="form-label">Retention Days</label>
                                        <input asp-for="Configuration.Audit.RetentionDays" type="number" class="form-control" min="0">
                                        <div class="form-text">Set to 0 for unlimited retention</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check mb-3">
                                        <input asp-for="Configuration.Audit.LogCreateOperations" type="checkbox" class="form-check-input">
                                        <label asp-for="Configuration.Audit.LogCreateOperations" class="form-check-label">
                                            Log Create Operations
                                        </label>
                                    </div>
                                    
                                    <div class="form-check mb-3">
                                        <input asp-for="Configuration.Audit.LogUpdateOperations" type="checkbox" class="form-check-input">
                                        <label asp-for="Configuration.Audit.LogUpdateOperations" class="form-check-label">
                                            Log Update Operations
                                        </label>
                                    </div>
                                    
                                    <div class="form-check mb-3">
                                        <input asp-for="Configuration.Audit.LogDeleteOperations" type="checkbox" class="form-check-input">
                                        <label asp-for="Configuration.Audit.LogDeleteOperations" class="form-check-label">
                                            Log Delete Operations
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <h5 class="mb-3 mt-4">Export Settings</h5>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check mb-3">
                                        <input asp-for="Configuration.Export.EnableExcelExport" type="checkbox" class="form-check-input">
                                        <label asp-for="Configuration.Export.EnableExcelExport" class="form-check-label">
                                            Enable Excel Export
                                        </label>
                                    </div>
                                    
                                    <div class="form-check mb-3">
                                        <input asp-for="Configuration.Export.EnablePdfExport" type="checkbox" class="form-check-input">
                                        <label asp-for="Configuration.Export.EnablePdfExport" class="form-check-label">
                                            Enable PDF Export
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="Configuration.Export.MaxExportRecords" class="form-label">Max Export Records</label>
                                        <input asp-for="Configuration.Export.MaxExportRecords" type="number" class="form-control" min="1">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label asp-for="Configuration.Export.FileNameTemplate" class="form-label">File Name Template</label>
                                        <input asp-for="Configuration.Export.FileNameTemplate" type="text" class="form-control">
                                        <div class="form-text">Use {TableName} and {DateTime:format} placeholders</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between mt-4 pt-3 border-top">
                        <div>
                            <button type="button" class="btn btn-outline-secondary" id="resetConfigBtn">
                                <i class="fas fa-undo me-1"></i>
                                Reset to Defaults
                            </button>
                        </div>
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                Save Configuration
                            </button>
                            <button type="button" class="btn btn-success ms-2" id="generateCrudBtn">
                                <i class="fas fa-magic me-1"></i>
                                Generate CRUD Pages
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Help Modal -->
<div class="modal fade" id="helpModal" tabindex="-1" aria-labelledby="helpModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="helpModalLabel">Configuration Help</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <h6>Database Configuration</h6>
                <p>Configure your database connection to enable schema extraction and CRUD generation.</p>
                
                <h6>Schema Upload</h6>
                <p>Upload an XML schema file or extract schema directly from your database connection.</p>
                
                <h6>Table Selection</h6>
                <p>Choose which tables to include in your CRUD application. You can include or exclude specific tables.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Configuration page JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // Test connection button
            document.getElementById('testConnectionBtn').addEventListener('click', async function() {
                const connectionString = document.querySelector('[name="Configuration.Database.ConnectionString"]').value;
                if (!connectionString) {
                    alert('Please enter a connection string first.');
                    return;
                }
                
                this.disabled = true;
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Testing...';
                
                try {
                    const response = await fetch('/api/database/test-connection', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ connectionString: connectionString })
                    });
                    
                    const result = await response.json();
                    const statusDiv = document.getElementById('connectionStatus');
                    
                    if (result.success) {
                        statusDiv.innerHTML = '<span class="text-success"><i class="fas fa-check-circle me-1"></i>Connection successful</span>';
                    } else {
                        statusDiv.innerHTML = '<span class="text-danger"><i class="fas fa-times-circle me-1"></i>Connection failed</span>';
                    }
                } catch (error) {
                    document.getElementById('connectionStatus').innerHTML = '<span class="text-danger"><i class="fas fa-times-circle me-1"></i>Connection failed</span>';
                } finally {
                    this.disabled = false;
                    this.innerHTML = '<i class="fas fa-plug me-1"></i> Test Connection';
                }
            });
            
            // Upload schema button
            document.getElementById('uploadSchemaBtn').addEventListener('click', async function() {
                const fileInput = document.querySelector('input[name="schemaFile"]');
                if (!fileInput.files[0]) {
                    alert('Please select a schema file first.');
                    return;
                }

                const formData = new FormData();
                formData.append('schemaFile', fileInput.files[0]);

                this.disabled = true;
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Uploading...';

                try {
                    const response = await fetch('/config?handler=UploadSchema', {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                        }
                    });

                    if (response.ok) {
                        location.reload();
                    } else {
                        alert('Upload failed');
                    }
                } catch (error) {
                    alert('Upload failed: ' + error.message);
                } finally {
                    this.disabled = false;
                    this.innerHTML = '<i class="fas fa-upload me-1"></i> Upload Schema';
                }
            });

            // Generate CRUD button
            document.getElementById('generateCrudBtn').addEventListener('click', function() {
                if (confirm('This will generate CRUD pages for all selected tables. Continue?')) {
                    window.location.href = '/tables';
                }
            });
        });
    </script>
}
