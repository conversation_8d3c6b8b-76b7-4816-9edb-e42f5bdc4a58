using Microsoft.AspNetCore.Mvc;
using Crudible.Services;

namespace Crudible.Controllers;

[ApiController]
[Route("api")]
public class ApiController : ControllerBase
{
    private readonly TableDataService _tableDataService;
    private readonly ILogger<ApiController> _logger;

    public ApiController(TableDataService tableDataService, ILogger<ApiController> logger)
    {
        _tableDataService = tableDataService;
        _logger = logger;
    }

    [HttpGet("tables/{tableName}/data")]
    public async Task<IActionResult> GetTableData(
        string tableName,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 25,
        [FromQuery] string searchTerm = "")
    {
        try
        {
            _logger.LogInformation("API request for table data: {TableName}, page: {Page}, pageSize: {PageSize}", 
                tableName, page, pageSize);

            var (data, totalCount) = await _tableDataService.GetTableDataAsync(tableName, page, pageSize, searchTerm);

            return Ok(new
            {
                success = true,
                data = data,
                totalCount = totalCount,
                page = page,
                pageSize = pageSize,
                totalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting table data for {TableName}", tableName);
            return BadRequest(new
            {
                success = false,
                message = ex.Message
            });
        }
    }

    [HttpGet("tables/{tableName}/record/{id}")]
    public async Task<IActionResult> GetRecord(string tableName, string id)
    {
        try
        {
            _logger.LogInformation("API request for record: {TableName}, ID: {Id}", tableName, id);

            // For demo purposes, assume the primary key column is the table name + "Id"
            var idColumn = tableName.TrimEnd('s') + "Id";
            if (tableName.ToLower() == "categories") idColumn = "CategoryId";
            else if (tableName.ToLower() == "products") idColumn = "ProductId";
            else if (tableName.ToLower() == "customers") idColumn = "CustomerId";
            else if (tableName.ToLower() == "orders") idColumn = "OrderId";
            else if (tableName.ToLower() == "orderitems") idColumn = "OrderItemId";

            var record = await _tableDataService.GetRecordByIdAsync(tableName, idColumn, id);

            if (record != null)
            {
                return Ok(new
                {
                    success = true,
                    data = record
                });
            }
            else
            {
                return NotFound(new
                {
                    success = false,
                    message = "Record not found"
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting record for {TableName}, ID: {Id}", tableName, id);
            return BadRequest(new
            {
                success = false,
                message = ex.Message
            });
        }
    }

    [HttpPost("tables/{tableName}/record")]
    public async Task<IActionResult> CreateRecord(string tableName, [FromBody] Dictionary<string, object> data)
    {
        try
        {
            _logger.LogInformation("API request to create record in {TableName}", tableName);

            var success = await _tableDataService.InsertRecordAsync(tableName, data);

            if (success)
            {
                return Ok(new
                {
                    success = true,
                    message = "Record created successfully"
                });
            }
            else
            {
                return BadRequest(new
                {
                    success = false,
                    message = "Failed to create record"
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating record in {TableName}", tableName);
            return BadRequest(new
            {
                success = false,
                message = ex.Message
            });
        }
    }

    [HttpPut("tables/{tableName}/record/{id}")]
    public async Task<IActionResult> UpdateRecord(string tableName, string id, [FromBody] Dictionary<string, object> data)
    {
        try
        {
            _logger.LogInformation("API request to update record in {TableName}, ID: {Id}", tableName, id);

            // For demo purposes, assume the primary key column is the table name + "Id"
            var idColumn = tableName.TrimEnd('s') + "Id";
            if (tableName.ToLower() == "categories") idColumn = "CategoryId";
            else if (tableName.ToLower() == "products") idColumn = "ProductId";
            else if (tableName.ToLower() == "customers") idColumn = "CustomerId";
            else if (tableName.ToLower() == "orders") idColumn = "OrderId";
            else if (tableName.ToLower() == "orderitems") idColumn = "OrderItemId";

            var success = await _tableDataService.UpdateRecordAsync(tableName, idColumn, id, data);

            if (success)
            {
                return Ok(new
                {
                    success = true,
                    message = "Record updated successfully"
                });
            }
            else
            {
                return BadRequest(new
                {
                    success = false,
                    message = "Failed to update record"
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating record in {TableName}, ID: {Id}", tableName, id);
            return BadRequest(new
            {
                success = false,
                message = ex.Message
            });
        }
    }

    [HttpDelete("tables/{tableName}/record/{id}")]
    public async Task<IActionResult> DeleteRecord(string tableName, string id)
    {
        try
        {
            _logger.LogInformation("API request to delete record from {TableName}, ID: {Id}", tableName, id);

            // For demo purposes, assume the primary key column is the table name + "Id"
            var idColumn = tableName.TrimEnd('s') + "Id";
            if (tableName.ToLower() == "categories") idColumn = "CategoryId";
            else if (tableName.ToLower() == "products") idColumn = "ProductId";
            else if (tableName.ToLower() == "customers") idColumn = "CustomerId";
            else if (tableName.ToLower() == "orders") idColumn = "OrderId";
            else if (tableName.ToLower() == "orderitems") idColumn = "OrderItemId";

            var success = await _tableDataService.DeleteRecordAsync(tableName, idColumn, id);

            if (success)
            {
                return Ok(new
                {
                    success = true,
                    message = "Record deleted successfully"
                });
            }
            else
            {
                return BadRequest(new
                {
                    success = false,
                    message = "Failed to delete record"
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting record from {TableName}, ID: {Id}", tableName, id);
            return BadRequest(new
            {
                success = false,
                message = ex.Message
            });
        }
    }
}
