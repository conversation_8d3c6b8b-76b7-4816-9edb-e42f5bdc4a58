﻿@using Crudible.Models
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Crudible</title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom Styles -->
    <link rel="stylesheet" href="~/css/crudible-styles.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="~/favicon.ico">
</head>
<body class="d-flex flex-column min-vh-100">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow-sm">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" asp-page="/Index">
                <i class="fas fa-database me-2"></i>
                Crudible
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                    aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" asp-page="/Index">
                            <i class="fas fa-home me-1"></i>
                            Dashboard
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="tablesDropdown" role="button"
                           data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-table me-1"></i>
                            Tables
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="tablesDropdown">
                            <li><a class="dropdown-item" href="/tables">
                                <i class="fas fa-list me-2"></i>All Tables
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <!-- Dynamic table links will be added here -->
                            <li><a class="dropdown-item" href="/tables/categories">
                                <i class="fas fa-tags me-2"></i>Categories
                            </a></li>
                            <li><a class="dropdown-item" href="/tables/products">
                                <i class="fas fa-box me-2"></i>Products
                            </a></li>
                            <li><a class="dropdown-item" href="/tables/customers">
                                <i class="fas fa-users me-2"></i>Customers
                            </a></li>
                            <li><a class="dropdown-item" href="/tables/orders">
                                <i class="fas fa-shopping-cart me-2"></i>Orders
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/demo">
                            <i class="fas fa-play me-1"></i>
                            Demo
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/tables">
                            <i class="fas fa-table me-1"></i>
                            Tables
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-page="/Configuration/Index">
                            <i class="fas fa-cog me-1"></i>
                            Configuration
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/status">
                            <i class="fas fa-info-circle me-1"></i>
                            Status
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="toolsDropdown" role="button"
                           data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-tools me-1"></i>
                            Tools
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="toolsDropdown">
                            <li><a class="dropdown-item" href="/schema/upload">
                                <i class="fas fa-upload me-2"></i>Upload Schema
                            </a></li>
                            <li><a class="dropdown-item" href="/export">
                                <i class="fas fa-download me-2"></i>Export Data
                            </a></li>
                            <li><a class="dropdown-item" href="/audit">
                                <i class="fas fa-history me-2"></i>Audit Log
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/help">
                                <i class="fas fa-question-circle me-2"></i>Help
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    <!-- Breadcrumb -->
    @if (ViewData["ShowBreadcrumb"] as bool? != false)
    {
        <nav aria-label="breadcrumb" class="bg-light border-bottom">
            <div class="container-fluid">
                <ol class="breadcrumb mb-0 py-2">
                    <li class="breadcrumb-item">
                        <a href="/" class="text-decoration-none">
                            <i class="fas fa-home"></i> Home
                        </a>
                    </li>
                    @if (ViewData["Breadcrumbs"] != null)
                    {
                        var breadcrumbs = ViewData["Breadcrumbs"] as List<BreadcrumbItem>;
                        if (breadcrumbs != null)
                        {
                            @foreach (var breadcrumb in breadcrumbs)
                            {
                                @if (breadcrumb.IsActive)
                                {
                                    <li class="breadcrumb-item active" aria-current="page">
                                        @if (!string.IsNullOrEmpty(breadcrumb.Icon))
                                        {
                                            <i class="@breadcrumb.Icon me-1"></i>
                                        }
                                        @breadcrumb.Title
                                    </li>
                                }
                                else
                                {
                                    <li class="breadcrumb-item">
                                        <a href="@breadcrumb.Url" class="text-decoration-none">
                                            @if (!string.IsNullOrEmpty(breadcrumb.Icon))
                                            {
                                                <i class="@breadcrumb.Icon me-1"></i>
                                            }
                                            @breadcrumb.Title
                                        </a>
                                    </li>
                                }
                            }
                        }
                    }
                    else if (!string.IsNullOrEmpty(ViewData["Title"]?.ToString()))
                    {
                        <li class="breadcrumb-item active" aria-current="page">@ViewData["Title"]</li>
                    }
                </ol>
            </div>
        </nav>
    }

    <!-- Main Content -->
    <main class="flex-grow-1">
        <div class="container-fluid py-4">
            <!-- Alert Messages -->
            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }

            @if (TempData["ErrorMessage"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    @TempData["ErrorMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }

            @if (TempData["WarningMessage"] != null)
            {
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    @TempData["WarningMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }

            @if (TempData["InfoMessage"] != null)
            {
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <i class="fas fa-info-circle me-2"></i>
                    @TempData["InfoMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }

            @RenderBody()
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-auto">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="fw-bold">
                        <i class="fas fa-database me-2"></i>
                        Crudible - Enterprise CRUD Generator
                    </h6>
                    <p class="mb-0 text-muted">
                        Automatically generate complete CRUD applications from database schemas.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="mb-2">
                        <a href="/help" class="text-light text-decoration-none me-3">
                            <i class="fas fa-question-circle me-1"></i>Help
                        </a>
                        <a href="/about" class="text-light text-decoration-none me-3">
                            <i class="fas fa-info-circle me-1"></i>About
                        </a>
                        <a href="https://github.com/crudible" class="text-light text-decoration-none" target="_blank">
                            <i class="fab fa-github me-1"></i>GitHub
                        </a>
                    </div>
                    <small class="text-muted">
                        &copy; 2025 Crudible. Built with ASP.NET Core & Bootstrap 5.
                    </small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay d-none">
        <div class="text-center">
            <div class="loading-spinner"></div>
            <div class="mt-3 text-white">
                <h5>Loading...</h5>
                <p class="mb-0">Please wait while we process your request.</p>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/crudible-validation.js" asp-append-version="true"></script>
    <script src="~/js/crudible-app.js" asp-append-version="true"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>